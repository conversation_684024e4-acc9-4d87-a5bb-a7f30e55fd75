{"name": "kesma-cms", "version": "4.34.0", "license": "MIT", "repository": {"type": "git", "url": "https://gitlab.trendency.hu/kozponti-cms/kesma-cms"}, "contributors": [], "scripts": {"ng": "npx ng", "start": "npx ng serve", "start-with-proxy": "ng serve --proxy-config proxy.conf.js", "start:serve-ssr": "npx ng run kesma-cms:serve-ssr --port=4200", "start:ssr": "npx ng lint && npm run build:ssr && npm run serve:ssr", "start:ssr-dev": "npx ng lint && npm run build:ssr-dev && npm run serve:ssr", "start:ssr-beta": "npx ng lint && npm run build:ssr-beta && npm run serve:ssr", "build": "npx ng build --configuration production", "build:prod": "npx ng build --configuration production", "build:dev": "npm run build-ck-editor:dev && npx ng build --configuration=devsrv", "build:dev-php81": "npm run build-ck-editor:dev && npx ng build --configuration=devsrv-php81", "build:test-borsv2": "npm run build-ck-editor:dev && npx ng build --configuration=testsrv-borsv2", "build:beta": "npx ng build --configuration=beta", "build:teszt": "npx ng build --configuration=teszt", "build:test": "npx ng build --configuration=teszt", "lint": "npx ng lint kesma-cms", "format": "npx prettier --config ./.prettierrc \"src/**/*.{js,ts,html,scss}\" --write", "analyze": "npx ng build --configuration production --stats-json && webpack-bundle-analyzer dist/browser/stats.json", "build:client-and-server-bundles": "npm run build-ck-editor:prod && npx ng build --configuration production && npx ng run kesma-cms:server:production", "build:client-and-server-bundles-dev": "npm run build-ck-editor:dev && npx ng build --configuration=devsrv && npx ng run kesma-cms:server:devsrv", "build:client-and-server-bundles-beta": "npm run build-ck-editor:prod && npx ng build --configuration production --configuration=beta && npx ng run kesma-cms:server:beta", "build:ssr": "npm run build:client-and-server-bundles", "build:ssr-dev": "npm run build:client-and-server-bundles-dev", "build:ssr-beta": "npm run build:client-and-server-bundles-beta", "serve:ssr": "npx ng lint && node dist/server/main.js", "build-and-zip": "rm -rf build.tar.gz && npm run build:ssr && tar -czf build.tar.gz dist/ node_modules/ package.json package-lock.json ecosystem.config.json", "install-global": "npm install -g @compodoc/compodoc@1.1.10 ngrok@3.2.5 webpack-bundle-analyzer@3.3.2", "build-ck-editor:dev": "vite build --config ./multiroot-ck-editor/vite.config.mjs -m development", "build-ck-editor:prod": "vite build --config ./multiroot-ck-editor/vite.config.mjs", "doc": "npx compodoc -p src/tsconfig.app.json -n \"Kesma documentation\"", "prepare": "husky"}, "pre-commit": [], "private": true, "dependencies": {"@angular/animations": "^19.2.3", "@angular/cdk": "^19.2.3", "@angular/common": "^19.2.3", "@angular/compiler": "^19.2.3", "@angular/core": "^19.2.3", "@angular/elements": "^19.2.3", "@angular/forms": "^19.2.3", "@angular/google-maps": "^19.2.3", "@angular/platform-browser": "^19.2.3", "@angular/platform-browser-dynamic": "^19.2.3", "@angular/platform-server": "^19.2.3", "@angular/router": "^19.2.3", "@angular/ssr": "^19.2.5", "@ant-design/icons-angular": "^19.0.0", "@ctrl/ngx-emoji-mart": "^9.2.0", "@ng-select/ng-select": "^14.2.4", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@sentry/angular": "^9.9.0", "@sentry/tracing": "^7.120.3", "@tinymce/tinymce-angular": "7.0.0", "@trendency/kesma-core": "3.3.1", "@trendency/kesma-ui": "3.48.0", "@trendency/leaflet": "~1.9.5", "bootstrap": "^5.3.3", "ckeditor5": "44.1.0", "core-js": "3.22.7", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "diff": "5.1.0", "domino": "2.1.6", "express": "^4.18.2", "fast-deep-equal": "3.1.3", "flatpickr": "^4.6.13", "http-server": "14.1.0", "image-focus": "1.2.1", "lodash": "4.17.21", "ng-zorro-antd": "^19.1.0", "ng2-pdf-viewer": "^10.2.2", "ngx-captcha": "^13.0.0", "ngx-date-fns": "^12.0.0", "ngx-image-cropper": "~9.1.2", "patch-package": "6.4.7", "pdfjs-dist": "^4.4.168", "reflect-metadata": "0.1.13", "rxjs": "^7.8.2", "suncalc": "^1.9.0", "swiper": "^11.2.5", "uuid": "8.3.2", "zone.js": "~0.15.0", "chart.js": "^4.4.9"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.5", "@angular-eslint/builder": "^19.2.1", "@angular-eslint/eslint-plugin": "^19.2.1", "@angular-eslint/eslint-plugin-template": "^19.2.1", "@angular-eslint/schematics": "^19.2.1", "@angular-eslint/template-parser": "^19.2.1", "@angular/cli": "^19.2.5", "@angular/compiler-cli": "^19.2.3", "@angular/language-service": "^19.2.3", "@ckeditor/ckeditor5-inspector": "^4.1.0", "@compodoc/compodoc": "1.1.19", "@types/diff": "5.0.2", "@types/jquery": "3.5.14", "@types/lodash": "4.14.182", "@types/lodash-es": "^4.17.12", "@types/slick-carousel": "1.6.37", "@types/sortablejs": "^1.13.0", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "browser-sync": "^3.0.3", "eslint": "^8.57.0", "eslint-plugin-functional": "^6.2.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-rxjs": "^5.0.2", "husky": "^9.1.7", "lint-staged": "^15.4.3", "postcss": "^8.4.14", "postcss-loader": "^8.1.1", "pre-commit": "1.2.2", "prettier": "^3.5.3", "raw-loader": "^4.0.2", "style-loader": "^4.0.0", "ts-loader": "^9.5.1", "typescript": "~5.5.4", "vite": "^5.1.8", "vite-plugin-raw": "^1.0.3", "webpack": "^5.92.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4"}, "overrides": {"@ckeditor/ckeditor5-alignment": "44.1.0", "@ckeditor/ckeditor5-autosave": "44.1.0", "@ckeditor/ckeditor5-basic-styles": "44.1.0", "@ckeditor/ckeditor5-clipboard": "44.1.0", "@ckeditor/ckeditor5-core": "44.1.0", "@ckeditor/ckeditor5-engine": "44.1.0", "@ckeditor/ckeditor5-enter": "44.1.0", "@ckeditor/ckeditor5-essentials": "44.1.0", "@ckeditor/ckeditor5-find-and-replace": "44.1.0", "@ckeditor/ckeditor5-font": "44.1.0", "@ckeditor/ckeditor5-heading": "44.1.0", "@ckeditor/ckeditor5-horizontal-line": "44.1.0", "@ckeditor/ckeditor5-html-embed": "44.1.0", "@ckeditor/ckeditor5-image": "44.1.0", "@ckeditor/ckeditor5-link": "44.1.0", "@ckeditor/ckeditor5-list": "44.1.0", "@ckeditor/ckeditor5-media-embed": "44.1.0", "@ckeditor/ckeditor5-ui": "44.1.0", "@ckeditor/ckeditor5-undo": "44.1.0", "@ckeditor/ckeditor5-upload": "44.1.0", "@ckeditor/ckeditor5-utils": "44.1.0", "@ckeditor/ckeditor5-paragraph": "44.1.0", "@ckeditor/ckeditor5-paste-from-office": "44.1.0", "@ckeditor/ckeditor5-remove-format": "44.1.0", "@ckeditor/ckeditor5-select-all": "44.1.0", "@ckeditor/ckeditor5-special-characters": "44.1.0", "@ckeditor/ckeditor5-table": "44.1.0", "@ckeditor/ckeditor5-theme-lark": "44.1.0", "@ckeditor/ckeditor5-typing": "44.1.0", "@ckeditor/ckeditor5-widget": "44.1.0", "ckeditor5": "44.1.0"}, "engines": {"node": ">=18.10.0", "npm": ">=9.0.0"}, "browser": {"fs": false}}