// Deves környezet
// apiUrl:
//  - Ha <PERSON>z API al-domainen van (pl. https://api.site.hu/publicapi/...) akkor ABSZOLÚT elérési út (https://api.site.hu/publicapi)
//  - Ha az API a fődomain-en van (pl. https://site.hu/publicapi/...) akkor RELATÍV elérési út (/publicapi)
// translation:
//  - locales: az oldalon használt nyelvek listája
//  - prefix: a route fordítókulcsok prefixe
//  - localesFolder: a mappa elérési útja, ahol a fordításhoz használt {lang}.JSON fájlok találhatók.

import { IEnvironment } from './environment.definitions';

export const environment: IEnvironment = {
  production: false,
  projectName: 'Kesma CMS',
  type: 'dev',
  apiUrls: {
    api: 'https://kozponti-api.dev.trendency.hu',
    media: 'https://kozponti-media.dev.trendency.hu',
  },
  mindApiUrl: 'https://mind.data.trendency.dev',
  cdnUrl: '',
  domains: {
    vilaggazdasag: {
      title: 'Világgazdaság',
      previewUrl: 'https://vilaggazdasag.dev.trendency.hu',
      portalHeader: 'vilaggazdasag',
      logo: '/assets/images/domain-logos/vilaggazdasag-logo.png',
    },
    magyarNemzet: {
      title: 'Magyar Nemzet',
      previewUrl: 'https://magyar-nemzet.dev.trendency.hu',
      portalHeader: 'magyar_nemzet',
      logo: '/assets/images/domain-logos/magyar-nemzet-logo.png',
    },
    baon: {
      title: 'BAON (Bács-Kiskun)',
      previewUrl: 'https://bama.dev.trendency.hu',
      portalHeader: 'baon',
      logo: '/assets/images/domain-logos/baon-logo.svg',
      portalTitle: 'BAON',
    },
    bama: {
      title: 'BAMA (Baranya)',
      previewUrl: 'https://bama.dev.trendency.hu',
      portalHeader: 'bama',
      logo: '/assets/images/domain-logos/bama-logo.svg',
      portalTitle: 'BAMA',
    },
    beol: {
      title: 'BEOL (Békés)',
      previewUrl: 'https://beol.dev.trendency.hu',
      portalHeader: 'beol',
      logo: '/assets/images/domain-logos/beol-logo.svg',
      portalTitle: 'BEOL',
    },
    boon: {
      title: 'BOON (Borson-Abaúj-Zemplén)',
      previewUrl: 'https://bama.dev.trendency.hu',
      portalHeader: 'boon',
      logo: '/assets/images/domain-logos/boon-logo.svg',
      portalTitle: 'BOON',
    },
    city7: {
      title: 'CITY7',
      previewUrl: 'https://bama.dev.trendency.hu',
      portalHeader: 'city7',
      logo: '/assets/images/domain-logos/city-7-logo.png',
    },
    delmagyar: {
      title: 'Delmagyar (Csongrád)',
      previewUrl: 'https://bama.dev.trendency.hu',
      portalHeader: 'delmagyar',
      logo: '/assets/images/domain-logos/delmagyar-logo.svg',
      portalTitle: 'DELMAGYAR',
    },
    duol: {
      title: 'DUOL (Dunaújváros)',
      previewUrl: 'https://bama.dev.trendency.hu',
      portalHeader: 'duol',
      logo: '/assets/images/domain-logos/duol-logo.svg',
      portalTitle: 'DUOL',
    },
    erdon: {
      title: 'ERDON (Erdély)',
      previewUrl: 'https://bama.dev.trendency.hu',
      portalHeader: 'erdon',
      logo: '/assets/images/domain-logos/erdon-logo.svg',
      portalTitle: 'ERDON',
    },
    feol: {
      title: 'FEOL (Fejér)',
      previewUrl: 'https://feol.dev.trendency.hu',
      portalHeader: 'feol',
      logo: '/assets/images/domain-logos/feol-logo.svg',
      portalTitle: 'FEOL',
    },
    kisalfold: {
      title: 'Kisalfold (Győr-Moson-Sopron)',
      previewUrl: 'https://bama.dev.trendency.hu',
      portalHeader: 'kisalfold',
      logo: '/assets/images/domain-logos/kisalfold-logo.svg',
      portalTitle: 'KISALFOLD',
    },
    haon: {
      title: 'HAON (Hajdú-Bihar)',
      previewUrl: 'https://bama.dev.trendency.hu',
      portalHeader: 'haon',
      logo: '/assets/images/domain-logos/haon-logo.svg',
      portalTitle: 'HAON',
    },
    heol: {
      title: 'HEOL (Heves)',
      previewUrl: 'https://bama.dev.trendency.hu',
      portalHeader: 'heol',
      logo: '/assets/images/domain-logos/heol-logo.svg',
      portalTitle: 'HEOL',
    },
    szoljon: {
      title: 'Szoljon (Jász-Nagykun-Szolnok)',
      previewUrl: 'https://bama.dev.trendency.hu',
      portalHeader: 'szoljon',
      logo: '/assets/images/domain-logos/szoljon-logo.svg',
      portalTitle: 'SZOLJON',
    },
    kemma: {
      title: 'KEMMA (Komárom-Esztergom)',
      previewUrl: 'https://bama.dev.trendency.hu',
      portalHeader: 'kemma',
      logo: '/assets/images/domain-logos/kemma-logo.svg',
      portalTitle: 'KEMMA',
    },
    nool: {
      title: 'NOOL (Nógrád)',
      previewUrl: 'https://bama.dev.trendency.hu',
      portalHeader: 'nool',
      logo: '/assets/images/domain-logos/nool-logo.svg',
      portalTitle: 'NOOL',
    },
    sonline: {
      title: 'Sonline (Somogy)',
      previewUrl: 'https://bama.dev.trendency.hu',
      portalHeader: 'sonline',
      logo: '/assets/images/domain-logos/sonline-logo.svg',
      portalTitle: 'SONLINE',
    },
    szon: {
      title: 'SZON (Szabolcs-Szatmár-Bereg)',
      previewUrl: 'https://bama.dev.trendency.hu',
      portalHeader: 'szon',
      logo: '/assets/images/domain-logos/szon-logo.svg',
      portalTitle: 'SZON',
    },
    teol: {
      title: 'TEOL (Tolna)',
      previewUrl: 'https://bama.dev.trendency.hu',
      portalHeader: 'teol',
      logo: '/assets/images/domain-logos/teol-logo.svg',
      portalTitle: 'TEOL',
    },
    vaol: {
      title: 'VAOL (Vas)',
      previewUrl: 'https://bama.dev.trendency.hu',
      portalHeader: 'vaol',
      logo: '/assets/images/domain-logos/vaol-logo.svg',
      portalTitle: 'VAOL',
    },
    veol: {
      title: 'VEOL (Veszprém)',
      previewUrl: 'https://veol.dev.trendency.hu',
      portalHeader: 'veol',
      logo: '/assets/images/domain-logos/veol-logo.svg',
      portalTitle: 'VEOL',
    },
    zaol: {
      title: 'ZAOL (Zala)',
      previewUrl: 'https://bama.dev.trendency.hu',
      portalHeader: 'zaol',
      logo: '/assets/images/domain-logos/zaol-logo.svg',
      portalTitle: 'ZAOL',
    },
    kpi: {
      title: 'Központi Szerkesztőség',
      previewUrl: 'https://bama.dev.trendency.hu',
      portalHeader: 'kpi',
      logo: '/assets/images/domain-logos/default-logo.png',
    },
    ripost: {
      title: 'Ripost',
      previewUrl: 'https://ripost.dev.trendency.hu',
      portalHeader: 'ripost',
      logo: '/assets/images/domain-logos/ripost-logo.png',
    },
    bors: {
      title: 'Bors',
      previewUrl: 'https://bors.dev.trendency.hu',
      portalHeader: 'bors',
      logo: '/assets/images/domain-logos/bors-logo.svg',
      portalTitle: 'BorsOnline',
    },
    nso: {
      title: 'Nemzeti sport',
      previewUrl: 'https://nemzetisport.dev.trendency.hu',
      portalHeader: 'nso',
      logo: '/assets/images/domain-logos/nso-logo.svg',
      portalTitle: 'Nemzeti Sport Online,',
    },
    mandiner: {
      title: 'Mandiner',
      previewUrl: 'https://mandiner.dev.trendency.hu',
      portalHeader: 'mandiner',
      logo: '/assets/images/domain-logos/mandiner-logo.svg',
      portalTitle: 'Mandiner.hu',
    },
    metropol: {
      title: 'Metropol',
      previewUrl: 'https://metropol.dev.trendency.hu',
      portalHeader: 'metropol',
      logo: '/assets/images/domain-logos/metropol-logo.jpg',
      googleMapsApiKey: 'AIzaSyBEDVJHcEt_qguNjaSh2qq6vTxcQgbBrRI',
    },
    gong_radio: {
      title: 'Gong Rádió',
      previewUrl: 'https://gongradio.dev.trendency.hu',
      portalHeader: 'gong_radio',
      logo: '/assets/images/domain-logos/gong-radio-logo.svg',
    },
    koponyeg: {
      title: 'Köpönyeg',
      previewUrl: 'https://koponyeg.dev.trendency.hu',
      portalHeader: 'koponyeg',
      logo: '/assets/images/domain-logos/koponyeg-logo.svg',
    },
    szabadfold: {
      title: 'Szabad Föld',
      previewUrl: 'https://szabadfold.dev.trendency.hu',
      portalHeader: 'szabadfold',
      logo: '/assets/images/domain-logos/szabadfold-logo.svg',
    },
    ingatlanbazar_magazin: {
      title: 'Ingatlanbazár magazin',
      previewUrl: 'https://ingatlanbazar.dev.trendency.hu/magazin',
      portalHeader: 'ingatlanbazar_magazin',
      logo: '/assets/images/domain-logos/ingatlanbazar.svg',
    },
    mindmegette: {
      title: 'Mindmegette.hu',
      previewUrl: 'https://mindmegette.dev.trendency.hu',
      portalHeader: 'mindmegette',
      logo: '/assets/images/domain-logos/mindmegette-logo.png',
    },
    szegedma: {
      title: 'Szegedma',
      previewUrl: 'https://szegedma.dev.trendency.hu',
      portalHeader: 'szegedma',
      logo: '/assets/images/domain-logos/szegedma-logo.png',
    },
    origo: {
      title: 'Origo',
      previewUrl: 'https://origo.dev.trendency.hu',
      portalHeader: 'origo',
      logo: '/assets/images/domain-logos/origo-logo.png',
    },
    she: {
      title: 'She',
      previewUrl: 'https://she.dev.trendency.hu',
      portalHeader: 'she',
      logo: '/assets/images/domain-logos/she-logo.png',
    },
    life: {
      title: 'Life',
      previewUrl: 'https://life.dev.trendency.hu',
      portalHeader: 'life',
      logo: '/assets/images/domain-logos/life-logo.png',
    },
    pesti_sracok: {
      title: 'PestiSrácok',
      previewUrl: 'https://pestisracok.dev.trendency.hu',
      portalHeader: 'pesti_sracok',
      logo: '/assets/images/domain-logos/pesti-sracok-logo.jpg',
    },
  },
  translation: {
    locales: ['hu'],
    prefix: 'ROUTES.',
    localesFolder: {
      client: '/assets/locales',
      server: '/../browser/assets/locales',
    },
  },
  sentry: {
    dsn: 'https://<EMAIL>/3',
    tracingOrigins: ['https://kozponti-api.dev.trendency.hu', 'https://kozponti-media.dev.trendency.hu'],
    sampleRate: 1.0,
  },
};
