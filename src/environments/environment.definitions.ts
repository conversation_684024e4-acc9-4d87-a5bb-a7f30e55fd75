import { DomainsObject } from 'src/app/core/modules/admin/admin.definitions';

export type EnvironmentType = 'local' | 'dev' | 'beta' | 'prod' | 'teszt';

// apiUrl:
// - Az az url amit a böngésző használ az API hívásokhoz böngésző és szerver oldalon
// vagy
//  apiUrl.clientApiUrl:
//    - Az az url amit a böngésző használ az API hívásokhoz
//  apiUrl.serverApiUrl:
//    - Az az url amit SSR-nél a Node használ az API hívásokhoz
// translation:
//  - locales: az oldalon használt nyelvek listája
//  - prefix: a route fordítókulcsok prefixe
//  - localesFolder:
//     - client: a mappa elérési útja böngésző oldalon, ahol a fordításhoz használt {lang}.JSON fájlok találhatók.
//     - server: a mappa elérési útja Node oldalon, ahol a fordításhoz használt {lang}.JSON fájlok találhatók.

export interface IEnvironment {
  production: boolean;
  type: EnvironmentType;
  projectName: string;
  apiUrls: ApiUrlCollection | ServerAndClientUrls<ApiUrlCollection>;
  cdnUrl: string | ServerAndClientUrls<string>;
  translation: IEnvironmentTranslationConfig;
  domains: DomainsObject;
  sentry: SentryConfig;
  useSubdomainPerPortal?: boolean;
  baseHost?: string;
  mindApiUrl?: string;
}

export interface IEnvironmentTranslationConfig {
  locales: string[];
  prefix: string;
  localesFolder: IEnvironmentLocalesFolder;
}

export interface ServerAndClientUrls<T extends string | ApiUrlCollection> {
  client: T;
  server: T;
}

export interface IEnvironmentLocalesFolder {
  client: string;
  server: string;
}

export interface ApiUrlCollection {
  api: string;
  media: string;
}

export type SentryConfig = Readonly<{
  dsn: string;
  tracingOrigins: string[];
  sampleRate: number;
}>;
