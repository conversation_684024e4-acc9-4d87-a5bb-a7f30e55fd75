// <PERSON><PERSON> k<PERSON>
// apiUrl:
//  - Ha <PERSON>z API al-domainen van (pl. https://api.site.hu/publicapi/...) akkor ABSZOLÚT elérési út (https://api.site.hu/publicapi)
//  - Ha az API a fődomain-en van (pl. https://site.hu/publicapi/...) akkor RELATÍV elérési út (/publicapi)
// translation:
//  - locales: az oldalon használt nyelvek listája
//  - prefix: a route fordítókulcsok prefixe
//  - localesFolder: a mappa elérési útja, ahol a fordításhoz használt {lang}.JSON fájlok találhatók.

import { IEnvironment } from './environment.definitions';

export const environment: IEnvironment = {
  production: true,
  projectName: 'Kesma CMS',
  type: 'prod',
  apiUrls: {
    api: 'https://cmsapi.app.content.private',
    media: 'https://cmsmediatar.app.content.private',
  },
  mindApiUrl: 'http://mind.prod.data.content.private',
  cdnUrl: '',
  useSubdomainPerPortal: true,
  baseHost: 'cms.app.content.private',
  domains: {
    vilaggazdasag: {
      title: 'Világgazdaság',
      previewUrl: 'https://www.vg.hu',
      portalHeader: 'vilaggazdasag',
      logo: '/assets/images/domain-logos/vilaggazdasag-logo.png',
      subdomain: 'vg',
      overrideApiUrl: 'https://vg.api.app.content.private',
    },
    magyarNemzet: {
      title: 'Magyar Nemzet',
      previewUrl: 'https://magyarnemzet.hu',
      portalHeader: 'magyar_nemzet',
      logo: '/assets/images/domain-logos/magyar-nemzet-logo.png',
      subdomain: 'mno',
      overrideApiUrl: 'https://mno.api.app.content.private',
    },
    kpi: {
      title: 'Központi Szerkesztőség',
      previewUrl: 'https://kpi.mediaworks.hu',
      portalHeader: 'kpi',
      logo: '/assets/images/domain-logos/default-logo.png',
    },
    //Megyei
    bama: {
      title: 'BAMA (Baranya)',
      previewUrl: 'https://www.bama.hu',
      portalHeader: 'bama',
      logo: '/assets/images/domain-logos/bama-logo.svg',
      portalTitle: 'BAMA',
    },
    baon: {
      title: 'BAON (Bács-Kiskun)',
      previewUrl: 'https://www.baon.hu',
      portalHeader: 'baon',
      logo: '/assets/images/domain-logos/baon-logo.svg',
      portalTitle: 'BAON',
    },
    beol: {
      title: 'BEOL (Békés)',
      previewUrl: 'https://www.beol.hu',
      portalHeader: 'beol',
      logo: '/assets/images/domain-logos/beol-logo.svg',
      portalTitle: 'BEOL',
    },
    boon: {
      title: 'BOON (Borsod-Abaúj-Zemplén)',
      previewUrl: 'https://www.boon.hu',
      portalHeader: 'boon',
      logo: '/assets/images/domain-logos/boon-logo.svg',
      portalTitle: 'BOON',
    },
    city7: {
      title: 'CITY7',
      previewUrl: 'https://new.city7.hu',
      portalHeader: 'city7',
      logo: '/assets/images/domain-logos/city-7-logo.png',
    },
    delmagyar: {
      title: 'DÉLMAGYAR (Csongrád-Csanád)',
      previewUrl: 'https://www.delmagyar.hu',
      portalHeader: 'delmagyar',
      logo: '/assets/images/domain-logos/delmagyar-logo.svg',
      portalTitle: 'DELMAGYAR',
    },
    duol: {
      title: 'DUOL (Dunaújváros)',
      previewUrl: 'https://www.duol.hu',
      portalHeader: 'duol',
      logo: '/assets/images/domain-logos/duol-logo.svg',
      portalTitle: 'DUOL',
    },
    erdon: {
      title: 'ERDON (Erdély)',
      previewUrl: 'https://www.erdon.ro',
      portalHeader: 'erdon',
      logo: '/assets/images/domain-logos/erdon-logo.svg',
      portalTitle: 'ERDON',
    },
    feol: {
      title: 'FEOL (Fejér)',
      previewUrl: 'https://www.feol.hu',
      portalHeader: 'feol',
      logo: '/assets/images/domain-logos/feol-logo.svg',
      portalTitle: 'FEOL',
    },
    haon: {
      title: 'HAON (Hajdú-Bihar)',
      previewUrl: 'https://www.haon.hu',
      portalHeader: 'haon',
      logo: '/assets/images/domain-logos/haon-logo.svg',
      portalTitle: 'HAON',
    },
    heol: {
      title: 'HEOL (Heves)',
      previewUrl: 'https://www.heol.hu',
      portalHeader: 'heol',
      logo: '/assets/images/domain-logos/heol-logo.svg',
      portalTitle: 'HEOL',
    },
    kemma: {
      title: 'KEMMA (Komárom-Esztergom)',
      previewUrl: 'https://www.kemma.hu',
      portalHeader: 'kemma',
      logo: '/assets/images/domain-logos/kemma-logo.svg',
      portalTitle: 'KEMMA',
    },
    kisalfold: {
      title: 'KISALFÖLD (Győr-Moson-Sopron)',
      previewUrl: 'https://www.kisalfold.hu',
      portalHeader: 'kisalfold',
      logo: '/assets/images/domain-logos/kisalfold-logo.svg',
      portalTitle: 'KISALFOLD',
    },
    nool: {
      title: 'NOOL (Nógrád)',
      previewUrl: 'https://www.nool.hu',
      portalHeader: 'nool',
      logo: '/assets/images/domain-logos/nool-logo.svg',
      portalTitle: 'NOOL',
    },
    sonline: {
      title: 'SONLINE (Somogy)',
      previewUrl: 'https://www.sonline.hu',
      portalHeader: 'sonline',
      logo: '/assets/images/domain-logos/sonline-logo.svg',
      portalTitle: 'SONLINE',
    },
    szoljon: {
      title: 'SZOLJON (Jász-Nagykun-Szolnok)',
      previewUrl: 'https://www.szoljon.hu',
      portalHeader: 'szoljon',
      logo: '/assets/images/domain-logos/szoljon-logo.svg',
      portalTitle: 'SZOLJON',
    },
    szon: {
      title: 'SZON (Szabolcs-Szatmár-Bereg)',
      previewUrl: 'https://www.szon.hu',
      portalHeader: 'szon',
      logo: '/assets/images/domain-logos/szon-logo.svg',
      portalTitle: 'SZON',
    },
    teol: {
      title: 'TEOL (Tolna)',
      previewUrl: 'https://www.teol.hu',
      portalHeader: 'teol',
      logo: '/assets/images/domain-logos/teol-logo.svg',
      portalTitle: 'TEOL',
    },
    vaol: {
      title: 'VAOL (Vas)',
      previewUrl: 'https://www.vaol.hu',
      portalHeader: 'vaol',
      logo: '/assets/images/domain-logos/vaol-logo.svg',
      portalTitle: 'VAOL',
    },
    veol: {
      title: 'VEOL (Veszprém)',
      previewUrl: 'https://www.veol.hu',
      portalHeader: 'veol',
      logo: '/assets/images/domain-logos/veol-logo.svg',
      portalTitle: 'VEOL',
    },
    zaol: {
      title: 'ZAOL (Zala)',
      previewUrl: 'https://www.zaol.hu',
      portalHeader: 'zaol',
      logo: '/assets/images/domain-logos/zaol-logo.svg',
      portalTitle: 'ZAOL',
    },
    bors: {
      title: 'Bors',
      previewUrl: 'https://www.borsonline.hu',
      portalHeader: 'bors',
      logo: '/assets/images/domain-logos/bors-logo.svg',
      portalTitle: 'Borsonline',
      subdomain: 'bors',
      overrideApiUrl: 'https://bors.api.app.content.private',
    },
    ripost: {
      title: 'Ripost',
      previewUrl: 'https://ripost.hu',
      portalHeader: 'ripost',
      logo: '/assets/images/domain-logos/ripost-logo.png',
      subdomain: 'ripost',
      overrideApiUrl: 'https://ripost.api.app.content.private',
    },
    metropol: {
      title: 'Metropol',
      previewUrl: 'https://metropol.hu',
      portalHeader: 'metropol',
      logo: '/assets/images/domain-logos/metropol-logo.jpg',
      subdomain: 'metropol',
      overrideApiUrl: 'https://metropol.api.app.content.private',
    },
    mandiner: {
      title: 'Mandiner',
      previewUrl: 'https://mandiner.hu',
      portalHeader: 'mandiner',
      logo: '/assets/images/domain-logos/mandiner-logo.svg',
      portalTitle: 'Mandiner.hu',
      subdomain: 'mandiner',
      overrideApiUrl: 'https://mandiner.api.app.content.private',
    },
    gong_radio: {
      title: 'Gong Rádió',
      previewUrl: 'https://radio1gong.hu',
      portalHeader: 'gong_radio',
      logo: '/assets/images/domain-logos/gong-radio-logo.svg',
      subdomain: 'gong',
      overrideApiUrl: 'https://gong.api.app.content.private',
    },
    koponyeg: {
      title: 'Köpönyeg',
      previewUrl: 'https://koponyeg.hu',
      portalHeader: 'koponyeg',
      logo: '/assets/images/domain-logos/koponyeg-logo.svg',
      subdomain: 'koponyeg',
      overrideApiUrl: 'https://koponyeg.api.app.content.private',
    },
    szabadfold: {
      title: 'Szabad Föld',
      previewUrl: 'https://szabadfold.hu',
      portalHeader: 'szabadfold',
      logo: '/assets/images/domain-logos/szabadfold-logo.svg',
      subdomain: 'szabadfold',
      overrideApiUrl: 'https://szabadfold.api.app.content.private',
    },
    nso: {
      title: 'Nemzeti Sport',
      previewUrl: 'https://www.nemzetisport.hu',
      portalHeader: 'nso',
      logo: '/assets/images/domain-logos/nso-logo.svg',
      subdomain: 'nso',
      overrideApiUrl: 'https://nso.api.app.content.private',
    },
    szegedma: {
      title: 'Szegedma',
      previewUrl: 'https://szegedma.hu',
      portalHeader: 'szegedma',
      logo: '/assets/images/domain-logos/szegedma-logo.png',
      subdomain: 'szegedma',
      overrideApiUrl: 'https://szegedma.api.app.content.private',
    },
    origo: {
      title: 'Origo',
      previewUrl: 'https://www.origo.hu',
      portalHeader: 'origo',
      logo: '/assets/images/domain-logos/origo-logo.png',
      subdomain: 'origo',
      overrideApiUrl: 'https://origo.api.app.content.private',
    },
    ingatlanbazar_magazin: {
      title: 'Ingatlanbazár magazin',
      previewUrl: 'https://ingatlanbazar.hu/magazin',
      portalHeader: 'ingatlanbazar_magazin',
      logo: '/assets/images/domain-logos/ingatlanbazar.svg',
      subdomain: 'ingatlanbazar',
      overrideApiUrl: 'https://ingatlanbazar.api.app.content.private',
    },
    mindmegette: {
      title: 'Mindmegette.hu',
      previewUrl: 'https://www.mindmegette.hu',
      portalHeader: 'mindmegette',
      logo: '/assets/images/domain-logos/mindmegette-logo.png',
      subdomain: 'mindmegette',
      overrideApiUrl: 'https://mindmegette.api.app.content.private',
    },
    she: {
      title: 'She',
      previewUrl: 'https://she.life.hu',
      portalHeader: 'she',
      logo: '/assets/images/domain-logos/she-logo.png',
      subdomain: 'she',
      overrideApiUrl: 'https://she.api.app.content.private',
    },
    life: {
      title: 'Life',
      previewUrl: 'https://www.life.hu',
      portalHeader: 'life',
      logo: '/assets/images/domain-logos/life-logo.png',
      subdomain: 'life',
      overrideApiUrl: 'https://life.api.app.content.private',
    },
    pesti_sracok: {
      title: 'PestiSrácok',
      previewUrl: 'https://pestisracok.hu',
      portalHeader: 'pesti_sracok',
      logo: '/assets/images/domain-logos/pesti-sracok-logo.jpg',
      subdomain: 'pesti_sracok',
      overrideApiUrl: 'https://pestisracok.api.app.content.private',
    },
  },
  translation: {
    locales: ['hu'],
    prefix: 'ROUTES.',
    localesFolder: {
      client: '/assets/locales',
      server: '/../browser/assets/locales',
    },
  },
  sentry: {
    dsn: 'https://<EMAIL>/5',
    tracingOrigins: [],
    sampleRate: 0.2,
  },
};
