// De<PERSON>, b<PERSON>ta körn<PERSON>zet
// apiUrl:
//  - Ha <PERSON>z API al-domainen van (pl. https://api.site.hu/publicapi/...) akkor ABSZOLÚT elérési út (https://api.site.hu/publicapi)
//  - Ha az API a fődomain-en van (pl. https://site.hu/publicapi/...) akkor RELATÍV elérési út (/publicapi)
// translation:
//  - locales: az oldalon használt nyelvek listája
//  - prefix: a route fordítókulcsok prefixe
//  - localesFolder: a mappa elérési útja, ahol a fordításhoz használt {lang}.JSON fájlok találhatók.

import { IEnvironment } from './environment.definitions';

// http://cmstest.content.private/

export const environment: IEnvironment = {
  production: true,
  projectName: 'Kesma CMS',
  type: 'teszt',
  apiUrls: {
    api: 'http://api-cmstest.content.private',
    media: 'http://kozponti-media-cmstest.content.private',
  },
  mindApiUrl: 'http://mind.prod.data.content.private',
  cdnUrl: '',
  domains: {
    vilaggazdasag: {
      title: 'Világgazdaság',
      previewUrl: 'http://vgfe.apptest.content.private',
      portalHeader: 'vilaggazdasag',
      logo: '/assets/images/domain-logos/vilaggazdasag-logo.png',
    },
    magyarNemzet: {
      title: 'Magyar Nemzet',
      previewUrl: 'http://mnofe.apptest.content.private',
      portalHeader: 'magyar_nemzet',
      logo: '/assets/images/domain-logos/magyar-nemzet-logo.png',
    },
    bama: {
      title: 'BAMA (Baranya)',
      previewUrl: 'http://bamafe.apptest.content.private',
      portalHeader: 'bama',
      logo: '/assets/images/domain-logos/bama-logo.svg',
      portalTitle: 'BAMA',
    },
    beol: {
      title: 'BEOL (Békés)',
      previewUrl: 'http://beolfe.apptest.content.private',
      portalHeader: 'beol',
      logo: '/assets/images/domain-logos/beol-logo.svg',
      portalTitle: 'BEOL',
    },
    kpi: {
      title: 'Központi Szerkesztőség',
      previewUrl: 'http://kpife.apptest.content.private',
      portalHeader: 'kpi',
      logo: '/assets/images/domain-logos/default-logo.png',
    },
    baon: {
      title: 'BAON (Bács-Kiskun)',
      previewUrl: 'http://baonfe.apptest.content.private',
      portalHeader: 'baon',
      logo: '/assets/images/domain-logos/baon-logo.svg',
      portalTitle: 'BAON',
    },
    boon: {
      title: 'BOON (Borson-Abaúj-Zemplén)',
      previewUrl: 'http://boonfe.apptest.content.private',
      portalHeader: 'boon',
      logo: '/assets/images/domain-logos/boon-logo.svg',
      portalTitle: 'BOON',
    },
    city7: {
      title: 'CITY7',
      previewUrl: 'http://city7fe.apptest.content.private',
      portalHeader: 'city7',
      logo: '/assets/images/domain-logos/city-7-logo.png',
    },
    delmagyar: {
      title: 'Delmagyar (Csongrád)',
      previewUrl: 'http://delmagyarfe.apptest.content.private',
      portalHeader: 'delmagyar',
      logo: '/assets/images/domain-logos/delmagyar-logo.svg',
      portalTitle: 'DELMAGYAR',
    },
    duol: {
      title: 'DUOL (Dunaújváros)',
      previewUrl: 'http://duolfe.apptest.content.private',
      portalHeader: 'duol',
      logo: '/assets/images/domain-logos/duol-logo.svg',
      portalTitle: 'DUOL',
    },
    erdon: {
      title: 'ERDON (Erdély)',
      previewUrl: 'http://erdonfe.apptest.content.private',
      portalHeader: 'erdon',
      logo: '/assets/images/domain-logos/erdon-logo.svg',
      portalTitle: 'ERDON',
    },
    feol: {
      title: 'FEOL (Fejér)',
      previewUrl: 'http://feolfe.apptest.content.private',
      portalHeader: 'feol',
      logo: '/assets/images/domain-logos/feol-logo.svg',
      portalTitle: 'FEOL',
    },
    kisalfold: {
      title: 'Kisalfold (Győr-Moson-Sopron)',
      previewUrl: 'http://kisalfoldfe.apptest.content.private',
      portalHeader: 'kisalfold',
      logo: '/assets/images/domain-logos/kisalfold-logo.svg',
      portalTitle: 'KISALFOLD',
    },
    haon: {
      title: 'HAON (Hajdú-Bihar)',
      previewUrl: 'http://haonfe.apptest.content.private',
      portalHeader: 'haon',
      logo: '/assets/images/domain-logos/haon-logo.svg',
      portalTitle: 'HAON',
    },
    heol: {
      title: 'HEOL (Heves)',
      previewUrl: 'http://heolfe.apptest.content.private',
      portalHeader: 'heol',
      logo: '/assets/images/domain-logos/heol-logo.svg',
      portalTitle: 'HEOL',
    },
    szoljon: {
      title: 'Szoljon (Jász-Nagykun-Szolnok)',
      previewUrl: 'http://szoljonfe.apptest.content.private',
      portalHeader: 'szoljon',
      logo: '/assets/images/domain-logos/szoljon-logo.svg',
      portalTitle: 'SZOLJON',
    },
    kemma: {
      title: 'KEMMA (Komárom-Esztergom)',
      previewUrl: 'http://kemmafe.apptest.content.private',
      portalHeader: 'kemma',
      logo: '/assets/images/domain-logos/kemma-logo.svg',
      portalTitle: 'KEMMA',
    },
    nool: {
      title: 'NOOL (Nógrád)',
      previewUrl: 'http://noolfe.apptest.content.private',
      portalHeader: 'nool',
      logo: '/assets/images/domain-logos/nool-logo.svg',
      portalTitle: 'NOOL',
    },
    sonline: {
      title: 'Sonline (Somogy)',
      previewUrl: 'http://sonlinefe.apptest.content.private',
      portalHeader: 'sonline',
      logo: '/assets/images/domain-logos/sonline-logo.svg',
      portalTitle: 'SONLINE',
    },
    szon: {
      title: 'SZON (Szabolcs-Szatmár-Bereg)',
      previewUrl: 'http://szonfe.apptest.content.private',
      portalHeader: 'szon',
      logo: '/assets/images/domain-logos/szon-logo.svg',
      portalTitle: 'SZON',
    },
    teol: {
      title: 'TEOL (Tolna)',
      previewUrl: 'http://teolfe.apptest.content.private',
      portalHeader: 'teol',
      logo: '/assets/images/domain-logos/teol-logo.svg',
      portalTitle: 'TEOL',
    },
    vaol: {
      title: 'VAOL (Vas)',
      previewUrl: 'http://vaolfe.apptest.content.private',
      portalHeader: 'vaol',
      logo: '/assets/images/domain-logos/vaol-logo.svg',
      portalTitle: 'VAOL',
    },
    veol: {
      title: 'VEOL (Veszprém)',
      previewUrl: 'http://veolfe.apptest.content.private',
      portalHeader: 'veol',
      logo: '/assets/images/domain-logos/veol-logo.svg',
      portalTitle: 'VEOL',
    },
    zaol: {
      title: 'ZAOL (Zala)',
      previewUrl: 'http://zaolfe.apptest.content.private',
      portalHeader: 'zaol',
      logo: '/assets/images/domain-logos/zaol-logo.svg',
      portalTitle: 'ZAOL',
    },
    ripost: {
      title: 'Ripost',
      previewUrl: 'http://ripostfe.apptest.content.private',
      portalHeader: 'ripost',
      logo: '/assets/images/domain-logos/ripost-logo.png',
    },
    bors: {
      title: 'Bors',
      previewUrl: 'http://borsfe.apptest.content.private',
      portalHeader: 'bors',
      logo: '/assets/images/domain-logos/bors-logo.svg',
      portalTitle: 'Borsonline',
    },
    mandiner: {
      title: 'Mandiner',
      previewUrl: 'http://mandinerfe.apptest.content.private',
      portalHeader: 'mandiner',
      logo: '/assets/images/domain-logos/mandiner-logo.svg',
      portalTitle: 'Mandiner.hu',
    },
    metropol: {
      title: 'Metropol',
      previewUrl: 'http://metropolfe.apptest.content.private',
      portalHeader: 'metropol',
      logo: '/assets/images/domain-logos/metropol-logo.jpg',
      googleMapsApiKey: 'AIzaSyBEDVJHcEt_qguNjaSh2qq6vTxcQgbBrRI',
    },
    gong_radio: {
      title: 'Gong Rádió',
      previewUrl: 'http://gongfe.apptest.content.private',
      portalHeader: 'gong_radio',
      logo: '/assets/images/domain-logos/gong-radio-logo.svg',
    },
    koponyeg: {
      title: 'Köpönyeg',
      previewUrl: 'http://koponyegfe.apptest.content.private',
      portalHeader: 'koponyeg',
      logo: '/assets/images/domain-logos/koponyeg-logo.svg',
    },
    szabadfold: {
      title: 'Szabad Föld',
      previewUrl: 'http://szabadfoldfe.apptest.content.private',
      portalHeader: 'szabadfold',
      logo: '/assets/images/domain-logos/szabadfold-logo.svg',
    },
    nso: {
      title: 'Nemzeti sport',
      previewUrl: 'http://nsofe.apptest.content.private',
      portalHeader: 'nso',
      logo: '/assets/images/domain-logos/nso-logo.svg',
    },
    szegedma: {
      title: 'Szegedma',
      previewUrl: 'http://szegedmafe.apptest.content.private',
      portalHeader: 'szegedma',
      logo: '/assets/images/domain-logos/szegedma-logo.png',
    },
    origo: {
      title: 'Origo',
      previewUrl: 'http://origofe.apptest.content.private',
      portalHeader: 'origo',
      logo: '/assets/images/domain-logos/origo-logo.png',
    },
    ingatlanbazar_magazin: {
      title: 'Ingatlanbazár magazin',
      previewUrl: 'http://ingatlanbazarfe.apptest.content.private',
      portalHeader: 'ingatlanbazar_magazin',
      logo: '/assets/images/domain-logos/ingatlanbazar.svg',
    },
    mindmegette: {
      title: 'Mindmegette.hu',
      previewUrl: 'http://mindmegettefe.apptest.content.private',
      portalHeader: 'mindmegette',
      logo: '/assets/images/domain-logos/mindmegette-logo.png',
    },
    she: {
      title: 'She',
      previewUrl: 'http://shefe.apptest.content.private',
      portalHeader: 'she',
      logo: '/assets/images/domain-logos/she-logo.png',
    },
    life: {
      title: 'Life',
      previewUrl: 'http://lifefe.apptest.content.private',
      portalHeader: 'life',
      logo: '/assets/images/domain-logos/life-logo.png',
    },
    pesti_sracok: {
      title: 'PestiSrácok',
      previewUrl: 'http://pestisracokfe.apptest.content.private',
      portalHeader: 'pesti_sracok',
      logo: '/assets/images/domain-logos/pesti-sracok-logo.jpg',
    },
  },
  translation: {
    locales: ['hu'],
    prefix: 'ROUTES.',
    localesFolder: {
      client: '/assets/locales',
      server: '/../browser/assets/locales',
    },
  },
  sentry: {
    dsn: '',
    tracingOrigins: [],
    sampleRate: 1.0,
  },
};
