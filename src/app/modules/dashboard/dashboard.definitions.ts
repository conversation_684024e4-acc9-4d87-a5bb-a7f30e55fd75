import { PortalConfigSetting } from '@shared/definitions/portal-config';
import { DASHBOARD_ITEM } from './api/dashboard.const';
import { DomainKey } from '../../core/modules/admin/admin.definitions';

export type Dashboard = Readonly<{
  rows: DashboardRow[];
}>;

export type DashboardRow = Readonly<{
  cols: DashboardBox[];
}>;

export type DashboardItem = (typeof DASHBOARD_ITEM)[keyof typeof DASHBOARD_ITEM];

export type DashboardBox = {
  title?: string;
  content: string | DashboardItem;
  site?: DomainKey | DomainKey[];
  requiredPortalConfig?: PortalConfigSetting | PortalConfigSetting[];
  requiresExternalContributors?: boolean;
  // Determines the width of the box as in bootstrap grid system. If omitted, 'col-3' will be used.
  width?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
};

/** Single record of the Google Realtime Table **/
export type GoogleRealtimeRecord = Readonly<{
  /** Page Title **/
  title: string;
  /** Page view count **/
  viewCount: number;
  /** Previous page view count
   *
   * If there is no previous data, or it cannot be identified, it will be null.
   */
  lastViewCount: number;
  /** Percentage difference between the number of views of the previous page and the current one.
   *
   * If there is no previous data, or it cannot be identified, it will be null.
   */
  lastViewCountPercentageDifference: number;
  /** Mobile views **/
  deviceMobileViewCount: number;
  /** Tablet views **/
  deviceTabletViewCount: number;
  /** Desktop Views **/
  deviceDesktopViewCount: number;
}>;
