{"rows": [{"cols": [{"content": "contact", "width": 6}, {"title": "Értesítések", "content": "notifications", "width": 6}]}, {"cols": [{"title": "Szerkesztői statisztika", "content": "author_statistics", "width": 12, "requiresExternalContributors": true, "requiredPortalConfig": "mind_login_is_enabled"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "content": "ranking_list", "width": 12, "requiresExternalContributors": true, "requiredPortalConfig": "mind_login_is_enabled"}, {"title": "Cikk lista", "content": "articles_list", "width": 12, "requiresExternalContributors": true, "requiredPortalConfig": "mind_login_is_enabled"}]}, {"cols": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content": "google_realtime_table", "requiredPortalConfig": "enable_google_realtime_page_views_data_load", "width": 12}, {"title": "Teszt box", "content": "Teszt box tartalma", "site": "vilaggazdasag", "width": 6}]}, {"cols": [{"title": "Google Drive", "content": "google_drive", "site": "mandiner"}, {"title": "Teszt box 2", "content": "Teszt box 2 tartalma"}, {"title": "Teszt box 3", "content": "Teszt box tartalma", "site": "vilaggazdasag"}, {"title": "Teszt box 4", "content": "Teszt box 4 tartalma"}, {"title": "Teszt box 5 Magyar Nemzet", "content": "Teszt box 5 tartalma", "site": "magyarNemzet"}, {"title": "Google Analitics", "content": "google_analytics"}]}, {"cols": [{"title": "Teszt box column 2", "content": "Teszt box tartalma"}]}, {"cols": []}]}