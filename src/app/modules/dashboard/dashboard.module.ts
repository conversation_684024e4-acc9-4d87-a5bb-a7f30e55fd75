import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

import { DashboardComponent } from 'src/app/modules/dashboard/components/dashboard/dashboard.component';
import { dashboardRoutes } from './dashboard.routing';

import { GoogleRealtimeTableComponent } from './components/google-realtime-table/google-realtime-table.component';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzTableModule } from 'ng-zorro-antd/table';
import { SharedModule } from '@shared/shared.module';
import { DashboardNotificationsComponent } from '@modules/dashboard/dashboard-modules/dashboard-notifications/dashboard-notifications.component';
import { SeoDashboardAuthorStatisticsComponent } from '@modules/seo-dashboard/components/seo-dashboard-author-statistics/seo-dashboard-author-statistics.component';
import { SeoDashboardRankingListComponent } from '@modules/seo-dashboard/components/seo-dashboard-ranking-list/seo-dashboard-ranking-list.component';
import { SeoDashboardArticlesListComponent } from '@modules/seo-dashboard/components/seo-dashboard-articles-list/seo-dashboard-articles-list.component';

@NgModule({
  declarations: [DashboardComponent, GoogleRealtimeTableComponent],
  imports: [
    RouterModule.forChild(dashboardRoutes),
    CommonModule,
    NzButtonModule,
    NzTableModule,
    SharedModule,
    DashboardNotificationsComponent,
    SeoDashboardAuthorStatisticsComponent,
    SeoDashboardArticlesListComponent,
    SeoDashboardRankingListComponent,
  ],
})
export class DashboardModule {}
