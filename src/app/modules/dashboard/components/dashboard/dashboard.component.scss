@use 'shared' as *;

.dashboard {
  &-body {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
  }

  &-row {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 5px;
    width: 100%;

    div.col-6 {
      max-width: 49%;
    }
  }

  &-box {
    margin-bottom: 10px;

    &-dashed {
      min-height: 140px;
      border: 3px dashed $grey-4;
      margin: 5px;
    }
  }
}

.title {
  background: $grey-5;
  padding: 8px;
  border: 1px solid $table-border;
  border-bottom: 0;
}

.body {
  position: relative;
  min-height: 100px;
  padding: 8px;

  &.bg {
    background: $white;
    border: 1px solid $table-border;
  }
}

.info-container {
  margin: 15px 0px;

  > span {
    display: block;
    font-size: 14px;

    &:not(:last-child) {
      margin-bottom: 12px;
      font-weight: 700;
    }

    &:first-child {
      font-size: 18px;
      line-height: normal;
    }

    &:not(:first-child):not(:last-child) {
      margin-left: 36px;
    }

    &:last-child {
      font-style: italic;
    }

    a {
      margin-left: 5px;
    }
  }
}

:host::ng-deep {
  .ant-alert {
    &-warning {
      .ant-alert-message {
        color: red;
      }
    }

    &-message {
      font-weight: 700;
    }
  }
}
