<h1>{{ 'CMS.dashboard' | translate }}</h1>

<div class="dashboard-body">
  @for (row of rows$ | async; track $index) {
    <div class="dashboard-row row">
      @for (col of row.cols; track col.title) {
        @if ((!col.requiredPortalConfig || portalConfigService.isConfigSet(col.requiredPortalConfig)) && (!col.requiresExternalContributors || externalContributor())) {
          <div [ngClass]="'col-' + (col.width ?? 3)" class="dashboard-box">
            @if(col.title) {
              <div class="title">
                {{ col.title }}
              </div>
            }
            <div class="body" [class.bg]="!!col.title">
              @switch (col.content) {
                @case (DASHBOARD_ITEM.GOOGLE_REALTIME_TABLE) {
                  <app-google-realtime-table />
                }
                @case (DASHBOARD_ITEM.GOOGLE_ANALYTICS) {
                  <p>I<PERSON> jön majd a google analitics</p>
                }
                @case (DASHBOARD_ITEM.AUTHOR_STATISTICS) {
                  <app-seo-dashboard-author-statistics />
                }
                @case (DASHBOARD_ITEM.RANKING_LIST) {
                  <app-seo-dashboard-ranking-list />
                }
                @case (DASHBOARD_ITEM.ARTICLES_LIST) {
                  <app-seo-dashboard-articles-list />
                }
                @case (DASHBOARD_ITEM.GOOGLE_DRIVE) {
                  <a
                    href="https://drive.google.com/drive/folders/1bkwc0sYzbyyDuVxc5Jyl6H2qMJfnX71x?fbclid=IwAR1ybEJh93zZZrgZCYSZpGJRQpadTMnsenAHJ-_7SWO77xjPTBqEzEfoI1A"
                    target="_blank">Google Drive</a>
                }
                @case (DASHBOARD_ITEM.NOTIFICATIONS) {
                  <app-dashboard-notifications />
                }
                @case (DASHBOARD_ITEM.CONTACT) {
                  @if (currentDomain$ | async; as currentDomain) {
                    <section class="info-container">
                      <span>Ha bármilyen technikai/funkcionális problémát tapasztaltok a CMS-ben, azt alábbi elérhetőségen jelezzétek:</span>
                      <span> Email:
                        @if (currentDomain.key === 'nso') {
                          <a href="mailto:<EMAIL>">nsosupport&#64;trendency.hu</a>
                        } @else {
                          <a href="mailto:<EMAIL>">mwsupport&#64;trendency.hu</a>
                        }
                      </span>
                      <span>Munkaidőben (H-P 09:00-17:00):<a href="tel:+36704238799">+36 70 423 8799</a></span>
                      <span>Munkaidőn kívül:<a href="tel:+36703175634">+36 70 317 5634</a></span>
                      <span>
                        A munkaidőn kívüli e-mailes megkereséseket csak másnap dolgozzuk fel. 17.00 óra után vagy hétvégén a szerkesztőség munkáját akadályozó problémát telefonon kell jelezni.
                      </span>
                    </section>
                  }
                }
                @default {
                  {{ col.content }}
                }
              }
            </div>
          </div>
        }
      }
    </div>
  }
</div>
