import { Component, computed, inject } from '@angular/core';
import { DASHBOARD_ITEM } from '../../api/dashboard.const';
import { DashboardService } from '../../api/dashboard.service';
import { map } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { DomainService } from 'src/app/core/modules/admin/services/domain.service';
import { Domain } from 'src/app/core/modules/admin/admin.definitions';
import { PortalConfigService } from '@shared/services/portal-config.service';
import { SeoDashboardApiService } from '@modules/seo-dashboard/api/services/seo-dashboard-api.service';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  standalone: false,
})
export class DashboardComponent {
  private readonly dashboardService = inject(DashboardService);
  private readonly domainService = inject(DomainService);
  private readonly portalConfigService = inject(PortalConfigService);
  private readonly seoDashboardApiService = inject(SeoDashboardApiService);

  readonly DASHBOARD_ITEM = DASHBOARD_ITEM;
  readonly dashboard$ = this.dashboardService.getDashboard();
  readonly rows$ = this.dashboard$.pipe(map((dashboard) => dashboard.rows));
  readonly currentDomain$: Observable<Domain> = this.domainService.currentDomain$;

  readonly externalContributor = computed(() => this.seoDashboardApiService.currentExternalContributor());
}
