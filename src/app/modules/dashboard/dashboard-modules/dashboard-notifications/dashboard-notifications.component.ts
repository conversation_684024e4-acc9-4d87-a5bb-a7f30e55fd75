import { Component, inject, signal } from '@angular/core';
import { NotificationService } from '@core/services/api/notifications/notification.service';
import { toSignal } from '@angular/core/rxjs-interop';
import { of, timer } from 'rxjs';
import { catchError, finalize, map, switchMap } from 'rxjs/operators';
import { UserNotification } from '@core/services/api/notifications/notification.definitions';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import { NzAlertComponent } from 'ng-zorro-antd/alert';

@Component({
  selector: 'app-dashboard-notifications',
  imports: [NzSpinComponent, NzAlertComponent],
  templateUrl: './dashboard-notifications.component.html',
  styleUrl: './dashboard-notifications.component.scss',
})
export class DashboardNotificationsComponent {
  private readonly notificationService = inject(NotificationService);

  private readonly isLoading = signal(true);

  readonly dashboardNotifications = toSignal<UserNotification[] | undefined>(
    //5 minutes if disabled:
    timer(0, this.notificationService.isSlowPolling ? 300_000 : 60_000).pipe(
      switchMap(() =>
        this.notificationService.getDashboardNotifications().pipe(
          finalize(() => this.isLoading.set(false)),
          catchError((err) => {
            console.error(err);
            return of({ data: [] as UserNotification[] });
          }),
          map(({ data }) => data)
        )
      )
    ),
    { initialValue: undefined }
  );
}
