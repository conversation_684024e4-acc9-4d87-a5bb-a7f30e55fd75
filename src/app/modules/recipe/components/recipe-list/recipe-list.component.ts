import { Component, inject } from '@angular/core';
import { ListBaseComponent } from '../../../list-base.component';
import { IBaseContentConfig } from '../../../../core/core.definitions';
import { FormResponseAdapter } from '@shared/adapters/formResponseAdapter';
import { CustomTemplateType, DataTableFilterType, IDataTableColumnInfo } from '@shared/definitions/data-table.definitions';
import { IHttpOptions } from '@external/http';
import { finalize, tap } from 'rxjs/operators';
import { RecipeType } from '../../definitions/recipes.definitions';
import { PortalConfigService } from '@shared/services/portal-config.service';
import { PortalConfigSetting } from '@shared/definitions/portal-config';

@Component({
  selector: 'app-recipe-list',
  templateUrl: './recipe-list.component.html',
  styleUrls: ['./recipe-list.component.scss'],
  standalone: false,
})
export class RecipeListComponent extends ListBaseComponent {
  isLoading = false;
  status: RecipeType;
  recipeTypes = Object.values(RecipeType);

  portalConfigService = inject(PortalConfigService);

  get config(): IBaseContentConfig {
    this.formApiCall = this.getFormInfo.bind(this);
    return {
      contentType: 'recipe',
      contentListType: 'recipe',
      previewPageRoute: [],
      tableTitle: 'CMS.recipes',
      contentGroup: 'recipe',
      editorType: 'templates',
      dataColumns: this.getDataColumns(),
      formApiCall: this.getFormInfo.bind(this),
    };
  }

  override editNavigateTo(path: string, id?: string, typeOverwrite?: string): Promise<boolean> {
    const translatedRoute = this.editTranslatedRoute(path, id);
    return this.router.navigate(translatedRoute, {
      relativeTo: this.route,
      queryParams: {
        contentGroup: this.config.contentGroup,
        type: typeOverwrite ? typeOverwrite : this.dataTableConfig.config.contentType,
        prevUrl: this.router.url.split('?')[0],
      },
    });
  }

  override list(params?: IHttpOptions) {
    this.isLoading = true;
    return this.apiService.recipe.getAllByStatus({ params } as IHttpOptions, this.status).pipe(finalize(() => (this.isLoading = false)));
  }

  getFormInfo(id?: string) {
    return FormResponseAdapter.recipeAdapter(super.getFormInfo(id));
  }

  public onTabChange() {
    this.updateList();
  }

  protected override getPreviewRowAction(): any {
    return {
      ...super.getPreviewRowAction(),
      eventHandler: this.onRecipePreview.bind(this),
    };
  }

  private onRecipePreview(data: any) {
    const id = data.id;
    return this.apiService.recipe.generatePreview(id).pipe(
      tap((previewData) => {
        const previewUrls = this.contentPreviewService.getRecipePreviewUrl(previewData.data);
        if (previewUrls.missingValues.length > 0) {
          const missingValues = previewUrls.missingValues.map((value) => this.translate.instant(value)).join(', ');
          const missingValuesError = `Előnézethez töltse ki az alábbi mezőket: ${missingValues}`;
          this.sharedService.showNotification('error', missingValuesError);
          return previewData;
        }
        if (!this.utilsService.isBrowser()) {
          this.sharedService.showNotification('error', 'csak böngészőben elérhető funkció');
          return previewData;
        }

        window.open(previewUrls.url, '_blank');
      })
    );
  }

  private getDataColumns(): IDataTableColumnInfo[] {
    const enabledNationalDish = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_SEND_RECIPE_TO_NATIONAL_DISH_2025);

    const columns = [
      {
        key: 'title',
        property: 'title',
        title: 'name',
        filterable: {
          queryKey: 'global_filter',
          type: DataTableFilterType.TEXT,
        },
      },
      {
        key: 'slug',
        property: 'slug',
        title: 'slug',
        customTemplateType: CustomTemplateType.SLUG,
      },
      {
        key: 'publicAuthor.fullName',
        property: 'publicAuthor.fullName',
        title: 'maestro',
      },
      {
        key: 'sentByUser.fullName',
        property: 'sentByUser.fullName',
        title: 'submitter',
      },
      {
        key: 'isActive',
        property: 'isActive',
        title: 'isActive',
        customTemplateType: CustomTemplateType.IS_ACTIVE,
      },
      {
        key: 'createdAt',
        property: 'createdAt',
        title: 'createdAt',
        customTemplateType: CustomTemplateType.DATE_TIME,
      },
      {
        key: 'publishDate',
        property: 'publishDate',
        title: 'publishDate',
        customTemplateType: CustomTemplateType.DATE_TIME,
        orderable: {
          queryKey: 'publish_date_order',
        },
      },
      {
        key: 'cookTime',
        property: 'cookTime',
        title: 'cookTime',
      },
      {
        key: 'totalTime',
        property: 'totalTime',
        title: 'totalTime',
      },
      {
        key: 'cover_images_status',
        property: 'cover_images_status',
        title: 'coverImagesStatus',
        customTemplateType: CustomTemplateType.IS_ACTIVE,
      },
    ];

    switch (this.status) {
      case RecipeType.WAITING:
        columns.push(
          ...[
            {
              key: 'cover_image_temp',
              property: 'cover_image_temp',
              title: 'coverImageTemp',
              customTemplateType: CustomTemplateType.IMAGE,
            },
            {
              key: 'secondary_cover_image_temp',
              property: 'secondary_cover_image_temp',
              title: 'secondaryCoverImageTemp',
              customTemplateType: CustomTemplateType.IMAGE,
            },
          ]
        );
        break;
      default:
        columns.push({
          key: 'coverImage',
          property: 'coverImage',
          title: 'coverImage',
          customTemplateType: CustomTemplateType.IMAGE,
        });
        break;
    }

    if (enabledNationalDish) {
      columns.push({
        key: 'isNationalDish2025',
        property: 'isNationalDish2025',
        title: 'isNationalDish2025',
        customTemplateType: CustomTemplateType.IS_ACTIVE,
      });
    }

    return columns;
  }

  setStatus(status: RecipeType) {
    this.status = status;
  }
}
