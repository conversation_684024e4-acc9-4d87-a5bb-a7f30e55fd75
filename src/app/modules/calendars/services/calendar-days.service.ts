import { Injectable, signal } from '@angular/core';
import { CalendarPreview } from '@modules/calendars/components/calendar-preview/calendar-preview.definitions';

@Injectable({
  providedIn: 'root',
})
export class CalendarDaysService {
  calendarDaysForm = signal<any[]>([]);
  calendarDaysMeta = signal<any[]>([]);
  parentId = signal<string | null>(null);

  reset(): void {
    this.calendarDaysForm.set([]);
    this.calendarDaysMeta.set([]);
    this.parentId.set(null);
  }

  mapCalendarToPreview(calendarData: any): CalendarPreview {
    return {
      name: calendarData.name,
      backgroundImage: calendarData.backgroundImage?.fullSizeUrl ?? undefined,
      colorOfDays: calendarData.colorOfDays,
      sponsorshipLeft: calendarData.sponsorshipMainOnLeft?.title,
      days: calendarData.days?.map((day) => ({
        name: day.name,
        backgroundImageUrl: day?.backgroundBeforeOpenImage?.fullSizeUrl ?? calendarData.defaultDayBackgroundBeforeOpenImage?.fullSizeUrl ?? undefined,
      })),
    };
  }
}
