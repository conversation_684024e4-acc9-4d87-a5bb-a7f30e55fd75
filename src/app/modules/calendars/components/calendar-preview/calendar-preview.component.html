@if (data(); as data) {
  <div class="calendar-wrapper" [style.background-image]="'url(' + data?.backgroundImage + ')'">
    <div class="calendar-body">
      @if (data.sponsorshipLeft) {
        <div class="calendar-sponsor-left">
          <!-- TODO sponsorship, needs BE data. -->
          <p>SZPONZOR</p>
        </div>
      }
      <div class="calendar-days-wrapper">
        <div class="calendar-day-list">
          @for (day of data?.days; track day.name) {
            @if (day.name) {
              <div class="calendar-day" [style.background-image]="'url(' + day?.backgroundImageUrl + ')'">
                <p class="calendar-day-name" [style.color]="data.colorOfDays">
                  {{ day.name }}
                </p>
              </div>
            }
          }
        </div>
      </div>
    </div>
  </div>
}
