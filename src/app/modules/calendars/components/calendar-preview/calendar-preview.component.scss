:host {
  &.mobile {
    .calendar-sponsor-left {
      display: none;
    }
  }
}

.calendar-wrapper {
  display: flex;
  flex-direction: column;
  background-size: cover;
  background-position: center;
  width: 100%;
  padding: 12px;
}

.calendar-body {
  display: flex;
  gap: 12px;
}

.calendar-sponsor-left {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30%;
  background-color: #5e727d;
}

.calendar-days-wrapper {
  display: flex;
  gap: 16px;
  height: 100%;
  background-size: cover;
  background-position: center;
  flex: 1;
}

.calendar-day-list {
  padding: 12px;
  display: flex;
  width: 100%;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: space-around;
}

.calendar-day {
  cursor: default;
  padding: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: cover;
  background-position: center;
  min-width: 70px;
  background-color: var(--kui-white);
}
