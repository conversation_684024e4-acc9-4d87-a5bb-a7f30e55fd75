<div class="header">
  <h3>Kalendárium szerkesztése</h3>
  <div class="header-buttons">
    <button nz-button nzType="default" [routerLink]="['/admin', 'calendars']">Vissza</button>
    <button nz-button nzType="primary" (click)="saveDataAndUpdateDays()" [nzLoading]="loading()"><PERSON><PERSON><PERSON></button>
  </div>
</div>
<app-form-generator-basic
  #formTemplate
  (autosave)="onFormControlChanged($event)"
  [formControls]="formInfo() | fromContentDataToFormControls"
  [originalContentData]="formInfo()"
></app-form-generator-basic>
<div class="days-table-title">
  <h3>Napok</h3>
  <button nz-button nzType="primary" (click)="onAddDay()"> Nap hozzáadása</button>
</div>
<nz-table #days [nzData]="tableDays()" [nzPageSize]="100">
  <thead>
  <tr>
    <th>{{ 'order' | translate }}</th>
    <th>{{ 'CMS.name' | translate }}</th>
    <th>{{ 'CMS.date' | translate }}</th>
    <th>{{ 'CMS.visibilityStart' | translate }}</th>
    <th>{{ 'CMS.visibilityEnd' | translate }}</th>
    <th>{{ 'CMS.openableStart' | translate }}</th>
    <th>{{ 'CMS.openableEnd' | translate }}</th>
    <th>Műveletek</th>
  </tr>
  </thead>
  <tbody>
  <tr *ngFor="let data of days.data; let i = index">
    <td>{{ data.daysOrder ?? 'Nincs megadva' }}</td>
    <td>{{ data.name ?? 'Nincs megadva'}}</td>
    <td>{{ (data?.dateOfDay | dateFormat: 'utc': 'yyyy.MM.dd.' ) ?? 'Nincs megadva' }}</td>
    <td>{{ (data?.visibilityStart | dateFormat: 'local': 'yyyy.MM.dd. HH:mm:ss' ) ?? 'Nincs megadva' }}</td>
    <td>{{ (data?.visibilityEnd | dateFormat: 'local': 'yyyy.MM.dd. HH:mm:ss' ) ?? 'Nincs megadva' }}</td>
    <td>{{ (data?.openableStart | dateFormat: 'local': 'yyyy.MM.dd. HH:mm:ss' ) ?? 'Nincs megadva' }}</td>
    <td>{{ (data?.openableEnd | dateFormat: 'local': 'yyyy.MM.dd. HH:mm:ss' ) ?? 'Nincs megadva' }}</td>
    <td class="actions">
      <button (click)="onDayEdit(data.id)" nz-button nzType="primary">Szerkesztés</button>
      <button
        class="event-actions-button"
        nz-popconfirm
        nzPopconfirmTitle="Biztosan törli ezt a napot?"
        nzOkText="Igen"
        nzCancelText="Mégsem"
        (nzOnConfirm)="onDayDelete(data.id)"
        nz-button
        nzType="primary"
        nzDanger
      >
        <span nz-icon nzType="delete" nzTheme="outline"></span>
      </button>
    </td>
  </tr>
  </tbody>
</nz-table>

<div class="days-table-title">
  <h3>Előnézet</h3>
</div>
<div class="preview-mode-selector">
  <nz-radio-group class="size-control" [ngModel]="previewMode()" (ngModelChange)="previewMode.set($event)">
    <label nz-radio-button nzValue="desktop"><span nz-icon nzType="desktop" nzTheme="outline"></span> {{ 'CMS.desktop-view' | translate }}</label>
    <label nz-radio-button nzValue="mobile"><span nz-icon nzType="mobile" nzTheme="outline"></span> {{ 'CMS.mobile-view' | translate }}</label>
  </nz-radio-group>
</div>
<div class="calendar-preview" [class.mobile]="previewMode() === 'mobile'">
  <app-calendar-preview [data]="calendarPreview()" [class.mobile]="previewMode() === 'mobile'"></app-calendar-preview>
</div>
