import { DOCUMENT } from '@angular/common';
import { inject, Injectable, signal } from '@angular/core';
import { DomainService } from '@core/modules/admin/services/domain.service';
import { ApiService } from '@core/services/api.service';
import { CleanHttpService } from '@core/services/clean-http.service';
import { ReqService } from '@external/http';
import { StorageService } from '@external/utils';
import type { Author } from '@modules/contributors/contributors.definitions';
import {
  ArticleRankingList,
  AuthorStatistics,
  SeoDashboardArticlesResponse,
  SeoSearchArticleRankingListParams,
  SeoSearchAuthorStatisticsParams,
  SeoSearchMindArticleParams,
} from '@modules/seo-dashboard/definitions';
import type { ApiResult } from '@trendency/kesma-ui';
import { format } from 'date-fns';
import { Observable, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class SeoDashboardApiService {
  private readonly reqService = inject(ReqService);
  private readonly cleanHttpService = inject(CleanHttpService);
  private readonly apiService = inject(ApiService);
  private readonly document = inject(DOCUMENT);
  private readonly storageService = inject(StorageService);
  private readonly domainService = inject(DomainService);

  private readonly contentLang = this.apiService.contentLang;
  private readonly cmsLang = this.apiService.cmsLang;

  private mindToken = this.storageService.getCookie('mind_jwt_token', this.document.cookie, null);

  get currentDomainMindKey(): string | undefined {
    switch (this.domainService.currentDomain?.key) {
      case 'vilaggazdasag':
        return 'vg';
      case 'magyarNemzet':
        return 'mno';
      default:
        return this.domainService.currentDomain?.key;
    }
  }

  readonly currentExternalContributor = signal<Author | null>(null);

  private readonly mindEnvironmentApiUrl = environment.mindApiUrl;

  login(): Observable<{ token: string | null }> {
    const jwt = this.storageService.getCookie('mind_jwt_token', this.document.cookie, null);

    return jwt
      ? of({ token: jwt }) // Don't log in if we already have jwt
      : this.reqService.get<{ token: string | null }>(`/api/${this.cmsLang}/${this.contentLang}/mind/get-auth-token`).pipe(
          tap(({ token }) => {
            if (!token) {
              return;
            }

            this.mindToken = token;
            this.storageService.setCookie('mind_jwt_token', token, 3600, environment.baseHost ? `.${environment.baseHost}` : null);
          }),
          catchError(() => of({ token: null }))
        );
  }

  getEditorUserStatistics(params: SeoSearchAuthorStatisticsParams): Observable<AuthorStatistics[]> {
    return this.cleanHttpService
      .get<ApiResult<AuthorStatistics[]>>(`${this.mindEnvironmentApiUrl}/api/editor/list`, {
        headers: this.getCommonHeaders(),
        params: this.getCommonParams(params),
      })
      .pipe(
        map(({ data }) => data),
        catchError(() => of([]))
      );
  }

  getArticleRankingList(params: SeoSearchArticleRankingListParams): Observable<ArticleRankingList[]> {
    return this.cleanHttpService
      .get<ArticleRankingList[]>(`${this.mindEnvironmentApiUrl}/api/articles/article-top-hitter`, {
        headers: this.getCommonHeaders(),
        params: this.getCommonParams(params),
      })
      .pipe(catchError(() => of([])));
  }

  getArticleList(params: SeoSearchMindArticleParams): Observable<SeoDashboardArticlesResponse> {
    return this.cleanHttpService
      .get<SeoDashboardArticlesResponse>(`${this.mindEnvironmentApiUrl}/api/articles/list`, {
        headers: this.getCommonHeaders(),
        params: this.getCommonParams(params),
      })
      .pipe(catchError(() => of({ data: [], total: 0 } as SeoDashboardArticlesResponse)));
  }

  private getCommonHeaders(): { Authorization: string; Portal: string } {
    return {
      Authorization: `Bearer ${this.mindToken}`,
      Portal: this.currentDomainMindKey,
    };
  }

  private getCommonParams<T extends SeoSearchArticleRankingListParams | SeoSearchAuthorStatisticsParams | SeoSearchMindArticleParams>(
    params: T
  ): Omit<T, 'dateFrom' | 'dateTo'> & {
    editor: string;
    view: 'date';
    dateFrom: string;
    dateTo: string;
  } {
    return {
      ...params,
      view: 'date',
      editor: this.currentExternalContributor()?.user?.fullName,
      dateFrom: format(params.dateFrom, 'yyyy-MM-dd'),
      dateTo: format(params.dateTo, 'yyyy-MM-dd'),
    };
  }
}
