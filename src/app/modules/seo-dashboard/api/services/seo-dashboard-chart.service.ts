import { inject, Injectable } from '@angular/core';
import type { ArticleCountDetail, ChannelGroup, FinalizedChartData } from '@modules/seo-dashboard/definitions';
import { getColorByPropertyName, lineTypeChartDataSet, transformToChartJsData } from '@modules/seo-dashboard/utils';
import { TranslateService } from '@ngx-translate/core';
import type { ChartDataset } from 'chart.js';
import cloneDeep from 'lodash-es/cloneDeep';

@Injectable({
  providedIn: 'root',
})
export class SeoDashboardChartService {
  private readonly translateService = inject(TranslateService);

  transformBarChartData(chartData: ArticleCountDetail[]): FinalizedChartData {
    const retChartData = cloneDeep(transformToChartJsData(chartData));

    retChartData.datasets = retChartData.datasets.map((obj: ChartDataset<any>) => {
      if (obj.label === 'pageViews') {
        obj = lineTypeChartDataSet(obj as ChartDataset<'line'>, getColorByPropertyName(obj.label));
        obj.type = 'line';
        obj.yAxisID = 'lineAxis';
        obj.label = 'Oldalmegtekintések';
      } else {
        obj.backgroundColor = obj.colorCode;
        obj.borderRadius = 2;
        obj.hoverBorderWidth = 0;
        obj.yAxisID = 'y';
      }
      return obj;
    });

    return retChartData;
  }

  transformPieChartData(chartData: ChannelGroup[]): FinalizedChartData {
    return {
      labels: chartData.map((channel) =>
        channel?.source ? this.translateService.instant(`CMS.seo-dashboard.author-statistics.channel-groups.${channel?.source}`) : ''
      ),
      datasets: [
        {
          label: '',
          data: chartData.map((channel) => channel?.value),
          backgroundColor: chartData.map((channel) => channel.colorCode),
        },
      ],
    };
  }
}
