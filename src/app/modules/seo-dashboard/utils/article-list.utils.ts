import { SeoDashboardMindArticle } from '../definitions';

export const mindArticleDataToArticle = (article: SeoDashboardMindArticle) => ({
  ...article,
  onFrontPage: article?.onFrontPage ? 'Igen' : article?.onFrontPage !== null ? 'Nem' : 'N/A',
  isOriginal: article?.isOriginal ? 'Igen' : article?.isOriginal !== null ? 'Nem' : 'N/A',
  author: article?.author.replace(/,/g, ', '),
  publishDate: new Date(article.publishDate),
});
