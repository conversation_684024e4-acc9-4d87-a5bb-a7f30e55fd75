import { ChartDataset } from 'chart.js';
import { CHART_PROPERTIES } from '../constants';
import { ArticleCountDetail, DataSet, FinalizedChartData } from '../definitions';

export const transformToChartJsData = (data: ArticleCountDetail[]): FinalizedChartData => {
  const labels = data?.map((item) => item?.statDate);
  const pageViewsData = data?.map((item) => item?.pageViews);

  const allTopics = data?.flatMap((item) => item?.topics);

  const topicsMap = allTopics?.reduce<Map<string, string>>((acc, topic) => {
    acc?.set(topic?.slug, topic?.colorCode);
    return acc;
  }, new Map());

  const uniqueTopics: [string, string][] = Array.from(topicsMap.entries());

  const datasets: DataSet[] = uniqueTopics.map(([topicSlug, topicColorCode]) => {
    const topicDataValues = data.map((item) => {
      const topic = item.topics.find((t) => t.slug === topicSlug);
      return topic ? topic.value : 0;
    });

    return {
      label: allTopics.find((topic) => topic.slug === topicSlug)?.name,
      data: topicDataValues,
      colorCode: topicColorCode,
    };
  });

  datasets.unshift({
    label: 'pageViews',
    data: pageViewsData,
    colorCode: null,
  });

  return {
    labels: labels,
    datasets: datasets,
  };
};

export function lineTypeChartDataSet(overrideDefaultsWith: ChartDataset<'line'>, lineColor?: string): ChartDataset<'line'> {
  return {
    ...{
      fill: true,
      pointBorderColor: '#ffffff',
      borderWidth: 2,
      pointRadius: 5,
      pointBackgroundColor: lineColor,
      // tension: 0.3,
      spanGaps: true,
      borderColor: lineColor,
      backgroundColor: '#fff0',
      hoverBorderColor: lineColor,
      pointBorderWidth: 2,
      pointHoverRadius: 6,
      pointHoverBorderWidth: 2,
      pointHoverBorderColor: '#ffffff',
      pointHoverBackgroundColor: lineColor,
    },
    ...overrideDefaultsWith,
  };
}

export function getColorByPropertyName(propertyName: string): string {
  const item = CHART_PROPERTIES.find((item) => item.matchingNames?.includes(propertyName)) ?? null;

  return item?.colorCode;
}
