import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { SeoDashboardApiService } from '@modules/seo-dashboard/api/services/seo-dashboard-api.service';
import { SeoDashboardAuthorStatisticsComponent } from '@modules/seo-dashboard/components/seo-dashboard-author-statistics/seo-dashboard-author-statistics.component';
import { SeoDashboardRankingListComponent } from '@modules/seo-dashboard/components/seo-dashboard-ranking-list/seo-dashboard-ranking-list.component';
import { SeoDashboardArticlesListComponent } from '@modules/seo-dashboard/components/seo-dashboard-articles-list/seo-dashboard-articles-list.component';

@Component({
  selector: 'app-seo-dashboard',
  templateUrl: './seo-dashboard.component.html',
  styleUrl: './seo-dashboard.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SeoDashboardAuthorStatisticsComponent, SeoDashboardRankingListComponent, SeoDashboardArticlesListComponent],
})
export class SeoDashboardComponent {
  private readonly seoDashboardApiService = inject(SeoDashboardApiService);

  readonly externalContributor = computed(() => this.seoDashboardApiService.currentExternalContributor());
}
