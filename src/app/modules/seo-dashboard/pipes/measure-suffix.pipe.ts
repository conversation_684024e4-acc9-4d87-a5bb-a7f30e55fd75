import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'measureSuffix',
})
export class MeasureSuffixPipe implements PipeTransform {
  transform(value: number | string, key: string): string | number {
    if (!value && value !== 0) {
      return 'N/A';
    }

    if (typeof value === 'number') {
      value = value.toLocaleString('hu-HU', { maximumFractionDigits: 1 });
    }

    switch (key) {
      case 'averageTimeOnPage':
        return `${value} s`;
      case 'articleLength':
        return `${value} karakter`;
      case 'bounceRate':
      case 'ctr':
      case 'frontPageClickRate':
        return `${value} %`;
      case 'users':
        return `${value} fő`;
      case 'pageViews':
      case 'channelGroupDirect':
      case 'channelGroupOrganic':
      case 'channelGroupReferral':
      case 'channelGroupSocial':
      case 'sessions':
        return `${value} db`;
      default:
        return value;
    }
  }
}
