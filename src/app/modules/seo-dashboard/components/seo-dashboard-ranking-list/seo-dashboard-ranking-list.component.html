<div class="filter-fields">
  <label for="data-range">Adatok intervalluma:</label>
  <nz-range-picker
    id="date-range"
    class="date-range-input"
    nzFormat="yyyy-MM-dd"
    [ngModel]="dateRange()"
    [nzAllowClear]="false"
    [nzPlaceHolder]="['Tól', 'Ig']"
    [nzRanges]="ranges"
    (ngModelChange)="onDateChange($event)"
  />
  <label for="segment">Szegmens:</label>
  <nz-select
    id="segment"
    [nzAllowClear]="false"
    [ngModel]="segment()"
    (ngModelChange)="onSegmentChange($event)"
  >
    @for (item of segments; track item.value) {
      <nz-option [nzValue]="item.value" [nzLabel]="item.label" />
    }
  </nz-select>
</div>

<div class="ranking-list-container">
  @if (isLoading()) {
    <div class="loading-layer">Adatok betöltése</div>
  }

  @if (hasRankingListData()) {
    <nz-table
      #articleRankingList
      class="ranking-list-table"
      [nzBordered]="true"
      [nzFrontPagination]="false"
      [nzScroll]="{ x: tableWidth() + 'px' }"
      [nzData]="articleRankingListData()"
    >
      <thead>
        <tr>
          <th nzLeft></th>
          @for (columnTitle of ArticleRankingListTableColumnTitles; track columnTitle) {
            <th>{{ 'CMS.seo-dashboard.article-ranking-list.' + columnTitle | translate }}</th>
          }
        </tr>
      </thead>
      <tbody>
        @for (row of articleRankingList?.data; track $index) {
          <tr>
            <td nzLeft>{{ 'CMS.seo-dashboard.article-ranking-list.' + row.type | translate }}</td>
            @for (key of ArticleRankingListTableColumnTitles; track key) {
              <td>
                @if (key === 'title') {
                  <a [href]="row?.url" target="_blank">
                    {{ row?.[key] }}
                  </a>
                } @else {
                  {{ row?.[key] | number: '1.0-1' | measureSuffix: key }}
                }
              </td>
            }
          </tr>
        }
      </tbody>
    </nz-table>
  } @else {
    <span>Nincs megjeleníthető adat</span>
  }
</div>
