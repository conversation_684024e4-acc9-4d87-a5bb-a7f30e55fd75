import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, type OnInit, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SeoDashboardApiService } from '@modules/seo-dashboard/api/services/seo-dashboard-api.service';
import { ArticleRankingListTableColumnTitles } from '@modules/seo-dashboard/constants';
import type { ArticleRankingList } from '@modules/seo-dashboard/definitions';
import { getTableWidth } from '@modules/seo-dashboard/utils';
import { finalize } from 'rxjs';
import { NzCellFixedDirective, NzTableComponent } from 'ng-zorro-antd/table';
import { DecimalPipe } from '@angular/common';
import { TranslatePipe } from '@ngx-translate/core';
import { MeasureSuffixPipe } from '@modules/seo-dashboard/pipes';
import { startOfMonth, startOfYear } from 'date-fns';
import { NzDatePickerComponent, NzRangePickerComponent } from 'ng-zorro-antd/date-picker';
import { FormsModule } from '@angular/forms';
import { NzOptionComponent, NzSelectComponent } from 'ng-zorro-antd/select';

@Component({
  selector: 'app-seo-dashboard-ranking-list',
  templateUrl: './seo-dashboard-ranking-list.component.html',
  styleUrl: './seo-dashboard-ranking-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NzTableComponent,
    NzCellFixedDirective,
    DecimalPipe,
    TranslatePipe,
    MeasureSuffixPipe,
    NzRangePickerComponent,
    FormsModule,
    NzDatePickerComponent,
    NzOptionComponent,
    NzSelectComponent,
  ],
})
export class SeoDashboardRankingListComponent implements OnInit {
  private readonly seoDashboardApiService = inject(SeoDashboardApiService);
  private readonly destroyRef = inject(DestroyRef);

  readonly ArticleRankingListTableColumnTitles = ArticleRankingListTableColumnTitles;
  readonly ranges = {
    'Ez év': [startOfYear(new Date()), new Date()],
    'E hónap': [startOfMonth(new Date()), new Date()],
  } as Record<string, [Date, Date]>;
  readonly segments = [
    {
      label: 'Összes',
      value: 'total',
    },
    {
      label: 'Mobil',
      value: 'mobile',
    },
    {
      label: 'Asztali',
      value: 'desktop',
    },
  ] as const;

  readonly segment = signal<(typeof this.segments)[number]['value']>(this.segments[0]?.value);
  readonly dateRange = signal<[Date, Date]>(this.ranges['Ez év']);
  readonly isLoading = signal(false);
  readonly hasRankingListData = signal(true);
  readonly articleRankingListData = signal<ArticleRankingList[]>([]);

  readonly tableWidth = computed(() => getTableWidth(this.ArticleRankingListTableColumnTitles));

  ngOnInit(): void {
    this.loadData();
  }

  onDateChange(newValue: [Date, Date]): void {
    this.dateRange.set(newValue);
    this.loadData();
  }

  onSegmentChange(newValue: (typeof this.segments)[number]['value']): void {
    this.segment.set(newValue);
    this.loadData();
  }

  private loadData(): void {
    this.isLoading.set(true);
    this.seoDashboardApiService
      .getArticleRankingList({
        segment: this.segment(),
        dateFrom: this.dateRange()[0],
        dateTo: this.dateRange()[1],
      })
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        finalize(() => this.isLoading.set(false))
      )
      .subscribe((data: ArticleRankingList[]) => {
        if (!data?.length) {
          this.hasRankingListData.set(false);
          return;
        }
        this.articleRankingListData.set(data);
      });
  }
}
