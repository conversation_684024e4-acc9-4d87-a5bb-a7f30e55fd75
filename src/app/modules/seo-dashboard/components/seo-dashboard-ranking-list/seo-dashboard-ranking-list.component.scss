:host {
  display: block;
  margin-bottom: 10px;
  .ant-table-cell-fix-left {
    z-index: 1;
  }
}

nz-select {
  width: 250px;
}

.filter-fields {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 10px;
}

.ranking-list {
  &-container {
    position: relative;
  }

  &-table {
    th {
      background-color: #f6f8fc;
    }
    tr td:first-child {
      font-weight: bold;
    }
  }
}

.loading-layer {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  backdrop-filter: blur(5px);
}

h2 {
  margin-bottom: 15px;
}

td, th {
  padding: 5px 12px;
}

