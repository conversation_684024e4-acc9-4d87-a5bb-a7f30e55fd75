<div class="filter-fields">
  <label for="data-range">Adatok intervalluma:</label>
  <nz-range-picker
    id="date-range"
    class="date-range-input"
    nzFormat="yyyy-MM-dd"
    [ngModel]="dateRange()"
    [nzAllowClear]="false"
    [nzPlaceHolder]="['Tól', 'Ig']"
    [nzRanges]="ranges"
    (ngModelChange)="onDateChange($event)"
  />
  <label for="segment">Szegmens:</label>
  <nz-select
    id="segment"
    [nzAllowClear]="false"
    [ngModel]="segment()"
    (ngModelChange)="onSegmentChange($event)"
  >
    @for (item of segments; track item.value) {
      <nz-option [nzValue]="item.value" [nzLabel]="item.label" />
    }
  </nz-select>
</div>
<div class="statistics-container">
  @if (isLoading()) {
    <div class="loading-layer">Adatok betöltése</div>
  }
  @if (hasStatisticsData()) {
    <div class="statistic-item column-first">
      <span>Szerkesztő neve</span>
      <span>{{ authorStatistics()?.name }}</span>
    </div>
    <div class="statistic-item column-second">
      <span>Szerkesztői profil</span>
      <span>{{ authorStatistics()?.authorProfile || 'N/A' }}</span>
    </div>
    <div class="statistic-item column-first">
      <span>Publikált cikkek száma</span>
      <span>{{ authorStatistics()?.articleCount | number: '1.0-1' }} db</span>
    </div>
    <div class="statistic-item column-second">
      <span>Legjell. tematika</span>
      <span>{{ authorStatistics()?.topic }}</span>
    </div>
    <div class="statistic-item">
      <span>Átl. felhasználószám / cikk</span>
      <span>{{ authorStatistics()?.averageUsers | number: '1.0-1' }} fő</span>
    </div>
    <div class="statistic-item">
      <span>Átl. oldalmegtekintés / cikk</span>
      <span>{{ authorStatistics()?.averagePageViews | number: '1.0-1' }} db</span>
    </div>
    <div class="statistic-item">
      <span>Átl. címlapi CTR</span>
      <span>{{ authorStatistics()?.averageCtr | number: '1.0-1' }} %</span>
    </div>
    <div class="statistic-item">
      <span>Átl. oldalon töltött idő</span>
      <span>{{ authorStatistics()?.averageTimeOnPage | number: '1.0-1' }} s</span>
    </div>
    <div class="statistic-item">
      <span>Átl. vissszafordulási arány</span>
      <span>{{ authorStatistics()?.averageBounceRate | number: '1.0-1' }} %</span>
    </div>
    <div class="statistic-item">
      <span>Átl. karakterszám / cikk</span>
      <span>{{ authorStatistics()?.averageLength | number: '1.0-1' }} karakter</span>
    </div>
    <div class="statistic-item">
      <span>Átl. szentiment érték</span>
      <span>{{ authorStatistics()?.averagePolarityIndex | number: '1.0-1' }}</span>
    </div>
    <div class="statistic-item">
      <span>Átl. SEO pontszám</span>
      <span>{{ authorStatistics()?.averageSeoScore | number: '1.0-1' }}</span>
    </div>
    <div class="more-button last-row">
      <button (click)="openAuthorDetailsClick()" class="w-100" nz-button nzType="primary">További részletek</button>
    </div>
  } @else {
    <span>Nincs megjeleníthető adat</span>
  }
</div>
