:host {
  display: block;
  margin-bottom: 10px;
}

h2 {
  margin-bottom: 15px;
}

nz-select {
  width: 250px;
}

.filter-fields {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 10px;
}

.statistics-container {
  padding: 10px;
  background-color: white;
  position: relative;

  display: grid;
  grid-template-columns: auto auto auto auto;
  grid-column-gap: 20px;
  grid-row-gap: 10px;
}

.loading-layer {
  position: absolute;
  top:0;
  left:0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  backdrop-filter: blur(5px);
}

.column-first {
  grid-column: 1 / span 2;
}

.column-second {
  grid-column: 3 / span 2;
}

.last-row {
  grid-column: 2 / span 2;
}

.statistic-item {
  display: flex;
  flex-direction: column;
  background-color: #f6f8fc;
  padding: 10px;

  span:first-child {
    font-weight: bold;
  }
}

.column-first,
.column-second {
  flex-direction: row;
  gap: 5px;
}

.more-button {
  display: flex;
  align-items: center;
  justify-content: center;
}

td, th {
  padding: 5px 12px;
}
