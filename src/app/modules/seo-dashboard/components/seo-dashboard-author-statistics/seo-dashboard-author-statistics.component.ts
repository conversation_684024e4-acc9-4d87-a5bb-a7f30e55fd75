import { ChangeDetectionStrategy, Component, DestroyRef, inject, type OnInit, signal, ViewContainerRef } from '@angular/core';
import { NzModalService } from 'ng-zorro-antd/modal';
import { SeoDashboardAuthorStatisticsModalComponent } from '../modals/seo-dashboard-author-statistics-modal/seo-dashboard-author-statistics-modal.component';
import { SeoDashboardApiService } from '@modules/seo-dashboard/api/services/seo-dashboard-api.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import type { AuthorStatistics } from '@modules/seo-dashboard/definitions';
import { finalize } from 'rxjs';
import { DecimalPipe } from '@angular/common';
import { NzDatePickerComponent, NzRangePickerComponent } from 'ng-zorro-antd/date-picker';
import { FormsModule } from '@angular/forms';
import { startOfMonth, startOfYear } from 'date-fns';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { NzOptionComponent, NzSelectComponent } from 'ng-zorro-antd/select';

@Component({
  selector: 'app-seo-dashboard-author-statistics',
  templateUrl: './seo-dashboard-author-statistics.component.html',
  styleUrl: './seo-dashboard-author-statistics.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [DecimalPipe, NzDatePickerComponent, NzRangePickerComponent, FormsModule, NzButtonComponent, NzSelectComponent, NzOptionComponent],
})
export class SeoDashboardAuthorStatisticsComponent implements OnInit {
  private readonly modal = inject(NzModalService);
  private readonly viewContainerRef = inject(ViewContainerRef);
  private readonly seoDashboardApiService = inject(SeoDashboardApiService);
  private readonly destroyRef = inject(DestroyRef);

  readonly ranges = {
    'Ez év': [startOfYear(new Date()), new Date()],
    'E hónap': [startOfMonth(new Date()), new Date()],
  } as Record<string, [Date, Date]>;
  readonly segments = [
    {
      label: 'Összes',
      value: 'total',
    },
    {
      label: 'Mobil',
      value: 'mobile',
    },
    {
      label: 'Asztali',
      value: 'desktop',
    },
  ] as const;

  readonly segment = signal<(typeof this.segments)[number]['value']>(this.segments[0]?.value);
  readonly dateRange = signal<[Date, Date]>(this.ranges['Ez év']);
  readonly authorStatistics = signal<AuthorStatistics | null>(null);
  readonly isLoading = signal(false);
  readonly hasStatisticsData = signal(true);

  ngOnInit(): void {
    this.loadStats();
  }

  onDateChange(newValue: [Date, Date]): void {
    this.dateRange.set(newValue);
    this.loadStats();
  }

  onSegmentChange(newValue: (typeof this.segments)[number]['value']): void {
    this.segment.set(newValue);
    this.loadStats();
  }

  openAuthorDetailsClick(): void {
    const modal = this.modal.create({
      nzContent: SeoDashboardAuthorStatisticsModalComponent,
      nzTitle: this.seoDashboardApiService.currentExternalContributor().fullName,
      nzViewContainerRef: this.viewContainerRef,
      nzWidth: 800,
      nzData: {
        data: this.authorStatistics(),
      },
      nzFooter: [
        {
          label: 'Rendben',
          onClick(): void {
            modal.destroy();
          },
          type: 'primary',
        },
      ],
    });
  }

  private loadStats(): void {
    this.isLoading.set(true);
    this.seoDashboardApiService
      .getEditorUserStatistics({
        segment: this.segment(),
        dateFrom: this.dateRange()[0],
        dateTo: this.dateRange()[1],
        sortDirection: 'desc',
      })
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        finalize(() => this.isLoading.set(false))
      )
      .subscribe((data: AuthorStatistics[]) => {
        if (!data?.length) {
          this.hasStatisticsData.set(false);
          return;
        }
        this.authorStatistics.set(data?.[0]);
      });
  }
}
