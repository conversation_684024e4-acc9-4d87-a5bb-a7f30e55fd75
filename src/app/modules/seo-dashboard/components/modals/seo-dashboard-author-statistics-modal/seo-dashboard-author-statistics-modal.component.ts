import { ChangeDetectionStrategy, Component, computed, ElementRef, inject, type OnDestroy, type OnInit, viewChild } from '@angular/core';
import { SeoDashboardChartService } from '@modules/seo-dashboard/api/services/seo-dashboard-chart.service';
import { ArticleViewsTableColumnTitles } from '@modules/seo-dashboard/constants';
import type { ArticleCountDetail, ArticleView, AuthorStatistics, ChannelGroup } from '@modules/seo-dashboard/definitions';
import { getTableWidth } from '@modules/seo-dashboard/utils';
import Chart from 'chart.js/auto';
import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { NzCellFixedDirective, NzTableComponent } from 'ng-zorro-antd/table';
import { TranslatePipe } from '@ngx-translate/core';
import { DecimalPipe } from '@angular/common';
import { MeasureSuffixPipe } from '@modules/seo-dashboard/pipes';

@Component({
  selector: 'app-seo-dashboard-author-statistics-modal',
  templateUrl: './seo-dashboard-author-statistics-modal.component.html',
  styleUrl: './seo-dashboard-author-statistics-modal.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NzTableComponent, TranslatePipe, NzCellFixedDirective, DecimalPipe, MeasureSuffixPipe],
})
export class SeoDashboardAuthorStatisticsModalComponent implements OnInit, OnDestroy {
  private readonly chartContainer = viewChild<ElementRef>('barChart');

  private readonly nzModalData = inject<{ data: AuthorStatistics }>(NZ_MODAL_DATA);
  private readonly seoDashboardChartService = inject(SeoDashboardChartService);

  readonly ArticleViewsTableColumTitles = ArticleViewsTableColumnTitles;

  private readonly modalData = computed<AuthorStatistics>(() => this.nzModalData.data);
  private readonly articleCountDetails = computed<ArticleCountDetail[]>(() => this.modalData()?.articleCountDetails);
  private readonly mostViewedArticle = computed<ArticleView>(() => ({
    ...this.modalData()?.mostViewedArticle,
    rowTitle: 'Legtöbb oldalmegtekintés elérő tartalom',
  }));
  private readonly leastViewedArticle = computed<ArticleView>(() => ({
    ...this.modalData()?.leastViewedArticle,
    rowTitle: 'Legkevesebb oldalmegtekintés elérő tartalom',
  }));

  readonly channelGroups = computed<ChannelGroup[]>(() => this.modalData()?.channelGroups?.sort((a, b) => b?.value - a?.value));

  readonly articleViewsData = computed<ArticleView[]>(() => [this.mostViewedArticle(), this.leastViewedArticle()]);

  readonly tableWidth = computed<number>(() => getTableWidth(this.ArticleViewsTableColumTitles));

  private chart: Chart;

  ngOnInit(): void {
    this.createChart();
  }

  ngOnDestroy(): void {
    this.chart.destroy();
  }

  createChart(): void {
    this.chart = new Chart(this.chartContainer().nativeElement, {
      type: 'bar',
      data: this.seoDashboardChartService.transformBarChartData(this.articleCountDetails()),
      options: {
        scales: {
          x: {
            stacked: true,
          },
          y: {
            stacked: true,
          },
          lineAxis: {
            display: true,
            axis: 'y',
            type: 'linear',
            position: 'right',
          },
        },
      },
    });
  }
}
