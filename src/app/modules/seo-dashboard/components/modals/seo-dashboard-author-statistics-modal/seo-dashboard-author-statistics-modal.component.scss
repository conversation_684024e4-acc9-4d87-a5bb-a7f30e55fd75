:host {
    display: block;

    .ant-table-cell-fix-left {
      z-index: 1;
    }
}

.chart {
    width: 100%;
    overflow-x: auto;
}

.channel-groups {
  &-table {
    margin-top: 10px;
  }
  &-source-color {
    height: 10px;
    width: 10px;
  }

  &-source-container {
    display: flex;
    align-items: center;
    gap: 10px;

    span {
      font-weight: bold;
    }
  }
}

.channel-groups {
  &-table {
    th, tr:nth-child(even) {
        background-color: #f6f8fc;
    }
  }
}

.article-view {
  &-table {
    th {
      background-color: #f6f8fc;
    }

    tr td:first-child {
      font-weight: bold;
    }
  }
}

td, th {
  padding: 5px 12px;
}

