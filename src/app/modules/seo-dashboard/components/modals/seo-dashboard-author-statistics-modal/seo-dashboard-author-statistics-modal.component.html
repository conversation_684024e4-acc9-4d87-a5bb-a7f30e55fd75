<h2><PERSON><PERSON><PERSON><PERSON> cikk</h2>
<div class="chart">
  <canvas #barChart id="'editors-list-details-number-of-articles'"></canvas>
</div>

<nz-table #channelGroupsTable class="channel-groups-table" [nzBordered]="true" [nzFrontPagination]="false" [nzData]="channelGroups()">
  <thead>
    <tr>
      <th></th>
      <th>{{ 'CMS.pageViews' | translate }}</th>
    </tr>
  </thead>
  <tbody>
    @for (row of channelGroupsTable.data; track row.source) {
      <tr>
        <td class="channel-groups-source-container">
          <div class="channel-groups-source-color" [style.background-color]="row?.colorCode"></div>
          <span> {{ 'CMS.seo-dashboard.author-statistics.channel-groups.' + row?.source | translate }}</span>
        </td>
        <td>{{ row?.value }} db</td>
      </tr>
    }
  </tbody>
</nz-table>

<nz-table
  #articleViewsTable
  class="article-view-table"
  [nzBordered]="true"
  [nzFrontPagination]="false"
  [nzScroll]="{ x: tableWidth() + 'px' }"
  [nzData]="articleViewsData()"
>
  <thead>
    <tr>
      <th nzLeft></th>
      @for (columnTitle of ArticleViewsTableColumTitles; track columnTitle) {
        <th>{{ 'CMS.seo-dashboard.author-statistics.article-views.' + columnTitle | translate }}</th>
      }
    </tr>
  </thead>
  <tbody>
    @for (row of articleViewsTable?.data; track row.rowTitle) {
      <tr>
        <td nzLeft>{{ row.rowTitle }}</td>
        @for (key of ArticleViewsTableColumTitles; track key?.title) {
          <td>
            @if (key === 'title') {
              <a [href]="row?.url" target="_blank">
                {{ row?.[key] }}
              </a>
            } @else {
              {{ row?.[key] | number: '1.0-1' | measureSuffix: key }}
            }
          </td>
        }
      </tr>
    }
  </tbody>
</nz-table>
