import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import type { ChannelGroup } from '@modules/seo-dashboard/definitions';
import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { SeoDashboardArticlesListPieChartComponent } from '../../seo-dashboard-articles-list/components/seo-dashboard-articles-list-pie-chart/seo-dashboard-articles-list-pie-chart.component';

@Component({
  selector: 'app-seo-dashboard-article-list-pie-chart-modal',
  templateUrl: './seo-dashboard-article-list-pie-chart-modal.component.html',
  styleUrl: './seo-dashboard-article-list-pie-chart-modal.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SeoDashboardArticlesListPieChartComponent],
})
export class SeoDashboardArticleListPieChartModalComponent {
  private readonly nzModalData = inject<{ pieChartData: ChannelGroup[] }>(NZ_MODAL_DATA);

  readonly pieChartData = computed(() => this.nzModalData.pieChartData);
}
