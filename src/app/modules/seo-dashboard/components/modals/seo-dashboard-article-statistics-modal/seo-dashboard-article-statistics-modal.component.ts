import { Component, computed, inject } from '@angular/core';
import type { SeoDashboardArticle } from '@modules/seo-dashboard/definitions';
import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { getTableWidth } from '@modules/seo-dashboard/utils';
import { FormatPipeModule } from 'ngx-date-fns';
import { hu } from 'date-fns/locale';
import { NzTableComponent } from 'ng-zorro-antd/table';
import { SeoDashboardArticlesListPieChartComponent } from '@modules/seo-dashboard/components/seo-dashboard-articles-list/components/seo-dashboard-articles-list-pie-chart/seo-dashboard-articles-list-pie-chart.component';
import { TranslatePipe } from '@ngx-translate/core';
import { MeasureSuffixPipe } from '@modules/seo-dashboard/pipes';

@Component({
  selector: 'app-seo-dashboard-article-statistics-modal',
  imports: [FormatPipeModule, NzTableComponent, SeoDashboardArticlesListPieChartComponent, TranslatePipe, MeasureSuffixPipe],
  templateUrl: './seo-dashboard-article-statistics-modal.component.html',
  styleUrl: './seo-dashboard-article-statistics-modal.component.scss',
})
export class SeoDashboardArticleStatisticsModalComponent {
  readonly nzModalData = inject<SeoDashboardArticle>(NZ_MODAL_DATA);

  readonly pieChartData = computed(() => this.nzModalData.channelGroups);

  readonly keys = computed(() => Object.keys(this.nzModalData).filter((key) => key !== 'channelGroups'));
  readonly entries = computed(() => Object.entries(this.nzModalData).filter(([key]) => key === 'channelGroups'));
  readonly tableWidth = computed(() => getTableWidth(this.keys()));

  readonly ArticleListColumnTitles: string[] = [
    'title',
    'users',
    'sessions',
    'pageViews',
    'averageTimeOnPage',
    'bounceRate',
    'onFrontPage',
    'isOriginal',
    'frontPageClickRate',
    'topic',
    'cluster',
    'articleLength',
    'seoScore',
    'column',
    'author',
    'publishDate',
  ];
  protected readonly hu = hu;
}
