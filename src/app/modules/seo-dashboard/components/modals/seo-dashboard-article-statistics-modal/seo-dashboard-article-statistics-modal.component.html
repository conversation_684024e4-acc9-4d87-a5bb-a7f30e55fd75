<app-seo-dashboard-articles-list-pie-chart [pieChartData]="pieChartData()" />

<nz-table [nzBordered]="true" [nzScroll]="{ x: '750px' }" [nzShowPagination]="false" [nzData]="entries()">
  <thead>
  <tr>
    <th [style.width]="'150px'"></th>
    <th></th>
  </tr>
  </thead>
  <tbody>
    @for (key of ArticleListColumnTitles; track key) {
      <tr>
        <td [style.font-weight]="700">{{ 'CMS.seo-dashboard.article-list.' + key | translate }}</td>
        <td>
          @switch (key) {
            @case ('title') {
              <a [href]="nzModalData?.url" target="_blank">
                {{ nzModalData?.[key] }}
              </a>
            }
            @case ('cluster') {
              @if (nzModalData?.[key]) {
                {{ 'CMS.seo-dashboard.article-list.clusters.' + nzModalData?.[key] | translate }}
              } @else {
                N/A
              }
            }
            @case ('publishDate') {
              {{ nzModalData?.[key] | measureSuffix: key | dfnsFormat: 'yyyy-MM-dd' : { locale: hu } }}
            }
            @default {
              {{ nzModalData?.[key] | measureSuffix: key }}
            }
          }
        </td>
      </tr>
    }
  </tbody>
</nz-table>
