import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, type OnInit, signal, ViewContainerRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SeoDashboardApiService } from '@modules/seo-dashboard/api/services/seo-dashboard-api.service';
import { ArticleListColumnTitles } from '@modules/seo-dashboard/constants';
import type { SeoDashboardArticle } from '@modules/seo-dashboard/definitions';
import { getTableWidth, mindArticleDataToArticle } from '@modules/seo-dashboard/utils';
import { hu } from 'date-fns/locale';
import { NzModalService } from 'ng-zorro-antd/modal';
import { finalize, map } from 'rxjs';
import { NzCellFixedDirective, NzTableComponent } from 'ng-zorro-antd/table';
import { TranslatePipe } from '@ngx-translate/core';
import { MeasureSuffixPipe } from '@modules/seo-dashboard/pipes';
import { FormatPipeModule } from 'ngx-date-fns';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { startOfMonth, startOfYear } from 'date-fns';
import { NzDatePickerComponent, NzRangePickerComponent } from 'ng-zorro-antd/date-picker';
import { NzOptionComponent, NzSelectComponent } from 'ng-zorro-antd/select';
import { FormsModule } from '@angular/forms';
import { NzPaginationComponent } from 'ng-zorro-antd/pagination';
import { SeoDashboardArticleStatisticsModalComponent } from '@modules/seo-dashboard/components/modals/seo-dashboard-article-statistics-modal/seo-dashboard-article-statistics-modal.component';

@Component({
  selector: 'app-seo-dashboard-articles-list',
  templateUrl: './seo-dashboard-articles-list.component.html',
  styleUrl: './seo-dashboard-articles-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NzTableComponent,
    TranslatePipe,
    NzCellFixedDirective,
    MeasureSuffixPipe,
    FormatPipeModule,
    NzButtonComponent,
    NzDatePickerComponent,
    NzOptionComponent,
    NzRangePickerComponent,
    NzSelectComponent,
    FormsModule,
    NzPaginationComponent,
  ],
})
export class SeoDashboardArticlesListComponent implements OnInit {
  private readonly seoDashboardApiService = inject(SeoDashboardApiService);
  private readonly modal = inject(NzModalService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly viewContainerRef = inject(ViewContainerRef);

  readonly ArticleListColumnTitles = ArticleListColumnTitles;
  readonly hu = hu;
  readonly ranges = {
    'Ez év': [startOfYear(new Date()), new Date()],
    'E hónap': [startOfMonth(new Date()), new Date()],
  } as Record<string, [Date, Date]>;
  readonly segments = [
    {
      label: 'Összes',
      value: 'total',
    },
    {
      label: 'Mobil',
      value: 'mobile',
    },
    {
      label: 'Asztali',
      value: 'desktop',
    },
  ] as const;

  readonly colWidths = ['250px', '60px', '80px', '60px', '60px', '60px', '150px', '80px'];

  readonly segment = signal<(typeof this.segments)[number]['value']>(this.segments[0]?.value);
  readonly dateRange = signal<[Date, Date]>(this.ranges['Ez év']);
  readonly isLoading = signal(false);
  readonly hasArticleListData = signal(true);
  readonly articleListData = signal<SeoDashboardArticle[]>([]);
  readonly total = signal(0);

  readonly tableWidth = computed(() => getTableWidth(this.ArticleListColumnTitles));
  readonly page = signal(1);

  ngOnInit(): void {
    this.loadData();
  }

  onDateChange(newValue: [Date, Date]): void {
    this.dateRange.set(newValue);
    this.loadData();
  }

  onSegmentChange(newValue: (typeof this.segments)[number]['value']): void {
    this.segment.set(newValue);
    this.loadData();
  }

  onPageChange(newValue: number): void {
    this.page.set(newValue);
    this.loadData();
  }

  private loadData(): void {
    this.isLoading.set(true);
    this.seoDashboardApiService
      .getArticleList({
        segment: this.segment(),
        field: 'title',
        sort: 'users',
        sortDirection: 'desc',
        page: this.page(),
        limit: 10,
        dateFrom: this.dateRange()[0],
        dateTo: this.dateRange()[1],
      })
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        map(({ data, total }) => ({
          data: data.map(mindArticleDataToArticle),
          total,
        })),
        finalize(() => this.isLoading.set(false))
      )
      .subscribe(({ data, total }) => {
        if (!data?.length) {
          this.hasArticleListData.set(false);
          return;
        }
        this.articleListData.set(data);
        this.total.set(total);
      });
  }

  onDetailsOpen(article: SeoDashboardArticle): void {
    const modal = this.modal.create({
      nzContent: SeoDashboardArticleStatisticsModalComponent,
      nzTitle: article.title,
      nzViewContainerRef: this.viewContainerRef,
      nzWidth: 800,
      nzData: {
        ...article,
      },
      nzFooter: [
        {
          label: 'Rendben',
          onClick(): void {
            modal.destroy();
          },
          type: 'primary',
        },
      ],
    });
  }
}
