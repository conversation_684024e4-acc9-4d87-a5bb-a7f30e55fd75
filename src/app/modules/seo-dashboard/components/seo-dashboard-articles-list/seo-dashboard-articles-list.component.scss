:host {
  display: block;

  .ant-table-cell-fix-left, .ant-table-cell-fix-right {
    z-index: 1;
  }
}

h2 {
  margin-bottom: 15px;
}

nz-select {
  width: 250px;
}

nz-pagination {
  margin: 16px 0 32px;
}

.filter-fields {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 10px;
}

.article-list {
  &-container {
    position: relative;
  }

  &-table {
    th {
      background-color: #f6f8fc;
    }
  }
}

td, th {
  padding: 5px 12px;
}

.loading-layer {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  backdrop-filter: blur(5px);
}
