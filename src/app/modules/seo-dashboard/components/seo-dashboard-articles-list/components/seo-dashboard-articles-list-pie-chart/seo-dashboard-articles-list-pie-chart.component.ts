import { ChangeDetectionStrategy, Component, ElementRef, inject, input, type OnDestroy, type OnInit, viewChild } from '@angular/core';
import { SeoDashboardChartService } from '@modules/seo-dashboard/api/services/seo-dashboard-chart.service';
import type { ChannelGroup } from '@modules/seo-dashboard/definitions';
import { Chart } from 'chart.js';

@Component({
  selector: 'app-seo-dashboard-articles-list-pie-chart',
  templateUrl: './seo-dashboard-articles-list-pie-chart.component.html',
  styleUrl: './seo-dashboard-articles-list-pie-chart.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SeoDashboardArticlesListPieChartComponent implements OnInit, OnDestroy {
  private readonly seoDashboardChartService = inject(SeoDashboardChartService);

  private readonly chartContainer = viewChild<ElementRef>('pieChart');

  readonly pieChartData = input.required<ChannelGroup[]>();
  readonly isSmall = input(false);

  private chart: Chart;

  ngOnInit(): void {
    this.createChart();
  }

  createChart(): void {
    this.chart = new Chart(this.chartContainer().nativeElement, {
      type: 'pie',
      data: this.seoDashboardChartService.transformPieChartData(this.pieChartData()),
      options: {
        plugins: {
          legend: {
            display: false,
          },
          tooltip: {
            enabled: !this.isSmall(),
          },
        },
      },
    });
  }

  ngOnDestroy(): void {
    this.chart.destroy();
  }
}
