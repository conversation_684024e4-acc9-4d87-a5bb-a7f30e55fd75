<div class="filter-fields">
  <label for="data-range">Adatok intervalluma:</label>
  <nz-range-picker
    id="date-range"
    class="date-range-input"
    nzFormat="yyyy-MM-dd"
    [ngModel]="dateRange()"
    [nzAllowClear]="false"
    [nzPlaceHolder]="['Tól', 'Ig']"
    [nzRanges]="ranges"
    (ngModelChange)="onDateChange($event)"
  />
  <label for="segment">Szegmens:</label>
  <nz-select
    id="segment"
    [nzAllowClear]="false"
    [ngModel]="segment()"
    (ngModelChange)="onSegmentChange($event)"
  >
    @for (item of segments; track item.value) {
      <nz-option [nzValue]="item.value" [nzLabel]="item.label" />
    }
  </nz-select>
</div>

<div class="article-list-container">
  @if (isLoading()) {
    <div class="loading-layer">Adatok betöltése</div>
  }

  @if (hasArticleListData()) {
    <nz-table #articleList class="article-list-table" [nzBordered]="true" [nzScroll]="{ x: tableWidth() + 'px' }" [nzData]="articleListData()" [nzShowPagination]="false">
      <thead>
        <tr>
          @for (columnTitle of ArticleListColumnTitles; track columnTitle) {
            <th [nzLeft]="$first"  [style.width]="colWidths[$index]">{{ 'CMS.seo-dashboard.article-list.' + columnTitle | translate }}</th>
          }
          <th nzRight [style.width]="'100px'"></th>
        </tr>
      </thead>
      <tbody>
        @for (row of articleList?.data; track row.title) {
          <tr>
            @for (key of ArticleListColumnTitles; track key) {
              <td [nzLeft]="$first">
                @switch (key) {
                  @case ('title') {
                    <a [href]="row?.url" target="_blank">
                      {{ row?.[key] }}
                    </a>
                  }
                  @case ('cluster') {
                    @if (row?.[key]) {
                      {{ 'CMS.seo-dashboard.article-list.clusters.' + row?.[key] | translate }}
                    } @else {
                      N/A
                    }
                  }
                  @case ('publishDate') {
                    {{ row?.[key] | measureSuffix: key | dfnsFormat: 'yyyy-MM-dd' : { locale: hu } }}
                  }
                  @default {
                    {{ row?.[key] | measureSuffix: key }}
                  }
                }
              </td>
            }
            <td nzRight><button class="w-100" nz-button nzType="primary" (click)="onDetailsOpen(row)">További részletek</button></td>
          </tr>
        }
      </tbody>
    </nz-table>
    <nz-pagination [nzPageIndex]="page()" [nzTotal]="total()" (nzPageIndexChange)="onPageChange($event)" />
  } @else {
    <span>Nincs megjeleníthető adat</span>
  }
</div>
