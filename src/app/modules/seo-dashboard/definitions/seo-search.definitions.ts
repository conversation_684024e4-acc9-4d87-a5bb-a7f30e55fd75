type SeoSearchSegment = 'total' | 'desktop' | 'mobile';

export interface SeoSearchArticleRankingListParams {
  /** Determines what segment is requested in data */
  segment: SeoSearchSegment;
  /** Start date of the query */
  dateFrom: Date;
  /** End date of the query */
  dateTo: Date;
}

export interface SeoSearchAuthorStatisticsParams extends SeoSearchArticleRankingListParams {
  sortDirection: 'asc' | 'desc';
}

export interface SeoSearchMindArticleParams extends SeoSearchAuthorStatisticsParams {
  page: number;
  limit: number;
  /** Field for sorting */
  sort: string;
  /** Field to search in */
  field: string;
}
