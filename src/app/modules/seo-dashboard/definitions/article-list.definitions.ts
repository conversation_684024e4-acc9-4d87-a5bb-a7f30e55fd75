import { ChannelGroup } from './author-statistics.definitions';

export interface SeoDashboardMindArticle {
  title: string;
  url: string;
  users: number;
  sessions: number;
  pageViews: number;
  averageTimeOnPage: number;
  bounceRate: number;
  onFrontPage: boolean;
  isOriginal: boolean;
  frontPageClickRate: number;
  articleLength: number;
  reactions: number;
  reaches: number;
  comments: number;
  shares: number;
  seoScore: number;
  polarityIndex: number;
  publishDate: string;
  column: string;
  topic: string;
  slug: string;
  cluster: string;
  emotion: string;
  channelGroups: ChannelGroup;
  author: string;
}

export interface SeoDashboardArticle extends Omit<SeoDashboardMindArticle, 'publishDate' | 'onFrontPage' | 'isOriginal'> {
  publishDate: Date;
  onFrontPage: string;
  isOriginal: string;
}

export interface SeoDashboardArticlesResponse {
  data: SeoDashboardMindArticle[];
  total: number;
}
