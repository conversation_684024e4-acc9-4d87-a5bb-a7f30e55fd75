export interface AuthorStatistics {
  articleCount: number;
  authorProfile: string;
  mainEmotion: string;
  averageUsers: number;
  averagePageViews: number;
  averageCtr: number;
  averageTimeOnPage: number;
  averageBounceRate: number;
  averageSeoScore: number;
  averageLength: number;
  averagePolarityIndex: number;
  articleCountDetails: ArticleCountDetail[];
  channelGroups: ChannelGroup[];
  leastViewedArticle: ArticleView;
  mostViewedArticle: ArticleView;
  name: string;
  topic: string;
}

export interface ArticleCountDetail {
  pageViews: number;
  statDate: string;
  topics: Topic[];
}

export interface Topic {
  colorCode: string;
  name: string;
  slug: string;
  value: number;
}

export interface ArticleView {
  rowTitle?: string;
  averageTimeOnPage: number;
  bounceRate: number;
  channelGroupDirect: number;
  channelGroupOrganic: number;
  channelGroupReferral: number;
  channelGroupSocial: number;
  ctr: number;
  pageViews: number;
  reactions: number;
  seoScore: number;
  shares: number;
  title: string;
  url: string;
}

export interface ChannelGroup {
  colorCode: string;
  source: string;
  value?: number;
  name?: string;
}
