import { Injectable } from '@angular/core';
import { ContentType } from '@trendency/kesma-ui';
import { ApiService } from '@core/services/api.service';
import { TagGeneratorBackendResult, TagGeneratorRequest, TagGeneratorTag } from '../definitions/tag-generator-definitions';
import { BehaviorSubject, Subject } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class TagGeneratorService {
  public request: TagGeneratorRequest = { title: '', lead: null, description: '', contentType: ContentType.ARTICLE, id: '' };
  public existingTagSelected$ = new Subject<TagGeneratorTag[]>();
  public generatedTagSelected$ = new Subject<TagGeneratorTag[]>();
  public error = new Subject<HttpErrorResponse>();
  public isLoadingSubject = new BehaviorSubject<boolean>(false);
  private readonly dataSubject = new Subject<TagGeneratorTag[]>();

  constructor(private readonly apiService: ApiService) {}

  setData(newData: TagGeneratorTag[]): void {
    this.dataSubject.next(newData);
  }

  getData(): Subject<TagGeneratorTag[]> {
    return this.dataSubject;
  }

  updateRequest(title: string, lead: string | null, description: string, contentType: ContentType, id: string): void {
    const pureStringDescription = this.removeHTMLTags(description).replace(/ {2,}/g, ' ');
    this.request = { title, lead, description: pureStringDescription, contentType, id };
  }

  public setLoadingState(isLoading: boolean): void {
    this.isLoadingSubject.next(isLoading);
  }

  public generateTags(): void {
    if (this.isLoadingSubject.value === false) {
      this.setLoadingState(true);
      this.apiService.getGeneratedTags(this.request).subscribe(
        (data: TagGeneratorBackendResult) => {
          this.setData(data.data);
          this.setLoadingState(false);
          this.error.next(null);
        },
        (error) => {
          this.setData(null);
          this.error.next(error);
          this.setLoadingState(false);
        }
      );
    }
  }

  private removeHTMLTags(input: string): string {
    return input
      .replace(/&nbsp;/g, ' ')
      .replace(/<[^>]+>/g, ' ')
      .trim();
  }
}
