import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { TagGeneratorTag } from '../definitions/tag-generator-definitions';
import { TagGeneratorService } from '../services/tag-generator.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { SelectSourceValueType } from '@shared/modules/form-controls/definitions/form-controls.definitions';
import { TranslateService } from '@ngx-translate/core';

const MIN_GENERATED_TAG_COUNT = 3;
@Component({
  selector: 'app-tag-generator',
  templateUrl: './tag-generator.component.html',
  styleUrls: ['./tag-generator.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class TagGeneratorComponent implements OnInit, OnDestroy {
  @Input() maxTagCount: number = Infinity;
  @Input() selectInputValues: SelectSourceValueType | SelectSourceValueType[];
  minGeneratedTagCount = MIN_GENERATED_TAG_COUNT;
  data$ = this.tagGeneratorService.getData();
  errorMessages = [];
  isLoading = false;
  selectedTags: TagGeneratorTag[] = [];
  error: HttpErrorResponse;
  private destroySubject = new Subject<void>();
  constructor(
    private tagGeneratorService: TagGeneratorService,
    private cd: ChangeDetectorRef,
    private translate: TranslateService
  ) {}

  ngOnInit(): void {
    this.tagGeneratorService.isLoadingSubject.pipe(takeUntil(this.destroySubject)).subscribe((isLoading) => {
      this.isLoading = isLoading;
      this.cd.detectChanges();
    });
    this.tagGeneratorService.existingTagSelected$.pipe(takeUntil(this.destroySubject)).subscribe((tags) => {
      this.selectedTags = tags;
      this.cd.detectChanges();
    });
    this.tagGeneratorService.error.pipe(takeUntil(this.destroySubject)).subscribe((error) => {
      this.error = error;
      this.errorMessages = [];
      if (this.error?.status === 422) {
        const errorList = error?.error?.errors;
        if (typeof errorList === 'object') {
          Object.keys(errorList).forEach((key) => {
            const translatedKey = this.translate.instant(`dynamicContentTagger.${key}`);
            this.errorMessages.push(`${translatedKey}: ${errorList[key]}`);
          });
        }
      } else if (this.error?.status === 400) {
        this.errorMessages = [
          'Nem sikerült címkét generálni. Ügyeljen rá hogy a cikk szövegtörzse + címe megfelelő mennyiségű szöveget tartalmazzon és próbálja újra!',
        ];
      } else {
        if (this.error?.error?.data?.message?.includes('timeout')) {
          this.errorMessages = ['A Címkéző API jelenleg nem elérhető, ezidő alatt a publikálás címke nélkül is megengedett'];
        } else {
          this.errorMessages = ['Hiba történt a generáláskor. Próbálja meg újra'];
        }
      }
      this.cd.detectChanges();
    });
  }

  ngOnDestroy(): void {
    this.destroySubject.next();
    this.destroySubject.complete();
    this.tagGeneratorService.generatedTagSelected$.next([]);
  }

  selectTag(tag: TagGeneratorTag): void {
    const valueArray = Array.from(this.selectInputValues as SelectSourceValueType[]);
    if (
      valueArray.length < this.maxTagCount &&
      this.selectedTags.length < this.maxTagCount &&
      !valueArray.some((selectedTag) => selectedTag['title'] === tag.title)
    ) {
      this.selectedTags.push(tag);
      this.tagGeneratorService.generatedTagSelected$.next(this.selectedTags);
    }
  }
}
