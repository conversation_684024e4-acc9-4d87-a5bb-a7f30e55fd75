<div class="tag-generator">
  <div class="form-control-container form-control-label tag-generator-title">Generált címkék</div>
  <div *ngIf="isLoading" class="tag-generator-text">
    <p class="tag-generator-text-general">Címkék generálása folyamatban...</p>
  </div>
  <ng-container *ngIf="data$ | async as data">
    <div *ngIf="!isLoading && data.length > 0 && selectedTags.length < minGeneratedTagCount" class="tag-generator-text">
      <p class="tag-generator-text-warning">Válassz minimum {{ minGeneratedTagCount }} gener<PERSON><PERSON> cím<PERSON>t, összesen {{ maxTagCount }} használható.</p>
    </div>
    <div
      *ngIf="!isLoading && data.length > 0 && selectedTags.length === minGeneratedTagCount && selectInputValues.length < maxTagCount"
      class="tag-generator-text"
    >
      <p class="tag-generator-text-warning">Hozzáadhatsz még maximum {{ maxTagCount - +selectInputValues.length }} címkét.</p>
    </div>
    <div *ngIf="!isLoading && data.length > 0 && selectInputValues.length === maxTagCount" class="tag-generator-text">
      <p class="tag-generator-text-warning">Elérted a maximális címke limitet, törölj a meglévők közül új hozzáadásához.</p>
    </div>
    <div *ngIf="!isLoading && !error && data.length > 0" class="tag-generator-tags">
      <div *ngFor="let tag of data" (click)="selectTag(tag)" class="tag" [attr.test-key]="tag.title">{{ tag.title }}</div>
    </div>
  </ng-container>

  <div *ngIf="!isLoading && error" class="tag-generator-text">
    <div>
      @for (errorMessage of errorMessages; track $index) {
        <p class="tag-generator-text-error">{{ errorMessage }}</p>
      }
    </div>
  </div>
</div>
