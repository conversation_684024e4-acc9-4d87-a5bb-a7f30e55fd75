export type BackendComment = {
  answerCount: string;
  article: any;
  articlePublishDate: string;
  columnId: string;
  columSlug: string;
  createdAt: string;
  dislikeCount: string;
  id: string;
  isLeader: string;
  isActive: string;
  isUpdated: string;
  likeCount: string;
  parent: any;
  rowActions?: [];
  text: string;
  updatedAt?: string;
  user: any;
  lastReportStatus: 'allowed' | 'denied' | 'waiting';
};
