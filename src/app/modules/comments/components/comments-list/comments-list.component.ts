import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UtilsService } from '@external/utils';

import { TranslateService } from '@ngx-translate/core';
import { DataTableEventType, IDataTableColumnInfo } from '@shared/definitions/data-table.definitions';
import { ContentPreviewService } from '@shared/services/content-preview.service';
import { SharedService } from '@shared/services/shared.service';
import { NzModalService } from 'ng-zorro-antd/modal';
import { BehaviorSubject, catchError, map, Observable, tap } from 'rxjs';
import { IBaseContentConfig } from 'src/app/core/core.definitions';
import { ApiService } from 'src/app/core/services/api.service';
import { BaseContentComponent } from 'src/app/modules/base-content.component';
import { processBackendCommentsResponse } from '../../utils/backend-comments';
import { DomainService } from '../../../../core/modules/admin/services/domain.service';

@Component({
  selector: 'app-comments-list',
  templateUrl: './comments-list.component.html',
  styleUrls: ['./comments-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class CommentsListComponent extends BaseContentComponent implements OnInit {
  @ViewChild('isLeaderTemplate', { static: true }) public isLeaderTemplate: TemplateRef<any>;
  @ViewChild('userTemplate', { static: true }) public userTemplate: TemplateRef<any>;
  @ViewChild('createdAtTemplate', { static: true }) public createdAtTemplate: TemplateRef<any>;
  @ViewChild('commentTextTemplate', { static: true }) public commentTextTemplate: TemplateRef<any>;
  @ViewChild('commentReportStatus', { static: true }) public commentReportStatus: TemplateRef<any>;

  firstInit = true;
  isLoading$ = new BehaviorSubject<boolean>(false);

  data?: any;

  constructor(
    public apiService: ApiService,
    public route: ActivatedRoute,
    public translate: TranslateService,
    public router: Router,
    public modalService: NzModalService,
    public sharedService: SharedService,
    public contentPreviewService: ContentPreviewService,
    public utilsService: UtilsService,
    public changeDetectorRef: ChangeDetectorRef,
    private readonly domainService: DomainService
  ) {
    super(
      apiService,
      route,
      translate,

      router,
      modalService,
      sharedService,
      contentPreviewService,
      utilsService
    );
  }

  ngOnInit(): void {
    this.route.data.subscribe((data) => {
      this.data = data.list;
      if (this.firstInit) {
        this.initTableDataAndConfig(this.data);
        this.firstInit = false;
      }
      this.changeDetectorRef.markForCheck();
    });
  }

  list(params: any) {
    this.isLoading$.next(true);
    const isDeleted = params?.status === 'deleted';
    return this.apiService.getAllComments({ params }).pipe(
      map((res) => processBackendCommentsResponse(res, isDeleted)),
      tap((data) => {
        this.isLoading$.next(false);
        this.changeDetectorRef.markForCheck();
      })
    );
  }

  get config() {
    const _config: IBaseContentConfig = {
      contentType: 'comment',
      contentListType: 'comments',
      previewPageRoute: [],
      tableTitle: this.translate.instant('CMS.comments'),
      editorType: 'editor',
      dataColumns: this.getDataColumns(),
    };
    return _config;
  }

  private getDataColumns(): IDataTableColumnInfo[] {
    const dataColumns = [
      {
        key: 'text',
        property: 'text',
        title: 'commenting.comment',
        customTemplate: this.commentTextTemplate,
      },
      {
        key: 'isLeader',
        property: 'isLeader',
        title: 'commenting.isLeader',
        customTemplate: this.isLeaderTemplate,
      },
      {
        key: 'createdAt',
        property: 'createdAt',
        title: 'commenting.createdAt',
        customTemplate: this.createdAtTemplate,
      },
      {
        key: 'likeCount',
        property: 'likeCount',
        title: 'commenting.likeCount',
      },
      {
        key: 'dislikeCount',
        property: 'dislikeCount',
        title: 'commenting.dislikeCount',
      },
      {
        key: 'article',
        property: 'article.title',
        title: 'article',
      },
      {
        key: 'user',
        property: 'user',
        title: 'commenting.commenter',
        customTemplate: this.userTemplate,
      },
    ];

    if (this.domainService.isBorsOnline()) {
      dataColumns.push({
        key: 'lastReportStatus',
        property: 'lastReportStatus',
        title: 'commenting.lastReportStatus',
        customTemplate: this.commentReportStatus,
      });
    }

    return dataColumns;
  }

  private handleApiCall(request: Observable<any>) {
    return request.pipe(
      catchError((err) => this.handleErrors(err)),
      tap(() => {
        this.sharedService.showNotification('success', 'CMS.commenting.action-success');
        this.refreshTableData(null);
      })
    );
  }

  onFollower({ id }) {
    return this.handleApiCall(this.apiService.commentToFollower(id));
  }
  onLeader({ id }) {
    return this.handleApiCall(this.apiService.commentToLeader(id));
  }

  onShow({ id }) {
    return this.handleApiCall(this.apiService.showComment(id));
  }
  onHide({ id }) {
    return this.handleApiCall(this.apiService.hideComment(id));
  }
  onDelete({ id }) {
    return this.handleApiCall(this.apiService.deleteComment(id));
  }
  restore(id: string) {
    return this.handleApiCall(this.apiService.restoreComment(id));
  }
  onApprove({ id }) {
    return this.handleApiCall(this.apiService.approveComment(id));
  }
  onDeny({ id }) {
    return this.handleApiCall(this.apiService.denyComment(id));
  }

  public getRowActionModuleConfigs() {
    return [
      {
        key: DataTableEventType.UPDATE,
        eventHandler: this.edit.bind(this),
        buttonType: 'primary',
        iconName: 'edit',
        label: 'edit',
        newTabOption: true,
      },
      {
        key: DataTableEventType.COMMENT_TO_LEADER,
        eventHandler: this.onLeader.bind(this),
        buttonType: 'primary',
        iconName: 'pushpin',
        label: 'COMMENT_TO_LEADER',
      },
      {
        key: DataTableEventType.COMMENT_TO_APPROVED,
        eventHandler: this.onApprove.bind(this),
        buttonType: 'primary',
        iconName: 'check-circle',
        label: 'COMMENT_TO_APPROVED',
      },
      {
        key: DataTableEventType.COMMENT_TO_DENIED,
        eventHandler: this.onDeny.bind(this),
        buttonType: 'danger',
        iconName: 'close-circle',
        label: 'COMMENT_TO_DENIED',
      },
      {
        key: DataTableEventType.COMMENT_TO_FOLLOWER,
        eventHandler: this.onFollower.bind(this),
        buttonType: 'danger',
        iconName: 'comment',
        label: 'COMMENT_TO_FOLLOWER',
      },
      {
        key: DataTableEventType.COMMENT_TO_SHOW,
        eventHandler: this.onShow.bind(this),
        buttonType: 'default',
        iconName: 'check',
        label: 'COMMENT_TO_SHOW',
      },
      {
        key: DataTableEventType.COMMENT_TO_HIDE,
        eventHandler: this.onHide.bind(this),
        buttonType: 'danger',
        iconName: 'eye-invisible',
        label: 'COMMENT_TO_HIDE',
      },
      {
        key: DataTableEventType.DELETE,
        eventHandler: this.onDelete.bind(this),
        buttonType: 'danger',
        iconName: 'delete',
        label: 'COMMENT_TO_DELETE',
      },
      {
        key: DataTableEventType.RESTORE,
        eventHandler: this.onRestore.bind(this),
        buttonType: 'danger',
        iconName: 'redo',
        label: 'COMMENT_TO_RESTORE',
      },
    ];
  }
}
