<app-data-table
  *ngIf="dataTableData"
  [loading]="isLoading$ | async"
  [tableData]="dataTableData"
  [tableConfig]="dataTableConfig"
  [enableSorting]="true"
  (rowActionTriggered)="handleAction($event, 'rowActions')"
  (globalActionTriggered)="handleAction($event, 'globalActions')"
  (queryParamsChanged)="refreshOrderedTableData($event)"
  class="data-table"
>
</app-data-table>

<ng-template #isLeaderTemplate let-text="data">
  <div *ngIf="text === '1'" class="leader-tag">
    <i nz-icon nzType="pushpin" nzTheme="fill"></i>
    {{ 'CMS.commenting.leader' | translate }}
  </div>
  <div *ngIf="text === '0'" class="follower-tag">
    {{ 'CMS.commenting.regular' | translate }}
  </div>
</ng-template>

<ng-template #userTemplate let-user="data">
  <div class="user-tag">
    <i nz-icon nzType="user" nzTheme="outline"></i>
    {{ user.name }}
  </div>
</ng-template>

<ng-template #createdAtTemplate let-date="data">
  {{ date | dateFormat: 'local' : 'yyyy.MM.dd HH:mm:ss' }}
</ng-template>

<ng-template #commentTextTemplate let-text="data">
  <app-comment-text [text]="text"></app-comment-text>
</ng-template>

<ng-template #commentReportStatus let-status="data">
    {{ ('CMS.commenting.' + status) | translate }}
</ng-template>
