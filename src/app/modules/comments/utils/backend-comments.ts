import { BackendComment } from '../definitions/comment.definition';
import { DataTableEventType } from '@shared/definitions/data-table.definitions';

/**
 * This mapper function is needed to add the row actions next to the items in the list.
 * BE Currently does not send these so we have to manually add them.
 * @param res BE Response
 * @param isDeleted If we query the deleted items or not
 * @returns
 */
export const processBackendCommentsResponse = (res: any, isDeleted = false): any => {
  res.meta = {
    ...res.meta,
    globalActions: ['list', 'delete'],
  };

  res.data = (res.data as BackendComment[]).map((item) => {
    if (item.lastReportStatus === 'waiting') {
      return {
        ...item,
        rowActions: [DataTableEventType.COMMENT_TO_APPROVED, DataTableEventType.COMMENT_TO_DENIED],
      };
    }

    if (item.lastReportStatus === 'denied') {
      return {
        ...item,
        rowActions: [DataTableEventType.DELETE],
      };
    }

    let actions = ['delete'];
    if (item.isActive === '1') {
      actions.push('comment_to_hide');
    } else {
      actions.push('comment_to_show');
    }
    if (item.isLeader === '1') {
      actions.push('comment_to_follower');
    } else {
      actions.push('comment_to_leader');
    }

    if (isDeleted) {
      actions.push('restore');
    }
    return {
      ...item,
      rowActions: actions,
    };
  });
  return res;
};
