import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, viewChild } from '@angular/core';
import { BaseContentComponent } from '@modules/base-content.component';
import { ActivatedRoute } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { IBaseContentConfig } from '@core/core.definitions';
import { CustomTemplateType, IDataTableColumnInfo } from '@shared/definitions/data-table.definitions';
import { DataTableModule } from '@shared/modules/data-table/data-table.module';
import { NzTagComponent } from 'ng-zorro-antd/tag';
import { map } from 'rxjs/operators';
import { extendWithActions } from '@modules/user-notifications/user-notifications.utils';

@Component({
  selector: 'app-user-notifications',
  imports: [DataTableModule, NzTagComponent],
  templateUrl: './user-notifications.component.html',
  styleUrl: './user-notifications.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UserNotificationsComponent extends BaseContentComponent implements OnInit {
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly destroyRef = inject(DestroyRef);
  private readonly portalsTemplate = viewChild('portalsTemplate');

  ngOnInit(): void {
    this.activatedRoute.data.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(({ list }) => this.initTableDataAndConfig(list));
  }

  get config(): IBaseContentConfig {
    const config: IBaseContentConfig = {
      contentType: 'user-notification',
      contentListType: 'user-notifications',
      previewPageRoute: [],
      tableTitle: this.translate.instant('CMS.user_notification'),
      editorType: 'modal',
      dataColumns: this.getDataColumns(),
      formApiCall: this.getFormInfo.bind(this),
    };
    this.formApiCall = this.getFormInfo.bind(this);
    return config;
  }

  list(params: any) {
    return this.apiService.notifications.list({ params }).pipe(map(extendWithActions));
  }

  create(data: any) {
    return this.apiService.notifications.create(data);
  }

  update(id: string, data: any) {
    return this.apiService.notifications.update(id, data);
  }

  getFormInfo(id?: string) {
    return this.apiService.notifications.formInfo(id);
  }

  publish(id: string) {
    return this.apiService.notifications.activate(id);
  }

  unpublish(id: string) {
    return this.apiService.notifications.inactivate(id);
  }

  private getDataColumns(): IDataTableColumnInfo[] {
    return [
      {
        key: 'title',
        property: 'title',
        title: 'title',
      },
      {
        key: 'type',
        property: 'type',
        title: 'type',
      },
      {
        key: 'isInstant',
        property: 'isInstant',
        title: 'isInstant',
        customTemplateType: CustomTemplateType.IS_ACTIVE,
      },
      {
        key: 'isActive',
        property: 'isActive',
        title: 'isActive',
        customTemplateType: CustomTemplateType.IS_ACTIVE,
      },
      {
        key: 'createdAt',
        property: 'createdAt',
        title: 'createdAt',
        customTemplateType: CustomTemplateType.DATE_TIME,
      },
      {
        key: 'portals',
        property: 'portals',
        title: 'portals',
        customTemplate: this.portalsTemplate(),
      },
    ];
  }

  handleQueryParamsChanged(queryParams?: { [p: string]: any }): void {
    this.refreshTableData(queryParams);
  }
}
