import { Routes } from '@angular/router';
import { UserNotificationsComponent } from '@modules/user-notifications/user-notifications.component';
import { userNotificationsResolver } from '@modules/user-notifications/user-notifications.resolver';

export const userNotificationRoutes: Routes = [
  {
    path: '',
    component: UserNotificationsComponent,
    resolve: { list: userNotificationsResolver },
    pathMatch: 'full',
  },
  {
    path: 'editor',
    loadChildren: () => import('src/app/item-editor-page/item-editor-page.module').then((m) => m.ItemEditorPageModule),
  },
];
