<app-data-table
  [loading]="false"
  [tableData]="dataTableData"
  [tableConfig]="dataTableConfig"
  [enableActions]="true"
  (rowActionTriggered)="handleAction($event, 'rowActions')"
  (globalActionTriggered)="handleAction($event, 'globalActions')"
  (queryParamsChanged)="handleQueryParamsChanged($event)"
>
</app-data-table>

<ng-template #portalsTemplate let-portals="data">
  @if(portals === 'all') {
    <nz-tag>MIND</nz-tag>
  } @else {
    @for (portal of portals;track portal.id) {
      <nz-tag>{{ portal.name }}</nz-tag>
    }
  }
</ng-template>
