import { DataTableEventType } from '@shared/definitions/data-table.definitions';
import { ApiListResult } from '@trendency/kesma-ui';
import { UserNotification } from '@core/services/api/notifications/notification.definitions';

export function extendWithActions({ data, meta }: ApiListResult<UserNotification>): ApiListResult<UserNotification> {
  return {
    meta: {
      ...meta,
      globalActions: [DataTableEventType.CREATE],
    },
    data: data.map((item) => ({
      ...item,
      rowActions: [DataTableEventType.UPDATE, item.isActive ? DataTableEventType.INACTIVATE : DataTableEventType.ACTIVATE],
    })),
  };
}
