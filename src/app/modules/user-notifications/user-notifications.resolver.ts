import { ResolveFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { ApiService } from '@core/services/api.service';
import { catchError, map } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { ApiListResult } from '@trendency/kesma-ui';
import { UserNotification } from '@core/services/api/notifications/notification.definitions';
import { extendWithActions } from '@modules/user-notifications/user-notifications.utils';

export const userNotificationsResolver: ResolveFn<ApiListResult<UserNotification>> = (route) => {
  const apiService = inject(ApiService);
  const router = inject(Router);
  const searchTerm = route.queryParams['global_filter'];
  return apiService.notifications.list({ global_filter: searchTerm ?? '' }).pipe(
    map(extendWithActions),
    catchError((error) => {
      router.navigate(['/', 'admin', 'error'], { state: { errorResponse: JSON.stringify(error) } }).then();
      return throwError(() => error);
    })
  );
};
