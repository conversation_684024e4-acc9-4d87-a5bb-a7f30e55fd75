import { ExchangeBoxDataType, ExchangeBoxType } from '@trendency/kesma-ui';
import { MediaStoreResult } from '../../media-store/media-store.definitions';
import { ExplicitArticleList } from './components/explicit-article-list-editor/definitions/explicit-article-list.definitions';
import { LayoutConfigurationArticle } from './layout-configuration.definitions';
import { BasicLayoutElementPreview, LayoutElementContent, LayoutElementContentType } from './layout-core.definitions';

export interface LayoutElementContentArticle extends LayoutElementContent {
  contentType: LayoutElementContentType.ARTICLE;
  secondaryContentType?: LayoutElementContentType;
  styleId: number;
  hasTitle: boolean;
  hasLead: boolean;
  hasImg?: boolean; // it's not present always in reality -> mapped later
  hasImage?: boolean; // FIXME mapped to hasImg
  hasDate?: boolean;
  hasReadTime?: boolean;
  canHaveCustomTag?: boolean; // TODO: set all non domain specific prop to optional
  configurable: true;
  fontSize?: number;
  withHorizontalSeparator?: boolean;
  withVerticalSeparator?: boolean;
  hasLine?: boolean;
  isLight?: boolean;
  hasBackground?: boolean;
  hasOutline?: boolean;
}

export interface LayoutElementContentDataBank extends LayoutElementContent {
  contentType: LayoutElementContentType.DATA_BANK;
  styleId: number;
  configurable: true;
}

export interface LayoutElementContentImageMapList extends LayoutElementContent {
  contentType: LayoutElementContentType.IMAGE_MAP_LIST;
  configurable: false;
  styleId: number;
}

export interface LayoutElementContentDrawnMapList extends LayoutElementContent {
  contentType: LayoutElementContentType.DRAWN_MAP_LIST;
  configurable: false;
  styleId: number;
}

export interface LayoutElementContentUpcomingMatches extends LayoutElementContent {
  contentType: LayoutElementContentType.UPCOMING_MATCHES;
  configurable: true;
}

export interface LayoutElementContentChampionshipTable extends LayoutElementContent {
  contentType: LayoutElementContentType.CHAMPIONSHIP_TABLE;
  configurable: true;
}

export interface LayoutElementContentHelloBudapest extends LayoutElementContent {
  contentType: LayoutElementContentType.HELLO_BUDAPEST;
  configurable: true;
  styleId?: number;
  hasTitle: boolean;
  hasLead: boolean;
  hasImg?: boolean; // it's not present always in reality -> mapped later
  canHaveCustomTag?: boolean; // TODO: set all non domain specific prop to optional
}

export interface LayoutElementContentShortNews extends LayoutElementContent {
  contentType: LayoutElementContentType.SHORT_NEWS;
  styleId: number;
  hasTitle: boolean;
  hasLead: boolean;
  hasImg: boolean;
  hasDate?: boolean;
  hasReadTime?: boolean;
  canHaveCustomTag?: boolean; // TODO: set all non domain specific prop to optional
  configurable: true;
}

export interface LayoutElementContentFastNews extends LayoutElementContent {
  contentType: LayoutElementContentType.FAST_NEWS;
  styleId: number;
  hasTitle: boolean;
  hasLead: boolean;
  hasImg: boolean;
  hasDate?: boolean;
  hasReadTime?: boolean;
  canHaveCustomTag?: boolean;
  configurable: true;
}

export interface LayoutElementContentMinuteToMinutes extends LayoutElementContent {
  contentType: LayoutElementContentType.MINUTE_TO_MINUTE;
  styleId: number;
  hasTitle: boolean;
  hasLead: boolean;
  hasImg: boolean;
  hasDate?: boolean;
  hasReadTime?: boolean;
  canHaveCustomTag?: boolean;
  configurable: true;
  withHorizontalSeparator?: boolean;
}

export interface LayoutElementContentQuiz extends LayoutElementContent {
  contentType: LayoutElementContentType.QUIZ;
  styleId: number;
  hasTitle: boolean;
  hasImg: boolean;
  hasTag: boolean;
  configurable: true;
}
export interface LayoutElementContentSponsoredQuiz extends LayoutElementContent {
  contentType: LayoutElementContentType.SPONSORED_QUIZ;
  styleId: number;
  hasTitle: boolean;
  hasImg: boolean;
  hasTag: boolean;
  configurable: true;
}

export interface LayoutElementContentWeeklyNewspaper extends LayoutElementContent {
  contentType: LayoutElementContentType.WEEKLY_NEWSPAPER_BOX;
  configurable: true;
  styleId: number;
}

export interface LayoutElementContentBlog extends LayoutElementContent {
  contentType: LayoutElementContentType.BLOG;
  styleId: number;
  hasTitle: boolean;
  configurable: true;
}

export interface LayoutElementContentMapRecommendations extends LayoutElementContent {
  contentType: LayoutElementContentType.MAP_RECOMMENDATIONS;
  styleId: number;
  configurable: false;
  mapConfig?: BasicLayoutElementPreview[];
}

export interface LayoutElementContentHero extends LayoutElementContent {
  contentType: LayoutElementContentType.HERO;
  configurable: true;
}

export interface LayoutElementContentMedicalMeteorology extends LayoutElementContent {
  contentType: LayoutElementContentType.MEDICAL_METEOROLOGY;
  configurable: false;
}

export interface LayoutElementContentTwelveDaysForecast extends LayoutElementContent {
  contentType: LayoutElementContentType.TWELVE_DAYS_FORECAST;
  configurable: false;
}

export interface LayoutElementContentDetections extends LayoutElementContent {
  contentType: LayoutElementContentType.DETECTIONS;
  configurable: true;
  styleId: number;
  minParentWidth?: number;
  parentWidth?: number;
  hasWithImageFilter?: boolean;
}

export interface LayoutElementContentOpinionList extends LayoutElementContent {
  contentType: LayoutElementContentType.OPINION_LIST;
  styleId: number;
  hasTitle: boolean;
  withHorizontalSeparator?: boolean;
  configurable: false;
  fontSize?: number;
}

export interface LayoutElementContentOpinion extends LayoutElementContent {
  contentType: LayoutElementContentType.OPINION;
  styleId: number;
  hasTitle: boolean;
  hasLead: boolean;
  hasImg: boolean;
  hasDate?: boolean;
  hasReadTime?: boolean;
  hasWriterName?: boolean; // technically not always present, it's mapped
  hasWriterImg?: boolean; // technically not always present, it's mapped
  hasAuthorName?: boolean; // FIXME mapped
  hasAuthorImg?: boolean; // FIXME mapped
  configurable: true;
  showHeader?: boolean;
  secondaryContentType?: LayoutElementContentType;
  fontSize?: number;
  withVerticalSeparator?: boolean;
}

export interface LayoutElementContentOpinionBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.OPINION_BLOCK;
  styleId: number;
  configurable: true;
  showHeader?: boolean;
  selectedArticles?: LayoutConfigurationArticle[];
}

export interface LayoutElementContentBayerBlog extends LayoutElementContent {
  contentType: LayoutElementContentType.BAYER_BLOG;
  styleId: number;
  configurable: true;
  showHeader?: boolean;
  selectedArticles?: LayoutConfigurationArticle[];
}

export interface LayoutElementContentWhereTheBallWillBe extends LayoutElementContent {
  contentType: LayoutElementContentType.WHERE_THE_BALL_WILL_BE;
  styleId: number;
  configurable: true;
  selectedArticles?: LayoutConfigurationArticle[];
}

export interface LayoutElementContentImage extends LayoutElementContent {
  contentType: LayoutElementContentType.IMAGE;
  cancelMargin: boolean;
  styleId: number;
  configurable: true;
}
export interface LayoutElementContentNote extends LayoutElementContent {
  contentType: LayoutElementContentType.NOTE;
  styleId: number;
  hasTitle: boolean;
  hasLead: boolean;
  hasImg: boolean;
  hasDate?: boolean;
  hasReadTime?: boolean;
  hasWriterName: boolean;
  hasWriterImg: boolean;
  configurable: true;
}

export interface LayoutElementContentVideo extends LayoutElementContent {
  contentType: LayoutElementContentType.VIDEO;
  styleId: number;
  hasTitle: boolean;
  hasLead: boolean;
  hasCoverImage: boolean;
  hasWatchTime: boolean;
  configurable: true;
}

export interface LayoutElementContentShortVideos extends Omit<LayoutElementContentVideo, 'contentType'> {
  contentType: LayoutElementContentType.SHORT_VIDEOS;
}

export interface LayoutElementContentGallery extends LayoutElementContent {
  contentType: LayoutElementContentType.GALLERY;
  styleId: number;
  hasTitle: boolean;
  hasImage: boolean;
  configurable: true;
  galleryLength: number;
  galleryLengthOverwrite?: number;
  isDisabledOverwriteContentLength?: boolean;
}

export interface LayoutElementContentManualArticle extends LayoutElementContent {
  contentType: LayoutElementContentType.MANUAL_ARTICLE;
  styleId: number;
  configurable: true;
  manualListOrder: number;
  manualList: Partial<ExplicitArticleList>;
  highlight?: boolean;
  caption?: boolean;
  italic?: boolean;
  live?: boolean;
  hasTitle?: boolean;
  hasImg?: boolean;
  hasLead?: boolean;
  fontSize?: number;
}

export interface LayoutElementContentManualOpinion extends LayoutElementContent {
  contentType: LayoutElementContentType.MANUAL_OPINION;
  styleId: number;
  configurable: true;
  manualListOrder: number;
  manualList: Partial<ExplicitArticleList>;
  desktopHeader: boolean;
  desktopFooter: boolean;
  mobileHeader: boolean;
  mobileFooter: boolean;
  highlight?: boolean;
  caption?: boolean;
  italic?: boolean;
  firstPreviewImage?: string;
  lastviewImage?: string;
  hasAuthorImg?: boolean;
  hasAuthorName?: boolean;
  hasDate?: boolean;
  hasImg?: boolean;
  hasLead?: boolean;
  hasTitle?: boolean;
  fontSize?: number;
}

export interface LayoutElementContentDossier extends LayoutElementContent {
  contentType: LayoutElementContentType.DOSSIER;
  styleId: number;
  maxSecondaryArticleCount: number[]; // The length of this array defines how many dossiers are in one box
  configurable: true;
}

export interface LayoutElementContentAd extends LayoutElementContent {
  contentType: LayoutElementContentType.AD;
  medium: string;
  bannerName: string;
  configurable: false;
}

export interface LayoutElementContentStockChart extends LayoutElementContent {
  contentType: LayoutElementContentType.STOCK;
  configurable: false;
  styleId: ExchangeBoxType;
  dataType: ExchangeBoxDataType;
  border: boolean;
}

export interface LayoutElementContentPodcastList extends LayoutElementContent {
  contentType: LayoutElementContentType.PODCAST_LIST;
  itemCount: number;
  configurable: true;
}

export interface LayoutElementContentVideoBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.VIDEO_BLOCK;
  itemCount: number;
  configurable: true;
}

export interface LayoutElementContentDossierRepeater extends LayoutElementContent {
  contentType: LayoutElementContentType.DOSSIER_REPEATER;
  configurable: true;
  itemCount: number;
  canModifyLength?: boolean;
}

export interface LayoutElementContentArticlesWithVideoContent extends LayoutElementContent {
  contentType: LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT;
  configurable: true;
  styleId: number;
  articleCount: number;
}

export interface LayoutElementContentArticlesWithPodcastContent extends LayoutElementContent {
  contentType: LayoutElementContentType.ARTICLES_WITH_PODCAST_CONTENT;
  configurable: true;
  styleId: number;
  btnUrl: string;
  withHorizontalSeparator?: boolean;
  articleLength?: string;
  hasBackground?: boolean;
}

export interface LayoutElementContentOlimpiaArticlesWithPodcastContent extends LayoutElementContent {
  contentType: LayoutElementContentType.OLIMPIA_ARTICLES_WITH_PODCAST_CONTENT;
  configurable: true;
  styleId: number;
  articleLength: number;
}

export interface LayoutElementContentSpotlight extends LayoutElementContent {
  contentType: LayoutElementContentType.SPOTLIGHT;
  configurable: true;
  styleId: number;
}

export interface LayoutElementContentTurpiBox extends LayoutElementContent {
  contentType: LayoutElementContentType.TURPI_BOX;
  configurable: true;
  styleId: number;
  minParentWidth?: number;
  selectedBestPracticesNumber?: number;
}

export interface LayoutElementContentAstrologyBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.ASTROLOGY;
  itemCount: number;
  configurable: true;
}

export interface LayoutContentTabConfig {
  id: string;
  title: string;
  slug: string;
}

export interface LayoutElementContentTabsBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.TABS;
  tabs: LayoutContentTabConfig[];
  configurable: true;
}

export interface LayoutElementContentBreakingBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.BREAKING;
  styleId: number;
  hasTitle: boolean;
  hasLead: boolean;
  hasImg?: boolean; // Changed to optional as in reality this is missing many times and only added at later mapping
  hasImage?: boolean; // FIXME added for type compatibilty as dialog uses `hasImage` and it's mapped to `hasImg`
  hasDate?: boolean;
  hasReadTime?: boolean;
  canHaveCustomTag?: boolean; // TODO: set all non domain specific prop to optional
  configurable: true;
}

export interface LayoutElementContentProgram extends LayoutElementContent {
  contentType: LayoutElementContentType.PROGRAM;
  styleId: number;
  hasTitle: boolean;
  hasDate: boolean;
  hasImg: boolean;
  hasLocation: boolean;
  canHaveCustomTag?: boolean; // TODO: set all non domain specific prop to optional
  configurable: true;
}

export interface LayoutElementContentTopStories extends LayoutElementContent {
  contentType: LayoutElementContentType.TOP_STORIES;
  hasTitle: boolean;
  configurable: false;
}

export interface LayoutElementContentMostViewed extends LayoutElementContent {
  contentType: LayoutElementContentType.MOST_VIEWED;
  hasTitle: boolean;
  configurable: true;
  isSidebar?: boolean;
  isPublishDate?: boolean;
}

export interface LayoutElementContentColumnBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.COLUMN_BLOCK;
  configurable: true;
}

export interface LayoutElementContentNewsletterBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.NEWSLETTER_BLOCK;
  styleId: number;
  configurable: false;
}

export interface LayoutElementContentNewsletterBlockGong extends LayoutElementContent {
  contentType: LayoutElementContentType.NEWSLETTER_BLOCK_GONG;
  styleId: number;
  configurable: false;
}

export interface LayoutElementContentOpinionNewsletterBox extends LayoutElementContent {
  contentType: LayoutElementContentType.OPINION_NEWSLETTER_BOX;
  styleId: number;
  configurable: false;
}

export interface LayoutElementContentFreshNews extends LayoutElementContent {
  contentType: LayoutElementContentType.FRESH_NEWS;
  configurable: true;
}

export interface LayoutElementContentLatestNews extends LayoutElementContent {
  contentType: LayoutElementContentType.LATEST_NEWS;
  configurable: true;
  numberOfArticles: number;
}

export interface LayoutElementContentLinkList extends LayoutElementContent {
  contentType: LayoutElementContentType.LINK_LIST;
  configurable: true;
}

export interface LayoutElementContentVote extends LayoutElementContent {
  contentType: LayoutElementContentType.VOTE;
  configurable: true;
  styleId?: number;
}

export interface LayoutElementContentSponsoredVote extends LayoutElementContent {
  contentType: LayoutElementContentType.SPONSORED_VOTE;
  configurable: true;
}

export interface LayoutElementContentMultiVote extends LayoutElementContent {
  contentType: LayoutElementContentType.MULTI_VOTE;
  configurable: true;
  styleId?: number;
}

export interface LayoutElementContentExperienceOccasion extends LayoutElementContent {
  contentType: LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION;
  configurable: true;
  styleId?: number;
}

export interface LayoutElementContentBrandingBox extends LayoutElementContent {
  contentType: LayoutElementContentType.BRANDING_BOX;
  brand: string;
  numberOfArticles: number;
  configurable: true;
}

export interface LayoutElementContentBrandingBoxEx extends LayoutElementContent {
  contentType: LayoutElementContentType.BRANDING_BOX_EX;
  brand: string;
  configurable: true;
  articleLimit?: number;
}

export interface LayoutElementContentVisegradPost extends LayoutElementContent {
  contentType: LayoutElementContentType.VISEGRAD_POST;
  configurable: true;
  numberOfArticles: number;
}

export interface LayoutElementContentCultureNation extends LayoutElementContent {
  contentType: LayoutElementContentType.CULTURE_NATION;
  configurable: false;
}

export interface LayoutElementContentBlockSeparator extends LayoutElementContent {
  contentType: LayoutElementContentType.BLOCK_SEPARATOR;
  configurable: false;
}

export interface LayoutElementContentSponsoredArticleBox extends LayoutElementContent {
  contentType: LayoutElementContentType.SPONSORED_ARTICLE_BOX;
  configurable: true;
}

export interface LayoutElementContentHtmlEmbed extends LayoutElementContent {
  contentType: LayoutElementContentType.HTML_EMBED;
  configurable: true;
}

export interface LayoutElementContentNewspaper extends LayoutElementContent {
  contentType: LayoutElementContentType.NEWSPAPER;
  configurable: boolean;
  printUrl?: string;
  onlineUrl?: string;
}

export interface LayoutElementContentServicesBox extends LayoutElementContent {
  contentType: LayoutElementContentType.SERVICES_BOX;
  configurable: true;
  styleId: number;
}

export interface LayoutElementContentPodcastAppRecommender extends LayoutElementContent {
  contentType: LayoutElementContentType.PODCAST_APP_RECOMMENDER;
  configurable: false;
  styleId: number;
}

export interface LayoutElementContentSocialMedia extends LayoutElementContent {
  contentType: LayoutElementContentType.SOCIAL_MEDIA;
  configurable: false;
}

export interface LayoutElementContentIngatlanbazar extends LayoutElementContent {
  contentType: LayoutElementContentType.INGATLANBAZAR;
  configurable: false;
}

export interface LayoutElementContentIngatlanbazarConfigurable extends LayoutElementContent {
  contentType: LayoutElementContentType.INGATLANBAZAR_CONFIGURABLE;
  configurable: true;
  showHeader: false;
  xmlUrl: null;
  itemsToShow: number;
}

export interface LayoutElementContentIngatlanbazarSearch extends LayoutElementContent {
  contentType: LayoutElementContentType.INGATLANBAZAR_SEARCH;
  configurable: true;
}

export interface LayoutElementContentTagBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.TAG_BLOCK;
  configurable: false;
}

export interface LayoutElementContentTrendingTagsBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.TRENDING_TAGS_BLOCK;
  configurable: true;
}

export interface LayoutElementContentFinalCountdown extends LayoutElementContent {
  contentType: LayoutElementContentType.FINAL_COUNTDOWN;
  configurable: true;
  styleId?: number;
}

export interface LayoutElementContentTagStripBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.TAG_BLOCK;
  configurable: true;
}

export interface LayoutElementContentKoponyeg extends LayoutElementContent {
  contentType: LayoutElementContentType.KOPONYEG;
  configurable: boolean;
  styleId?: number;
}

export interface LayoutElementContentWaze extends LayoutElementContent {
  contentType: LayoutElementContentType.WAZE;
  configurable: false;
}

export interface LayoutElementContentSzakikereso extends LayoutElementContent {
  contentType: LayoutElementContentType.SZAKIKERESO;
  configurable: false;
}

export interface LayoutElementContentBestRecommender extends LayoutElementContent {
  contentType: LayoutElementContentType.BEST_RECOMMENDER;
  configurable: true;
}

export interface LayoutElementContentMediaPanel extends LayoutElementContent {
  hideArticleType?: boolean;
  contentType: LayoutElementContentType.MEDIA_PANEL;
  configurable: true;
}

export interface LayoutElementContentCategoryStepper extends LayoutElementContent {
  contentType: LayoutElementContentType.CATEGORY_STEPPER;
  configurable: true;
}

export interface LayoutElementContentAstronetHoroszkop extends LayoutElementContent {
  contentType: LayoutElementContentType.ASTRONET_HOROSZKOP;
  configurable: false;
}

export interface LayoutElementContentAstronetJoslas extends LayoutElementContent {
  contentType: LayoutElementContentType.ASTRONET_JOSLAS;
  configurable: false;
}
export interface LayoutElementContentSorozatveto extends LayoutElementContent {
  contentType: LayoutElementContentType.SOROZATVETO;
  styleId: number;
  configurable: true;
}

export interface LayoutElementContentDossierList extends LayoutElementContent {
  contentType: LayoutElementContentType.DOSSIER_LIST;
  styleId: number;
  configurable: true;
}

export type VisegradArticleMapped = Readonly<{
  title: string;
  slug: string;
  img: string;
  desc: string;
  link: string;
}>;

// FIXME are we really need this? this is the same as the Article!!!
export interface LayoutElementContentPrBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.PR_BLOCK;
  styleId: number;
  hasTitle: boolean;
  hasLead: boolean;
  hasImg?: boolean; // in reality, it's not there always
  hasImage?: boolean; // FIXME mapped to there
  hasDate?: boolean;
  hasReadTime?: boolean;
  canHaveCustomTag?: boolean; // TODO: set all non domain specific prop to optional
  configurable: true;
  withVerticalSeparator?: boolean;
}

export interface LayoutElementContentWysiwyg extends LayoutElementContent {
  contentType: LayoutElementContentType.WYSIWYG;
  configurable: true;
}

export interface LayoutElementContentBroadcastRecommender extends LayoutElementContent {
  contentType: LayoutElementContentType.BROADCAST_RECOMMENDER;
  configurable: false;
}

export interface LayoutElementContentSongTopList extends LayoutElementContent {
  contentType: LayoutElementContentType.SONG_TOP_LIST;
  configurable: false;
}

export interface LayoutElementContentPdfBox extends LayoutElementContent {
  contentType: LayoutElementContentType.PDF_BOX;
  configurable: true;
  styleId?: string;
}

export interface LayoutElementContentRssBox extends LayoutElementContent {
  contentType: LayoutElementContentType.RSS_BOX;
  configurable: true;
  rssUrl: string;
  title: string;
  numberOfArticles: number;
  textColor: string;
  backgroundColor: string;
  articleTitleTextColor: string;
  leadTextColor: string;
  dividerColor: string;
  columnButtonBackgroundColor: string;
  columnButtonTextColor: string;
  columnButtons: { label: string; url: string }[];
  logo?: MediaStoreResult;
  logoHref: string;
}

export interface LayoutElementContentTenyekBox extends LayoutElementContent {
  contentType: LayoutElementContentType.TENYEK_BOX;
  configurable: false;
}

export interface LayoutElementContentGPNewsBox extends LayoutElementContent {
  contentType: LayoutElementContentType.GP_NEWS_BOX;
  styleId: number;
  configurable: true;
  turnOffPreviewImageGenerateByContentLength: boolean;
  previewImages?: { previewImage: string }[];
}

export interface LayoutElementContentEBNews extends LayoutElementContent {
  contentType: LayoutElementContentType.EB_NEWS;
  styleId: number;
  configurable: true;
  previewImages?: { previewImage: string }[];
}

export interface LayoutElementContentBrandingBoxArticle extends LayoutElementContent {
  contentType: LayoutElementContentType.BRANDING_BOX_ARTICLE;
  styleId: number;
  configurable: true;
  previewImages?: { previewImage: string }[];
}

export interface LayoutElementContentLiveBar extends LayoutElementContent {
  contentType: LayoutElementContentType.LIVE_BAR;
  configurable: true;
}

export interface LayoutElementContentSportBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.SPORT_BLOCK;
  styleId: number;
  configurable: true;
}

export interface LayoutElementContentArticleBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.ARTICLE_BLOCK;
  styleId: number;
  configurable: true;
  hasAutoFill?: boolean;
}

export interface LayoutElementContentRecipeCategorySelect extends LayoutElementContent {
  contentType: LayoutElementContentType.RECIPE_CATEGORY_SELECT;
  styleId: number;
  configurable: true;
}

export interface LayoutElementContentTextBox extends LayoutElementContent {
  contentType: LayoutElementContentType.TEXT_BOX;
  styleId: number;
  configurable: true;
}

export interface LayoutElementContentTurpiCard extends LayoutElementContent {
  contentType: LayoutElementContentType.TURPI_CARD;
  styleId: number;
  configurable: true;
}

export interface LayoutElementContentIngredient extends LayoutElementContent {
  contentType: LayoutElementContentType.INGREDIENT;
  styleId: number;
  configurable: true;
}

export interface LayoutElementContentGuaranteeBox extends LayoutElementContent {
  contentType: LayoutElementContentType.GUARANTEE_BOX;
  styleId: number;
  configurable: true;
}

export interface LayoutElementContentMoreArticles extends LayoutElementContent {
  contentType: LayoutElementContentType.MORE_ARTICLES;
  styleId: number;
  configurable: true;
}

export interface LayoutElementContentRelatedArticles extends LayoutElementContent {
  contentType: LayoutElementContentType.RELATED_ARTICLES;
  styleId: number;
  selectedArticlesNumber?: number;
  configurable: true;
  isOrigoSport: boolean;
}

export interface LayoutElementContentArticleSlider extends LayoutElementContent {
  contentType: LayoutElementContentType.ARTICLE_SLIDER;
  configurable: true;
  styleId: number;
}

export interface LayoutElementContentDidYouKnow extends LayoutElementContent {
  contentType: LayoutElementContentType.DID_YOU_KNOW;
  configurable: true;
}

export interface LayoutElementContentOlimpiaHungarianCompetitions extends LayoutElementContent {
  contentType: LayoutElementContentType.OLIMPIA_HUNGARIAN_COMPETITIONS;
  configurable: true;
}

export interface LayoutElementContentOlimpiaLargeNavigator extends LayoutElementContent {
  contentType: LayoutElementContentType.OLIMPIA_LARGE_NAVIGATOR;
  styleId: number;
  configurable: true;
}

export interface LayoutElementContentRecipe extends LayoutElementContent {
  contentType: LayoutElementContentType.RECIPE;
  styleId: number;
  hasBackground: boolean;
  configurable: true;
  hasTitle?: boolean;
  hasImg?: boolean;
  hasImage?: boolean;
  hasLead?: boolean;
  canHaveCustomTag?: boolean;
}

export interface LayoutElementContentRecipeSwiper extends LayoutElementContent {
  contentType: LayoutElementContentType.RECIPE_SWIPER;
  configurable: true;
  styleId: number;
}

export interface LayoutElementContentOfferBox extends LayoutElementContent {
  contentType: LayoutElementContentType.OFFER_BOX;
  styleId: number;
  configurable: true;
}

export interface LayoutElementContentMaestroBox extends LayoutElementContent {
  contentType: LayoutElementContentType.MAESTRO_BOX;
  styleId: number;
  configurable: true;
}

export interface LayoutElementContentGastroOccasionRecommender extends LayoutElementContent {
  contentType: LayoutElementContentType.GASTRO_OCCASION_RECOMMENDER;
  styleId: number;
  configurable: true;
}
export interface LayoutElementContentGastroExperienceRecommendation extends LayoutElementContent {
  contentType: LayoutElementContentType.GASTRO_EXPERIENCE_RECOMMENDATION;
  styleId: number;
  configurable: true;
  remainingNumberOfSeatsOrder: boolean;
}

export interface LayoutElementContentGastroThematicRecommender extends LayoutElementContent {
  contentType: LayoutElementContentType.GASTRO_THEMATIC_RECOMMENDER;
  styleId: number;
  configurable: true;
}

export interface LayoutElementContentGastroExperienceOccasionSwiper extends LayoutElementContent {
  contentType: LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION_SWIPER;
  styleId: number;
  configurable: true;
}
export interface LayoutElementContentConfigurableSponsoredBox extends LayoutElementContent {
  contentType: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX;
  configurable: true;
}
export interface LayoutElementContentHighlightedSelection extends LayoutElementContent {
  contentType: LayoutElementContentType.HIGHLIGHTED_SELECTION;
  styleId: number;
  configurable: true;
}

export interface LayoutElementContentSelection extends LayoutElementContent {
  contentType: LayoutElementContentType.SELECTION;
  styleId: number;
  configurable: true;
  maxContentLength: number;
  hasBackground: boolean;
}

export interface LayoutElementContentDailyMenu extends LayoutElementContent {
  contentType: LayoutElementContentType.DAILY_MENU;
  showCurrentDay: boolean;
  styleId: number;
  configurable: false;
}

export interface LayoutElementContentTelekomVivicitta extends LayoutElementContent {
  contentType: LayoutElementContentType.TELEKOM_VIVICITTA;
  configurable: false;
  styleId: number;
  title: string;
}

export interface LayoutElementContentConference extends LayoutElementContent {
  contentType: LayoutElementContentType.CONFERENCE;
  configurable: true;
}

export interface LayoutElementContentCountdownBox extends LayoutElementContent {
  contentType: LayoutElementContentType.COUNTDOWN_BOX;
  configurable: true;
  styleId: number;
}

export interface LayoutElementContentElectionsBox extends LayoutElementContent {
  contentType: LayoutElementContentType.ELECTIONS_BOX;
  configurable: false;
  styleId: number;
}

export interface LayoutElementContentEbCountdownBlockTitle extends LayoutElementContent {
  contentType: LayoutElementContentType.EB_COUNTDOWN_BLOCK_TITLE;
  configurable: true;
  styleId: number;
}

export interface LayoutElementContentOlimpiaCountdownBlockTitle extends LayoutElementContent {
  contentType: LayoutElementContentType.EB_COUNTDOWN_BLOCK_TITLE;
  configurable: true;
  styleId: number;
}

export interface LayoutElementContentOlimpiaNews extends LayoutElementContent {
  contentType: LayoutElementContentType.OLIMPIA_NEWS;
  styleId: number;
  configurable: true;
  previewImages?: { previewImage: string }[];
}

export interface LayoutElementContentOlimpiaHungarianTeam extends LayoutElementContent {
  contentType: LayoutElementContentType.OLIMPIA_HUNGARIAN_TEAM;
  configurable: true;
  styleId: number;
}

export interface LayoutElementContentOlimpiaResultsBlock extends LayoutElementContent {
  contentType: LayoutElementContentType.OLIMPIA_RESULTS_BLOCK;
  configurable: false;
  styleId: number;
}

export interface LayoutElementContentPublicAuthors extends LayoutElementContent {
  contentType: LayoutElementContentType.PUBLIC_AUTHORS;
  configurable: true;
  styleId: number;
}

export interface LayoutElementContentExperienceGift extends LayoutElementContent {
  contentType: LayoutElementContentType.EXPERIENCE_GIFT;
  configurable: false;
}
export interface LayoutElementContentEventCalendar extends LayoutElementContent {
  contentType: LayoutElementContentType.EVENT_CALENDAR;
  configurable: false;
}

export interface LayoutElementContentJobListings extends LayoutElementContent {
  contentType: LayoutElementContentType.JOB_LISTINGS;
  configurable: false;
}

export interface LayoutElementContentTopTenTags extends LayoutElementContent {
  contentType: LayoutElementContentType.TOP_TEN_TAGS;
  configurable: true;
}

export interface LayoutElementContentTopicSuggestion extends LayoutElementContent {
  contentType: LayoutElementContentType.TOPIC_SUGGESTION;
  configurable: false;
}

export interface LayoutElementContentSecretDaysCalendar extends LayoutElementContent {
  contentType: LayoutElementContentType.SECRET_DAYS_CALENDAR;
  configurable: true;
}
