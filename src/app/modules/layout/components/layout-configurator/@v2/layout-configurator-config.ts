import { LayoutElementContentType } from '@modules/layout/layout-core.definitions';
import { LayoutConfigurator } from '@modules/layout/components/layout-configurator/@v2/layout-configurator-definitions';
import {
  ArticleConfigurator,
  ArticlePairConfigurator,
  ArticleShiftingConfigurator,
  ArticlesWithGalleryConfigurator,
  ArticlesWithVideoConfigurator,
  AstronetBrandingBoxConfigurator,
  AuthorConfigurator,
  BayerBlogConfigurator,
  BestRecommenderConfigurator,
  BlogConfigurator,
  BrandingBoxConfigurator,
  BrandingBoxExternalConfigurator,
  BreakingArticleConfigurator,
  CategoryStepperConfigurator,
  ChampionshipConfigurator,
  ColumnBlockConfigurator,
  ConferenceConfigurator,
  CountdownBoxConfigurator,
  DataBankConfigurator,
  DetectionConfigurator,
  DossierConfigurator,
  DossierContentConfigurator,
  DossiersConfigurator,
  FastNewsConfigurator,
  FinalCountdownConfigurator,
  FreshNewsConfigurator,
  GalleriesConfigurator,
  GastroExperienceOccasionConfigurator,
  GastroExperienceOccasionSwiperConfigurator,
  GastroExperienceRecommendationConfigurator,
  GastroOccasionRecommenderConfigurator,
  GastroThematicRecommenderConfigurator,
  GPNewsConfigurator,
  GuaranteeBoxConfigurator,
  HelloBudapestConfigurator,
  HeroConfigurator,
  HighlightedSelectionConfigurator,
  HtmlEmbedConfigurator,
  ImageConfigurator,
  IngatlanbazarSearchConfigurator,
  IngredientConfigurator,
  KoponyegConfigurator,
  LatestAndMostReadArticlesConfigurator,
  LatestNewsConfigurator,
  LeadEditorsConfigurator,
  MaestroBoxConfigurator,
  ManualArticleConfigurator,
  ManualOpinionConfigurator,
  MinuteToMinuteConfigurator,
  MoreArticlesConfigurator,
  MostViewedConfigurator,
  MultiVoteConfigurator,
  NewsFeedConfigurator,
  NewspaperConfigurator,
  NoteArticleConfigurator,
  OfferConfigurator,
  OpinionArticleConfigurator,
  PdfBoxConfigurator,
  PodcastArticleConfigurator,
  PodcastListConfigurator,
  PrArticleConfigurator,
  ProgramConfigurator,
  PublicAuthorConfigurator,
  QuizConfigurator,
  RecipeCategorySelectConfigurator,
  RecipeConfigurator,
  RecipeForMaestrosConfigurator,
  RecipeSelectForSelectionConfigurator,
  RelatedArticlesConfigurator,
  SecretDaysCalendarConfigurator,
  SelectionConfigurator,
  ServicesBoxConfigurator,
  ShortVideosConfigurator,
  SorozatvetoConfigurator,
  SponsoredArticleBoxConfigurator,
  SpotlightConfigurator,
  SubColumnsConfigurator,
  TabsConfigurator,
  TagStripConfigurator,
  TeamsConfigurator,
  TextBoxConfigurator,
  TopCommentedArticlesConfigurator,
  TopTenTagsConfigurator,
  TrendingTagsConfigurator,
  TripBoxConfigurator,
  TurpiBoxConfigurator,
  TurpiCardConfigurator,
  UpcomingMatchesConfigurator,
  VideoConfigurator,
  VideosConfigurator,
  VoteConfigurator,
  WeeklyNewspaperArticlesConfigurator,
  WeeklyNewspaperBoxConfigurator,
  WhereTheBallWillBeConfigurator,
  WysiwygConfigurator,
} from '@modules/layout/components/layout-configurator/@v2/configurators';
import {
  ArticleOverwrite,
  FastNewsOverwrite,
  NoteOverwrite,
  OpinionOverwrite,
  RecipeCategorySelectOverwrite,
  RecipeOverwrite,
  VideoOverwrite,
} from '@modules/layout/components/layout-configurator/@v2/overwrites';
import {
  LayoutConfiguratorArticleComponent,
  LayoutConfiguratorArticleShiftingComponent,
  LayoutConfiguratorRecipesByCategoryComponent,
  LayoutConfiguratorRecipesForMaestrosComponent,
  LayoutConfiguratorTabsComponent,
  LayoutConfiguratorTeamsComponent,
  LayoutConfiguratorWysiwygComponent,
} from '@modules/layout/components/layout-configurator/@v2/components/configurators';
import { ConfigurableSponsoredBoxConfigurator } from '@modules/layout/components/layout-configurator/@v2/configurators/configurable-sponsored-box';
import { SponsoredVoteConfigurator } from '@modules/layout/components/layout-configurator/@v2/configurators/sponsored-vote-configurator';
import { SponsoredQuizConfigurator } from './configurators/sponsored-quiz-configurator';
import { DidYouKnowConfigurator } from '@modules/layout/components/layout-configurator/@v2/configurators/did-you-know-configurator';

export const LAYOUT_CONFIGURATOR_CONFIG: LayoutConfigurator[] = [
  {
    configuratorType: ArticleConfigurator,
    overwriteType: ArticleOverwrite,
    supportedContentTypes: [
      LayoutElementContentType.ARTICLE_SLIDER,
      LayoutElementContentType.MEDIA_PANEL,
      LayoutElementContentType.BRANDING_BOX_ARTICLE,
      LayoutElementContentType.SPORT_BLOCK,
    ],
  },
  {
    configuratorType: ArticlePairConfigurator,
    supportedContentTypes: [LayoutElementContentType.STAR_BIRTHS],
  },
  {
    configuratorType: ArticleConfigurator,
    overwriteType: ArticleOverwrite,
    customComponent: LayoutConfiguratorArticleComponent,
    supportedContentTypes: [LayoutElementContentType.ARTICLE],
  },
  {
    configuratorType: ManualArticleConfigurator,
    supportedContentTypes: [LayoutElementContentType.MANUAL_ARTICLE],
  },
  {
    configuratorType: ManualOpinionConfigurator,
    supportedContentTypes: [LayoutElementContentType.MANUAL_OPINION],
  },
  {
    configuratorType: ArticleShiftingConfigurator,
    customComponent: LayoutConfiguratorArticleShiftingComponent,
    supportedContentTypes: [LayoutElementContentType.ARTICLE_BLOCK],
  },
  {
    configuratorType: GPNewsConfigurator,
    supportedContentTypes: [LayoutElementContentType.GP_NEWS_BOX],
  },
  {
    configuratorType: ArticlesWithVideoConfigurator,
    supportedContentTypes: [LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT],
  },
  {
    configuratorType: SpotlightConfigurator,
    supportedContentTypes: [LayoutElementContentType.SPOTLIGHT],
  },
  {
    configuratorType: MostViewedConfigurator,
    supportedContentTypes: [LayoutElementContentType.MOST_VIEWED],
  },
  {
    configuratorType: PrArticleConfigurator,
    overwriteType: ArticleOverwrite,
    customComponent: LayoutConfiguratorArticleComponent,
    supportedContentTypes: [LayoutElementContentType.PR_BLOCK],
  },
  {
    configuratorType: BreakingArticleConfigurator,
    supportedContentTypes: [LayoutElementContentType.BREAKING],
  },
  {
    configuratorType: ColumnBlockConfigurator,
    supportedContentTypes: [LayoutElementContentType.COLUMN_BLOCK],
  },
  {
    configuratorType: KoponyegConfigurator,
    supportedContentTypes: [LayoutElementContentType.KOPONYEG],
  },
  {
    configuratorType: FastNewsConfigurator,
    overwriteType: FastNewsOverwrite,
    supportedContentTypes: [LayoutElementContentType.FAST_NEWS],
  },
  {
    configuratorType: FreshNewsConfigurator,
    supportedContentTypes: [LayoutElementContentType.FRESH_NEWS],
  },
  {
    configuratorType: PodcastArticleConfigurator,
    supportedContentTypes: [LayoutElementContentType.ARTICLES_WITH_PODCAST_CONTENT, LayoutElementContentType.PODCAST_ARTICLE_LIST],
  },
  {
    configuratorType: WysiwygConfigurator,
    customComponent: LayoutConfiguratorWysiwygComponent,
    supportedContentTypes: [LayoutElementContentType.WYSIWYG],
  },
  {
    configuratorType: DetectionConfigurator,
    supportedContentTypes: [LayoutElementContentType.DETECTIONS],
  },
  {
    configuratorType: TeamsConfigurator,
    customComponent: LayoutConfiguratorTeamsComponent,
    supportedContentTypes: [LayoutElementContentType.TEAMS],
  },
  {
    configuratorType: QuizConfigurator,
    supportedContentTypes: [LayoutElementContentType.QUIZ],
  },
  {
    configuratorType: SponsoredQuizConfigurator,
    supportedContentTypes: [LayoutElementContentType.SPONSORED_QUIZ],
  },
  {
    configuratorType: NoteArticleConfigurator,
    overwriteType: NoteOverwrite,
    supportedContentTypes: [LayoutElementContentType.NOTE],
  },
  {
    configuratorType: VideoConfigurator,
    overwriteType: VideoOverwrite,
    supportedContentTypes: [LayoutElementContentType.VIDEO],
  },
  {
    configuratorType: AuthorConfigurator,
    supportedContentTypes: [LayoutElementContentType.AUTHOR],
  },
  {
    configuratorType: OpinionArticleConfigurator,
    overwriteType: OpinionOverwrite,
    supportedContentTypes: [LayoutElementContentType.OPINION],
  },
  {
    configuratorType: BayerBlogConfigurator,
    supportedContentTypes: [LayoutElementContentType.BAYER_BLOG],
  },
  {
    configuratorType: WhereTheBallWillBeConfigurator,
    supportedContentTypes: [LayoutElementContentType.WHERE_THE_BALL_WILL_BE],
  },
  {
    configuratorType: DataBankConfigurator,
    supportedContentTypes: [LayoutElementContentType.DATA_BANK],
  },
  {
    configuratorType: TagStripConfigurator,
    supportedContentTypes: [LayoutElementContentType.TAG_BLOCK],
  },
  {
    configuratorType: MultiVoteConfigurator,
    supportedContentTypes: [LayoutElementContentType.MULTI_VOTE],
  },
  {
    configuratorType: IngredientConfigurator,
    supportedContentTypes: [LayoutElementContentType.INGREDIENT],
  },
  {
    configuratorType: HtmlEmbedConfigurator,
    supportedContentTypes: [LayoutElementContentType.HTML_EMBED],
  },
  {
    configuratorType: BlogConfigurator,
    supportedContentTypes: [LayoutElementContentType.BLOG],
  },
  {
    configuratorType: CategoryStepperConfigurator,
    supportedContentTypes: [LayoutElementContentType.CATEGORY_STEPPER],
  },
  {
    configuratorType: BrandingBoxConfigurator,
    supportedContentTypes: [LayoutElementContentType.BRANDING_BOX],
  },
  {
    configuratorType: DidYouKnowConfigurator,
    supportedContentTypes: [LayoutElementContentType.DID_YOU_KNOW],
  },
  {
    configuratorType: BrandingBoxExternalConfigurator,
    supportedContentTypes: [LayoutElementContentType.BRANDING_BOX_EX],
  },
  {
    configuratorType: ChampionshipConfigurator,
    supportedContentTypes: [LayoutElementContentType.CHAMPIONSHIP_TABLE],
  },
  {
    configuratorType: ConferenceConfigurator,
    supportedContentTypes: [LayoutElementContentType.CONFERENCE],
  },
  {
    configuratorType: DossierConfigurator,
    overwriteType: DossierContentConfigurator,
    supportedContentTypes: [LayoutElementContentType.DOSSIER, LayoutElementContentType.DOSSIER_REPEATER],
  },
  {
    configuratorType: NewsFeedConfigurator,
    overwriteType: DossierContentConfigurator,
    supportedContentTypes: [LayoutElementContentType.NEWS_FEED],
  },
  {
    configuratorType: DossiersConfigurator,
    supportedContentTypes: [LayoutElementContentType.DOSSIER_LIST],
  },
  {
    configuratorType: FinalCountdownConfigurator,
    supportedContentTypes: [LayoutElementContentType.FINAL_COUNTDOWN],
  },
  {
    configuratorType: GalleriesConfigurator,
    supportedContentTypes: [LayoutElementContentType.GALLERY],
  },
  {
    configuratorType: ConfigurableSponsoredBoxConfigurator,
    supportedContentTypes: [LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX],
  },
  {
    configuratorType: WeeklyNewspaperBoxConfigurator,
    overwriteType: WeeklyNewspaperArticlesConfigurator,
    allowFullOverwrite: true,
    supportedContentTypes: [LayoutElementContentType.WEEKLY_NEWSPAPER_BOX],
  },
  {
    configuratorType: RelatedArticlesConfigurator,
    supportedContentTypes: [LayoutElementContentType.RELATED_ARTICLES],
  },
  {
    configuratorType: PdfBoxConfigurator,
    supportedContentTypes: [LayoutElementContentType.PDF_BOX],
  },
  {
    configuratorType: HelloBudapestConfigurator,
    supportedContentTypes: [LayoutElementContentType.HELLO_BUDAPEST],
  },
  {
    configuratorType: VoteConfigurator,
    supportedContentTypes: [LayoutElementContentType.VOTE],
  },
  {
    configuratorType: SponsoredVoteConfigurator,
    supportedContentTypes: [LayoutElementContentType.SPONSORED_VOTE],
  },
  {
    configuratorType: VideosConfigurator,
    supportedContentTypes: [LayoutElementContentType.VIDEO_BLOCK],
  },
  {
    configuratorType: UpcomingMatchesConfigurator,
    supportedContentTypes: [LayoutElementContentType.UPCOMING_MATCHES],
  },
  {
    configuratorType: NewspaperConfigurator,
    supportedContentTypes: [LayoutElementContentType.NEWSPAPER],
  },
  {
    configuratorType: RecipeCategorySelectConfigurator,
    overwriteType: RecipeCategorySelectOverwrite,
    overwriteCustomComponent: LayoutConfiguratorRecipesByCategoryComponent,
    allowFullOverwrite: true,
    supportedContentTypes: [LayoutElementContentType.RECIPE_CATEGORY_SELECT],
  },
  {
    configuratorType: TrendingTagsConfigurator,
    supportedContentTypes: [LayoutElementContentType.TRENDING_TAGS_BLOCK],
  },
  {
    configuratorType: RecipeConfigurator,
    overwriteType: RecipeOverwrite,
    supportedContentTypes: [LayoutElementContentType.RECIPE, LayoutElementContentType.RECIPE_SWIPER],
  },
  {
    configuratorType: SelectionConfigurator,
    overwriteType: RecipeSelectForSelectionConfigurator,
    allowFullOverwrite: true,
    supportedContentTypes: [LayoutElementContentType.SELECTION],
  },
  {
    configuratorType: HighlightedSelectionConfigurator,
    supportedContentTypes: [LayoutElementContentType.HIGHLIGHTED_SELECTION],
  },
  {
    configuratorType: PublicAuthorConfigurator,
    supportedContentTypes: [LayoutElementContentType.PUBLIC_AUTHORS],
  },
  {
    configuratorType: MinuteToMinuteConfigurator,
    overwriteType: ArticleOverwrite,
    supportedContentTypes: [LayoutElementContentType.MINUTE_TO_MINUTE],
  },
  {
    configuratorType: MoreArticlesConfigurator,
    supportedContentTypes: [LayoutElementContentType.MORE_ARTICLES],
  },
  {
    configuratorType: IngatlanbazarSearchConfigurator,
    supportedContentTypes: [LayoutElementContentType.INGATLANBAZAR_SEARCH],
  },
  {
    configuratorType: LatestNewsConfigurator,
    supportedContentTypes: [LayoutElementContentType.LATEST_NEWS],
  },
  {
    configuratorType: TextBoxConfigurator,
    supportedContentTypes: [LayoutElementContentType.TEXT_BOX],
  },
  {
    configuratorType: SorozatvetoConfigurator,
    supportedContentTypes: [LayoutElementContentType.SOROZATVETO],
  },
  {
    configuratorType: TurpiCardConfigurator,
    supportedContentTypes: [LayoutElementContentType.TURPI_CARD],
  },
  {
    configuratorType: OfferConfigurator,
    supportedContentTypes: [LayoutElementContentType.OFFER_BOX],
  },
  {
    configuratorType: ProgramConfigurator,
    supportedContentTypes: [LayoutElementContentType.PROGRAM],
  },
  {
    configuratorType: BestRecommenderConfigurator,
    supportedContentTypes: [LayoutElementContentType.BEST_RECOMMENDER],
  },
  {
    configuratorType: ImageConfigurator,
    supportedContentTypes: [LayoutElementContentType.IMAGE],
  },
  {
    configuratorType: GastroThematicRecommenderConfigurator,
    supportedContentTypes: [LayoutElementContentType.GASTRO_THEMATIC_RECOMMENDER],
  },
  {
    configuratorType: GastroOccasionRecommenderConfigurator,
    supportedContentTypes: [LayoutElementContentType.GASTRO_OCCASION_RECOMMENDER],
  },
  {
    configuratorType: GastroExperienceOccasionConfigurator,
    supportedContentTypes: [LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION],
  },
  {
    configuratorType: GastroExperienceOccasionSwiperConfigurator,
    supportedContentTypes: [LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION_SWIPER],
  },
  {
    configuratorType: GastroExperienceRecommendationConfigurator,
    supportedContentTypes: [LayoutElementContentType.GASTRO_EXPERIENCE_RECOMMENDATION],
  },
  {
    configuratorType: CountdownBoxConfigurator,
    supportedContentTypes: [LayoutElementContentType.COUNTDOWN_BOX],
  },
  {
    configuratorType: TurpiBoxConfigurator,
    supportedContentTypes: [LayoutElementContentType.TURPI_BOX],
  },
  {
    configuratorType: ServicesBoxConfigurator,
    supportedContentTypes: [LayoutElementContentType.SERVICES_BOX],
  },
  {
    configuratorType: HeroConfigurator,
    supportedContentTypes: [LayoutElementContentType.HERO],
  },
  {
    configuratorType: TripBoxConfigurator,
    supportedContentTypes: [LayoutElementContentType.TRIP_BOX],
  },
  {
    configuratorType: SponsoredArticleBoxConfigurator,
    supportedContentTypes: [LayoutElementContentType.SPONSORED_ARTICLE_BOX],
  },
  {
    configuratorType: PodcastListConfigurator,
    supportedContentTypes: [LayoutElementContentType.PODCAST_LIST],
  },
  {
    configuratorType: MaestroBoxConfigurator,
    overwriteType: RecipeForMaestrosConfigurator,
    overwriteCustomComponent: LayoutConfiguratorRecipesForMaestrosComponent,
    allowFullOverwrite: true,
    supportedContentTypes: [LayoutElementContentType.MAESTRO_BOX],
  },
  {
    configuratorType: LeadEditorsConfigurator,
    supportedContentTypes: [LayoutElementContentType.LEAD_EDITORS],
  },
  {
    configuratorType: GuaranteeBoxConfigurator,
    supportedContentTypes: [LayoutElementContentType.GUARANTEE_BOX],
  },
  {
    configuratorType: TabsConfigurator,
    customComponent: LayoutConfiguratorTabsComponent,
    supportedContentTypes: [LayoutElementContentType.TABS],
  },
  {
    configuratorType: TopTenTagsConfigurator,
    supportedContentTypes: [LayoutElementContentType.TOP_TEN_TAGS],
  },
  {
    configuratorType: ArticlesWithGalleryConfigurator,
    supportedContentTypes: [LayoutElementContentType.GALLERY_ARTICLE_LIST],
  },
  {
    configuratorType: LatestAndMostReadArticlesConfigurator,
    supportedContentTypes: [LayoutElementContentType.LATEST_AND_MOST_READ_ARTICLES],
  },
  {
    configuratorType: SubColumnsConfigurator,
    supportedContentTypes: [LayoutElementContentType.SUB_COLUMNS],
  },
  {
    configuratorType: AstronetBrandingBoxConfigurator,
    supportedContentTypes: [LayoutElementContentType.ASTRONET_BRANDING_BOX, LayoutElementContentType.ASTRONET_COLUMNS],
  },
  {
    configuratorType: SecretDaysCalendarConfigurator,
    supportedContentTypes: [LayoutElementContentType.SECRET_DAYS_CALENDAR],
  },
  {
    configuratorType: TopCommentedArticlesConfigurator,
    supportedContentTypes: [LayoutElementContentType.TOP_COMMENTED_ARTICLES],
  },
  {
    configuratorType: ShortVideosConfigurator,
    supportedContentTypes: [LayoutElementContentType.SHORT_VIDEOS],
  },
];
