import { Injectable } from '@angular/core';
import {
  LayoutConfiguratorDef,
  LayoutConfiguratorFormFieldType,
  LayoutConfiguratorFormInfo,
  LayoutElementDynamicConfig,
} from '@modules/layout/components/layout-configurator/@v2/layout-configurator-definitions';
import { BehaviorSubject } from 'rxjs';
import {
  RealEstateBazaarType,
  realEstateBazaarTypes,
  realEstateBudapestLocations,
  realEstateCountyLocations,
  realEstateCountyLocationsWithBudapest,
  realEstateOtherLocations,
} from '@trendency/kesma-ui';
import { LayoutConfiguratorBuilderComponent } from '@modules/layout/components/layout-configurator/@v2/components';
import { RealEstateBazaarLocation } from '@trendency/kesma-ui/lib/components/real-estate-bazaar-search-block/real-estate-bazaar-search-block.definitions';

@Injectable()
export class IngatlanbazarSearchConfigurator implements LayoutConfiguratorDef {
  title: string = 'Ingatlanbazár kereső doboz konfigurálása';
  locations = new BehaviorSubject<RealEstateBazaarLocation[]>(realEstateBudapestLocations);
  types = new BehaviorSubject<RealEstateBazaarType[]>(realEstateBazaarTypes);

  formInfo: LayoutConfiguratorFormInfo[] = [
    {
      fieldType: LayoutConfiguratorFormFieldType.Label,
      label: 'Terület és típus',
    },
    {
      fieldType: LayoutConfiguratorFormFieldType.Checkbox,
      label: 'Budapest és körzetei',
      bindingProperty: 'showBudapestLocations',
      triggerFn: this.handleLocationChange.bind(this),
    },
    {
      fieldType: LayoutConfiguratorFormFieldType.Checkbox,
      label: 'Budapest és megyék',
      onlyFor: ['bors', 'ripost'],
      bindingProperty: 'showCountyLocationsWithBudapest',
      triggerFn: this.handleLocationChange.bind(this),
    },
    {
      fieldType: LayoutConfiguratorFormFieldType.Checkbox,
      label: 'Megyeszékhelyek',
      bindingProperty: 'showCountyLocations',
      triggerFn: this.handleLocationChange.bind(this),
    },
    {
      fieldType: LayoutConfiguratorFormFieldType.Checkbox,
      label: 'Egyéb nagyvárosok',
      bindingProperty: 'showOtherLocations',
      triggerFn: this.handleLocationChange.bind(this),
    },
    {
      fieldType: LayoutConfiguratorFormFieldType.Select,
      label: 'Alapértelmezett terület',
      displayProperty: 'name',
      initialValue: ({ defaultLocation }) => {
        return defaultLocation ? this.locations.value.find((location) => location.id === defaultLocation)?.id : this.locations.value[0].id;
      },
      explicitSource: this.locations,
      bindValue: (location: RealEstateBazaarLocation) => location?.id,
      triggerFn: (location: string, builderComponent: LayoutConfiguratorBuilderComponent) => {
        builderComponent.config.defaultLocation = location;
      },
    },
    {
      fieldType: LayoutConfiguratorFormFieldType.Select,
      label: 'Alapértelmezett típus',
      displayProperty: 'name',
      initialValue: ({ defaultType }) => {
        return defaultType ? this.types.value.find((type) => type.id === defaultType).id : this.types.value[0].id;
      },
      explicitSource: this.types,
      bindValue: (type: RealEstateBazaarType) => type?.id,
      triggerFn: (type: string, builderComponent: LayoutConfiguratorBuilderComponent) => {
        builderComponent.config.defaultType = type;
      },
    },
    {
      fieldType: LayoutConfiguratorFormFieldType.Label,
      label: 'Gombok',
    },
    {
      fieldType: LayoutConfiguratorFormFieldType.Checkbox,
      label: 'Ingatlant hirdetek gomb megjelenítése',
      bindingProperty: 'showAdvertiseButton',
    },
    {
      fieldType: LayoutConfiguratorFormFieldType.Checkbox,
      label: 'Újépítésű ingatlanok gomb megjelenítése',
      bindingProperty: 'showNewBuildButton',
    },
    {
      fieldType: LayoutConfiguratorFormFieldType.Label,
      label: 'Átirányítás',
    },
    {
      fieldType: LayoutConfiguratorFormFieldType.Text,
      label: 'UTM source',
      bindingProperty: 'utmSource',
    },
  ];

  initialConfig(): LayoutElementDynamicConfig {
    return {
      showBudapestLocations: true,
      showCountyLocations: false,
      showCountyLocationsWithBudapest: false,
      showOtherLocations: false,
      showAdvertiseButton: true,
      showNewBuildButton: true,
      defaultLocation: undefined,
      defaultType: undefined,
      utmSource: undefined,
    };
  }

  beforeOpen(config: LayoutElementDynamicConfig): void {
    this.setLocationByConfig(config);
  }

  private handleLocationChange(_: boolean, builderComponent: LayoutConfiguratorBuilderComponent): void {
    const config = builderComponent.config;
    this.setLocationByConfig(config);
  }

  private setLocationByConfig(config: LayoutElementDynamicConfig): void {
    const locations = [];
    if (config?.showBudapestLocations) {
      locations.push(...realEstateBudapestLocations);
    }
    if (config?.showCountyLocationsWithBudapest) {
      locations.push(...realEstateCountyLocationsWithBudapest);
    }
    if (config?.showOtherLocations) {
      locations.push(...realEstateOtherLocations);
    }
    if (config?.showCountyLocations) {
      locations.push(...realEstateCountyLocations);
    }
    this.locations.next([...new Set(locations)]);
  }
}
