import { inject, Injectable } from '@angular/core';
import { LayoutConfiguratorDef, LayoutElementDynamicConfig } from '../../../layout-configurator-definitions';
import { ContentElementItemType, LayoutElementContent } from '@modules/layout/layout-core.definitions';
import { SourceApiService, ViewApiService } from '../../../services';
import { IHttpOptions } from '@external/http';

@Injectable()
export class ShortVideosConfigurator implements LayoutConfiguratorDef {
  private readonly sourceApiService = inject(SourceApiService);
  private readonly viewApiService = inject(ViewApiService);
  title: string = 'Short videók konfigurálása';
  contentElementItemType: ContentElementItemType = ContentElementItemType.SHORT_VIDEOS;

  initialConfig(element: LayoutElementContent): LayoutElementDynamicConfig {
    return {
      selectedShortVideos: Array(element.contentLength).fill(null),
      disableNewestAutoFillFilter: true,
      autoFill: {
        filterColumns: [],
        filterArticleNetworkSlots: [],
        filterTags: [],
        newest: true,
      },
    };
  }

  sourceRequest(options: IHttpOptions) {
    return this.sourceApiService.getVideos({
      ...options,
      params: {
        ...options.params,
        isShort_filter: '1',
      },
    });
  }

  viewRequest(videoId: string) {
    return this.viewApiService.getVideo(videoId);
  }
}
