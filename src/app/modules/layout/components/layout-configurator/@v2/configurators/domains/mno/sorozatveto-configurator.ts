import { inject, Injectable } from '@angular/core';
import { IHttpOptions } from '@external/http';
import { ContentElementItemType, LayoutElementContent } from '@modules/layout/layout-core.definitions';
import { LayoutConfiguratorDef, LayoutElementDynamicConfig } from '@modules/layout/components/layout-configurator/@v2/layout-configurator-definitions';
import { SourceApiService, ViewApiService } from '@modules/layout/components/layout-configurator/@v2/services';
import { forkJoin } from 'rxjs';
import { ArticleCard } from '@trendency/kesma-ui';
import { ApiService } from '@core/services/api.service';
import { map } from 'rxjs/operators';

@Injectable()
export class SorozatvetoConfigurator implements LayoutConfiguratorDef {
  private readonly sourceApiService = inject(SourceApiService);
  private readonly viewApiService = inject(ViewApiService);
  private readonly apiService = inject(ApiService);

  title: string = 'Így írnak ők konfigurálása';
  contentElementItemType: ContentElementItemType = ContentElementItemType.ARTICLES;
  cannotModifyContentLength: boolean = true;
  keepOriginalWithSelection: boolean = true;

  initialConfig(element: LayoutElementContent): LayoutElementDynamicConfig {
    return {
      selectedArticles: Array(element.contentLength).fill(null),
      selectedReview: null,
      hasImage: true,
      hasTitle: true,
      hasLead: true,
    };
  }

  sourceRequest(options: IHttpOptions) {
    Object.assign(options.params, {
      isReviewable_filter: 1,
      withTimed: 1,
      opinion_filter: 0,
    });
    return this.sourceApiService.getArticles(options);
  }

  viewRequest(articleId: string) {
    return forkJoin([this.viewApiService.getArticle(articleId), this.apiService.getReviews(articleId)]).pipe(
      map(([articleView, reviews]) => ({
        data: {
          ...articleView?.data,
          thumbnail: {
            url: (articleView?.data as ArticleCard)?.thumbnailUrl,
          },
          originalReviews: reviews?.data?.map((review) => ({
            id: review?.id,
            lead: '',
            ArticleReviewBody: [],
            isHighlightedOpinion: false,
            author: {
              name: review?.publicAuthor?.fullName,
              avatarUrl: review?.publicAuthor?.avatarThumbnailUrl,
              slug: review?.publicAuthor?.slug,
            },
          })),
        },
      }))
    );
  }
}
