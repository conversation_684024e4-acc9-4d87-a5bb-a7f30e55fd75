import { Injectable } from '@angular/core';
import { ArticleConfigurator } from './article-configurator';
import { LayoutConfiguratorFormInfo, LayoutElementDynamicConfig } from '@modules/layout/components/layout-configurator/@v2/layout-configurator-definitions';
import { LayoutElementContent } from '@modules/layout/layout-core.definitions';

type LayoutElementContentPr = LayoutElementContent & {
  hasImg?: boolean;
};

@Injectable()
export class PrArticleConfigurator extends ArticleConfigurator {
  isArraySizeAffected: boolean = this.domainService.isMandiner();
  override title: string = 'PR cikkek konfigurálása';
  override formInfo: LayoutConfiguratorFormInfo[] = this.formInfo
    .filter(({ bindingProperty }) => ['foregroundColor', 'backgroundColor'].includes(bindingProperty))
    .map((fieldInfo) => ({
      ...fieldInfo,
      showIf: ({ styleId }): boolean => {
        if (this.domainService.isRipost()) {
          return styleId === 4;
        }
        return true;
      },
    }));

  override initialConfig(element: LayoutElementContentPr): LayoutElementDynamicConfig {
    return {
      leadingArticle: false,
      selectedArticles: Array(element.contentLength).fill(null),
      doAutoFill: true,
      title: 'Szponzorált tartalom',
      hasImage: element?.hasImg,
      autoFill: {
        filterColumns: [],
        filterPriorities: [],
        filterTags: [],
        filterRegions: [],
        filterSports: [],
        filterSponsorships: [],
        filterPrTags: null,
        filterArticleNetworkSlots: null,
        orderPriority: 'date',
        newest: false,
      },
    };
  }
}
