import { ChangeDetectionStrategy, Component, OnD<PERSON>roy, OnInit, signal, viewChildren } from '@angular/core';
import { BaseLayoutConfiguratorComponent } from '@modules/layout/components/layout-configurator/@v2/components';
import {
  LayoutConfigurationResult,
  LayoutConfiguratorFormFieldType,
  LayoutConfiguratorFormInfo,
  LayoutElementDynamicConfig,
} from '@modules/layout/components/layout-configurator/@v2/layout-configurator-definitions';
import { SelectPageableHttpLoader } from '@shared/modules/form-controls/components/selects/select-form-control/select-pageable-http-loader';
import { DomainKey } from '@core/modules/admin/admin.definitions';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { NzSwitchComponent } from 'ng-zorro-antd/switch';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { catchError, tap } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { isObject } from 'lodash';
import get from 'lodash/get';
import set from 'lodash/set';
import { forkJoin, Observable, of, take } from 'rxjs';
import { resizeArrayWithNulls } from '@modules/layout/components/layout-configurator/@v2/layout-configurator-utils';
import { cloneDeep } from 'lodash-es';
import { LayoutElementContentType } from '@modules/layout/layout-core.definitions';

@Component({
  selector: 'app-layout-configurator-builder',
  templateUrl: 'layout-configurator-builder.component.html',
  styleUrl: 'layout-configurator-builder.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class LayoutConfiguratorBuilderComponent extends BaseLayoutConfiguratorComponent implements OnInit, OnDestroy {
  protected isTimedList = viewChildren<NzSwitchComponent>('isTimedList');

  isLoading = signal<boolean>(false);
  initialConfig = signal<LayoutElementDynamicConfig>(null);
  isSeoOptionsEnabled = false;

  readonly LayoutConfiguratorFormFieldType = LayoutConfiguratorFormFieldType;

  readonly defaultMaxLength: number = 15;
  readonly defaultMinLength: number = 1;
  readonly placeholderImg: string = '/assets/images/layout-frames/mno/image-placeholder.png';

  contentLoader?: SelectPageableHttpLoader<LayoutElementDynamicConfig>;

  ngOnInit(): void {
    super.ngOnInit();
    this.initialConfig.set(this.configurator?.initialConfig?.(this.element));
    const { contentElementItemType } = this.configurator;
    if (contentElementItemType) {
      this.createSourceRequest();
    }
    this.isSeoOptionsEnabled =
      ['mindmegette', 'bors', 'magyarNemzet'].includes(this.domain) &&
      [LayoutElementContentType.RECIPE, LayoutElementContentType.ARTICLE, LayoutElementContentType.GUARANTEE_BOX].includes(this.element.contentType);
  }

  ngOnDestroy(): void {
    this.contentLoader?.close();
  }

  get hasAutoFill(): boolean {
    // We used autoFill of initialConfig for backwards compatibility.
    const autoFill = this.initialConfig()?.autoFill;
    return !this.configurator?.forceHideAutoFill && !!Object.keys(autoFill || {})?.length;
  }

  /**
   * Checks if the configuration for the specified content element item type is a valid array.
   * @returns True if the config property is a valid array, false otherwise.
   */
  get isConfigArrayValid(): boolean {
    return Array.isArray(this.config[this.configurator?.contentElementItemType]);
  }

  /**
   *  Retrieves the configuration as an array of {@link LayoutConfigurationResult} objects.
   */
  get configAsArray(): LayoutConfigurationResult[] {
    const config = this.config[this.configurator?.contentElementItemType];
    return (config?.length ? config : [null]) as LayoutConfigurationResult[];
  }

  /**
   * Retrieves the formInfo if there is form information for the current domain.
   */
  get formInfoForDomain(): LayoutConfiguratorFormInfo[] {
    const formInfo = this.configurator?.formInfo;
    if (!formInfo) {
      return [];
    }
    // Check if at least one field meets the conditions.
    return formInfo.filter(
      (field) =>
        (!field?.showIf || field?.showIf?.(this.element, this.config)) &&
        (!field?.onlyFor || field?.onlyFor?.includes(this.domain)) &&
        (!field?.hideFor || !field?.hideFor?.includes(this.domain)) &&
        (!field?.requiredPortalConfig || this.portalConfigService.isConfigSet(field.requiredPortalConfig))
    );
  }

  /**
   * Retrieves the current domain key.
   */
  get domain(): DomainKey {
    return this.domainService.currentDomain.key;
  }

  /**
   * Retrieves and updates the original content for the given item, if necessary.
   *
   * This method checks if the item has an existing `original` value or if the request
   * has already been made. If not, it triggers the `viewRequest` function to fetch
   * the data and updates the `item` with the fetched content. If any of the conditions
   * are not met (i.e., missing request, missing item ID, or already having original data),
   * the function will exit early without making a request.
   *
   * @param selectedContent Details of the selected content.
   * @param index Index of the selected content. (provided when selected content is an array)
   * @param shifting Handles content selection by shifting, removing the last one in the process.
   */
  protected viewRequest(selectedContent: LayoutConfigurationResult, index?: number, shifting?: boolean): Observable<unknown> {
    const request = this.configurator?.viewRequest?.bind(this.configurator);
    const itemId = selectedContent?.id;
    // If there's no request, itemId, or the item already has original data, return early.
    if (!request || !itemId) {
      return;
    }
    this.isLoading.set(true);
    return request(itemId, selectedContent?.original || selectedContent).pipe(
      takeUntilDestroyed(this.destroyRef),
      tap(({ data: original }) => {
        this.isLoading.set(false);
        const config = {
          ...(this.configurator?.keepOriginalWithSelection ? selectedContent : {}),
          ...(selectedContent?.overwrite ? { overwrite: selectedContent?.overwrite || null } : {}),
          ...(selectedContent?.foregroundColor ? { foregroundColor: selectedContent?.foregroundColor } : {}),
          ...(selectedContent?.backgroundColor ? { backgroundColor: selectedContent?.backgroundColor } : {}),
          id: itemId,
          visibleFrom: selectedContent?.visibleFrom,
          visibleUntil: selectedContent?.visibleUntil,
          title: selectedContent?.title || original?.title,
          original: { ...(selectedContent?.original ? selectedContent.original : {}), ...original },
        };
        this.setContentWithShifting(config, index, shifting);
      }),
      catchError((error: HttpErrorResponse) => {
        this.isLoading.set(false);
        console.error(`The endpoint is broken with the following error status: ${error.status}! Message: ${error.message}`);
        return of(null);
      })
    );
  }

  updateWithViewRequest(selectedContent: LayoutConfigurationResult, index?: number, shifting?: boolean): void {
    const request$ = this.viewRequest(selectedContent, index, shifting);
    if (request$?.subscribe) {
      request$.pipe(take(1)).subscribe();
    } else {
      this.setContentWithShifting(selectedContent, index, shifting);
    }
    this.selectChange.emit({ value: selectedContent, index });
  }

  /**
   * Clears the date range on the selected content to avoid unnecessary inclusion when saving the layout.
   * @param selectedContent The content whose date range will be cleared.
   */
  clearDateRange(selectedContent: LayoutConfigurationResult): void {
    // Check if selectedContent is valid before attempting to modify its properties.
    if (selectedContent) {
      selectedContent.visibleFrom = undefined;
      selectedContent.visibleUntil = undefined;
      return;
    }
    throw Error(`Attempted to clear date range on invalid selectedContent: ${selectedContent}`);
  }

  /**
   * Determines if the item at the given index is timed and checked.

   * This method checks whether the `configurator.canTiming` flag is truthy and
   * whether the item at the specified index in the timed list is marked as checked.
   *
   * @param index The index of the item to check.
   * @returns True if the item is both timed and checked, false otherwise.
   */
  isTimed(index?: number): boolean {
    const timedList = this.isTimedList();
    if (index === undefined) {
      return Boolean(this.configurator?.canTiming && timedList.at(0)?.isChecked);
    }
    return Boolean(this.configurator?.canTiming && timedList.at(index)?.isChecked);
  }

  getContentDisplayedTitle(config: LayoutElementDynamicConfig): string {
    return this.configurator?.contentElementItemDisplayedTitle ? this.configurator.contentElementItemDisplayedTitle(config) : config?.title;
  }

  /**
   * Retrieves the initial value for a form field based on the provided form info.
   *
   * > If the `bindingProperty` exists, fetches its value from the config.
   * > If the `defaultValue` is a function, calls it with the current config.
   * > If the `defaultValue` exists (but isn't a function), returns it directly.
   * > Returns `null` if no valid value is found.
   */
  getInputInitialValue(formInfo: LayoutConfiguratorFormInfo) {
    if (formInfo?.bindingProperty) {
      return get(this.config, formInfo.bindingProperty);
    }
    if (typeof formInfo?.initialValue === 'function') {
      return formInfo.initialValue(this.config);
    }
    if (formInfo?.initialValue) {
      return formInfo?.initialValue;
    }
    return null;
  }

  /**
   * Handles the change event for a form field.
   */
  onValueChange(formInfo: LayoutConfiguratorFormInfo, value: unknown): void {
    if (formInfo?.bindingProperty) {
      set(this.config, formInfo.bindingProperty, value);
    }
    if (formInfo?.triggerFn) {
      formInfo?.triggerFn(value, this);
    }
  }

  onContentLengthChange(newLength: number): void {
    this.config[this.configurator?.contentElementItemType] = resizeArrayWithNulls(this.config[this.configurator?.contentElementItemType], newLength);
    if (!this.configurator?.isArraySizeAffected && !this.configurator?.cannotModifyContentLength) {
      this.element.contentLength = newLength;
    }
  }

  /**
   * Handles the drop event for rearranging items in an array.
   * @param event The drag-and-drop event containing information about the item movement.
   * @protected
   */
  protected drop(event: CdkDragDrop<unknown[]>) {
    // Ensure that the config property is valid and the contentElementItemType exists
    const items = this.config[this.configurator.contentElementItemType];
    if (Array.isArray(items)) {
      moveItemInArray(items, event.previousIndex, event.currentIndex);
      return;
    }
    throw Error('Invalid items array!');
  }

  createSourceRequest(): void {
    const request$ = this.configurator?.sourceRequest?.bind?.(this.configurator);
    if (!request$) {
      return;
    }
    let itemRequests = null;
    const selectedItems = this.config[this.configurator.contentElementItemType];
    if (Array.isArray(selectedItems)) {
      itemRequests = selectedItems.map((item, index) => item?.id && this.viewRequest(item, index)).filter(Boolean);
    } else {
      if (isObject(selectedItems)) {
        itemRequests = [this.viewRequest(selectedItems as LayoutConfigurationResult)].filter(Boolean);
      }
    }
    this.contentLoader = new SelectPageableHttpLoader<unknown>((options) => {
      if (this.columnId && this.configurator?.useColumnFilterIfExists) {
        Object.assign(options.params, { 'columns_filter[]': this.columnId });
      }
      return request$(options, this.element, this.config);
    });
    if (itemRequests?.length) {
      forkJoin(itemRequests)
        .pipe(take(1))
        .subscribe(() => {
          const initialData = this.getInitialDataForLoader();
          this.contentLoader.init(initialData);
        });
      return;
    }
    const initialData = this.getInitialDataForLoader();
    this.contentLoader.init(initialData);
  }

  private getInitialDataForLoader(): unknown[] {
    const { contentElementItemType } = this.configurator;
    if (!contentElementItemType) {
      return [];
    }
    const existingConfigItems = this.config[contentElementItemType];
    if (Array.isArray(existingConfigItems)) {
      return existingConfigItems.filter((item) => !!item).map((item) => ('original' in item ? { ...item, ...item.original } : item));
    }
    if (isObject(existingConfigItems)) {
      const hasOriginal = 'original' in existingConfigItems && isObject(existingConfigItems.original);
      return [hasOriginal ? { ...existingConfigItems, ...(existingConfigItems.original as object) } : existingConfigItems];
    }
    return [];
  }

  private setContentWithShifting(config: LayoutConfigurationResult, index: number, shifting?: boolean): void {
    if (shifting && this.isConfigArrayValid && config && this.config?.[this.configurator.contentElementItemType]?.[index]) {
      const items = this.config[this.configurator.contentElementItemType];
      items.splice(index, 0, config);
      items.pop();
      this.config[this.configurator.contentElementItemType] = cloneDeep(items);
      this.cdr.detectChanges();
      return;
    }
    if (!isNaN(index) && this.isConfigArrayValid) {
      this.config[this.configurator.contentElementItemType][index] = config;
    } else {
      this.config[this.configurator.contentElementItemType] = config;
    }
    this.cdr.markForCheck();
  }

  nzMinValue(value): number {
    return isNaN(value) ? this.defaultMinLength : value;
  }
}
