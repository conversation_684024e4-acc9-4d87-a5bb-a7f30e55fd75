<div class="select-group">
  @if (columnsLoader) {
    <ng-container
      *ngTemplateOutlet="
        selectTemplate;
        context: {
          label: 'Rovatok',
          filterKey: 'filterColumns',
          loader: columnsLoader,
        }
      "
    >
    </ng-container>
  }
  @if (publicAuthorsLoader) {
    <ng-container
      *ngTemplateOutlet="
        selectTemplate;
        context: {
          label: 'Szerzők',
          filterKey: 'filterAuthors',
          loader: publicAuthorsLoader,
          displayProperty: 'fullName',
        }
      "
    >
    </ng-container>
  }
  @if (recipeCategoriesLoader) {
    <ng-container
      *ngTemplateOutlet="
        selectTemplate;
        context: {
          label: 'Recept kateg<PERSON><PERSON>',
          filterKey: 'filterRecipeCategories',
          loader: recipeCategoriesLoader,
        }
      "
    >
    </ng-container>
  }
  @if (tagsLoader) {
    <ng-container
      *ngTemplateOutlet="
        selectTemplate;
        context: {
          label: 'Címkék',
          filterKey: 'filterTags',
          loader: tagsLoader,
        }
      "
    >
    </ng-container>
  }
  @if (sponsorshipsLoader) {
    <ng-container
      *ngTemplateOutlet="
        selectTemplate;
        context: {
          label: 'Szponzorációk',
          filterKey: 'filterSponsorships',
          loader: sponsorshipsLoader,
        }
      "
    >
    </ng-container>
  }
  @if (prioritiesLoader) {
    <ng-container
      *ngTemplateOutlet="
        selectTemplate;
        context: {
          label: 'Prioritások',
          filterKey: 'filterPriorities',
          translate: true,
          loader: prioritiesLoader,
        }
      "
    >
    </ng-container>
  }
  @if (articleNetworkSlotsLoader) {
    <ng-container
      *ngTemplateOutlet="
        selectTemplate;
        context: {
          label: 'Hálózati helyek',
          filterKey: 'filterArticleNetworkSlots',
          loader: articleNetworkSlotsLoader,
          nzMode: 'default',
        }
      "
    >
    </ng-container>
  }
  @if (dossierNetworkSlotsLoader) {
    <ng-container
      *ngTemplateOutlet="
        selectTemplate;
        context: {
          label: 'Dosszié hálózati helyek',
          filterKey: 'filterDossierNetworkSlots',
          loader: dossierNetworkSlotsLoader,
          nzMode: 'default',
        }
      "
    >
    </ng-container>
  }
  @if (filterVotingNetworkSlotsLoader) {
    <ng-container
      *ngTemplateOutlet="
        selectTemplate;
        context: {
          label: 'Szavazás hálózati helyek',
          filterKey: 'filterVotingNetworkSlots',
          loader: filterVotingNetworkSlotsLoader,
          nzMode: 'default',
        }
      "
    >
    </ng-container>
  }
  @if (regionsLoader) {
    <ng-container
      *ngTemplateOutlet="
        selectTemplate;
        context: {
          label: 'Régiók',
          filterKey: 'filterRegions',
          loader: regionsLoader,
        }
      "
    >
    </ng-container>
  }
  @if (sportsLoader) {
    <ng-container
      *ngTemplateOutlet="
        selectTemplate;
        context: {
          label: 'Sportok',
          filterKey: 'filterSports',
          loader: sportsLoader,
        }
      "
    >
    </ng-container>
  }
  @if (prTagsLoader) {
    <ng-container
      *ngTemplateOutlet="
        selectTemplate;
        context: {
          label: 'PR Címkék',
          filterKey: 'filterPrTags',
          loader: prTagsLoader,
        }
      "
    >
    </ng-container>
  }
  @if (programTypesLoader) {
    <ng-container
      *ngTemplateOutlet="
        selectTemplate;
        context: {
          label: 'Program típusok',
          filterKey: 'filterTypes',
          loader: programTypesLoader,
        }
      "
    >
    </ng-container>
  }
  @if (programLocationsLoader) {
    <ng-container
      *ngTemplateOutlet="
        selectTemplate;
        context: {
          label: 'Program helyszínek',
          filterKey: 'filterLocations',
          loader: programLocationsLoader,
        }
      "
    >
    </ng-container>
  }
  @if (experienceCategoriesLoader) {
    <ng-container
      *ngTemplateOutlet="
        selectTemplate;
        context: {
          label: 'Élmény kategóriák',
          filterKey: 'filterExperienceCategories',
          loader: experienceCategoriesLoader,
          nzMode: 'multiple',
        }
      "
    >
    </ng-container>
  }
  @if (experienceLoader) {
    <ng-container
      *ngTemplateOutlet="
        selectTemplate;
        context: {
          label: 'Élmények',
          filterKey: 'filterExperience',
          loader: experienceLoader,
          nzMode: 'default',
        }
      "
    >
    </ng-container>
  }
  @if (brandingBoxTypesLoader) {
    <ng-container
      *ngTemplateOutlet="
        selectTemplate;
        context: {
          label: 'Branding típusok',
          filterKey: 'filterBrandingBoxTypes',
          displayProperty: 'name',
          loader: brandingBoxTypesLoader,
          nzMode: 'default',
        }
      "
    >
    </ng-container>
  }
</div>

@if (initialConfig().autoFill?.orderPriority && config().autoFill?.orderPriority) {
  <nz-form-label nzNoColon>Rendezési beállítások:</nz-form-label>
  <nz-radio-group [(ngModel)]="config().autoFill.orderPriority" nzButtonStyle="solid" class="w-100">
    @for (setting of sortSettings(); track setting.value) {
      <label nz-radio-button [nzValue]="setting.value">{{ setting.text }}</label>
    }
  </nz-radio-group>
}

@if (initialConfig().autoFill?.newest !== undefined && config().autoFill?.newest !== undefined) {
  <nz-form-label nzNoColon class="mt-16">Megjelenítési módok:</nz-form-label>
  <div class="block">
    <nz-switch
      class="w-100"
      [nzDisabled]="initialConfig()?.disableNewestAutoFillFilter"
      nzCheckedChildren="Mindig a legfrissebb megjelenítése"
      nzUnCheckedChildren="Mindig a legfrissebb megjelenítése"
      [(ngModel)]="config().autoFill.newest"
    >
    </nz-switch>
  </div>
}

<!-- MME order priorities: -->
<ng-container *onlyFor="'mindmegette'">
  @if (initialConfig().autoFill?.filterOnlyMmeWarranty !== undefined) {
    <div class="block">
      <nz-switch
        class="w-100 mt-10"
        nzCheckedChildren="Garanciás tartalom"
        nzUnCheckedChildren="Garanciás tartalom"
        [(ngModel)]="config().autoFill.filterOnlyMmeWarranty"
      >
      </nz-switch>
    </div>
  }
  @if (initialConfig().autoFill?.hasImage !== undefined) {
    <div class="block">
      <nz-switch
        class="w-100 mt-10"
        nzCheckedChildren="Elsődleges képpel rendelkező"
        nzUnCheckedChildren="Elsődleges képpel rendelkező"
        [(ngModel)]="config().autoFill.hasImage"
      >
      </nz-switch>
    </div>
  }
  @if (initialConfig().autoFill?.hasSecondaryImage !== undefined) {
    <div class="block">
      <nz-switch
        class="w-100 mt-10"
        nzCheckedChildren="Másodlagos képpel rendelkező"
        nzUnCheckedChildren="Másodlagos képpel rendelkező"
        [(ngModel)]="config().autoFill.hasSecondaryImage"
      >
      </nz-switch>
    </div>
  }
  @if (initialConfig().autoFill?.hasVideo !== undefined) {
    <div class="block">
      <nz-switch class="w-100 mt-10" nzCheckedChildren="Videós tartalom" nzUnCheckedChildren="Videós tartalom" [(ngModel)]="config().autoFill.hasVideo">
      </nz-switch>
    </div>
  }
</ng-container>

<ng-template
  #selectTemplate
  let-label="label"
  let-loader="loader"
  let-filterKey="filterKey"
  let-nzMode="nzMode"
  let-translate="translate"
  let-displayProperty="displayProperty"
>
  <nz-form-item class="select">
    <nz-form-label nzNoColon>{{ label }}:</nz-form-label>
    <nz-select
      class="w-100"
      [nzMode]="nzMode || 'multiple'"
      [nzPlaceHolder]="label"
      [nzAllowClear]="true"
      [nzServerSearch]="true"
      [nzShowSearch]="true"
      [compareWith]="loader.compareFn"
      [nzLoading]="loader.isLoading$ | async"
      (nzScrollToBottom)="loader.loadMore()"
      (nzOnSearch)="loader.search($event)"
      [(ngModel)]="config().autoFill[filterKey]"
    >
      @for (data of loader.data$ | async; track data.id) {
        <nz-option [nzLabel]="displayProperty ? data[displayProperty] : translate ? (data.title | translate) : data.title" [nzValue]="data"> </nz-option>
      }
    </nz-select>
  </nz-form-item>
</ng-template>
