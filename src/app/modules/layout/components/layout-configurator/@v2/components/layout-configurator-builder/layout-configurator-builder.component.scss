:host {
  display: block;
  .title {
    font-weight: 700;
    margin-bottom: 5px;
  }
  .form-item {
    display: flex;
    align-items: center;
    max-width: 100%;
    width: 100%;
    margin-bottom: 5px; // Override <nz-form-item> margin
    nz-form-control,
    nz-input-number,
    nz-select {
      width: inherit;
      overflow: hidden;
    }
    &-wrapper {
      display: flex;
      flex-direction: column;
      width: 100%;
      overflow: hidden;
      gap: 10px;
    }
  }
  .label {
    font-size: 16px;
    font-weight: 700;
    margin-top: 10px;
  }
  .no-shrink {
    flex-shrink: 0;
  }
  .w-100 {
    width: 100%;
  }
  .align-center {
    align-items: center;
  }
  .flex {
    display: flex;
    gap: 10px;
  }
  .has-thumbnail::ng-deep {
    nz-select,
    .ant-select[class] .ant-select-selector,
    nz-select-top-control {
      height: 62px;
    }
    .ant-select-selection-placeholder {
      display: flex;
      align-items: center;
    }
  }
}
::ng-deep {
  .configurator-modal {
    .ant-modal-footer .ant-btn-primary {
      width: 100%;
    }
  }

  :has(.has-thumbnail) {
    cdk-virtual-scroll-viewport {
      min-height: 65px;
    }
  }
}

.block {
  display: block;
  margin-bottom: 10px;
}
.image-thumbnail {
  object-fit: cover;
  height: 55px;
  width: 70px;
  border: 1px solid black;
  margin-right: 10px;
}
