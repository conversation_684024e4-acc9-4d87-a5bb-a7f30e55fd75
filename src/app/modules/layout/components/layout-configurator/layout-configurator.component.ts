import { Component, HostListener, inject, Input, OnChanges, OnInit, SimpleChanges, ViewContainerRef } from '@angular/core';
import { SharedService } from '@shared/services/shared.service';
import { LayoutElementContentConfigurationShortVideos, LayoutIframeService } from '@trendency/kesma-ui';
import { NzModalService } from 'ng-zorro-antd/modal';
import { DomainService } from 'src/app/core/modules/admin/services/domain.service';
import { ApiService } from 'src/app/core/services/api.service';
import {
  LayoutElementContentConfiguration,
  LayoutElementContentConfigurationArticle,
  LayoutElementContentConfigurationArticleBlock,
  LayoutElementContentConfigurationArticlesWithPodcast,
  LayoutElementContentConfigurationAuthor,
  LayoutElementContentConfigurationBlog,
  LayoutElementContentConfigurationBrandingBox,
  LayoutElementContentConfigurationBrandingBoxEx,
  LayoutElementContentConfigurationBreaking,
  LayoutElementContentConfigurationCategoryStepper,
  LayoutElementContentConfigurationCountdownBox,
  LayoutElementContentConfigurationDetections,
  LayoutElementContentConfigurationDossier,
  LayoutElementContentConfigurationDossierList,
  LayoutElementContentConfigurationDossierRepeater,
  LayoutElementContentConfigurationEbCountdownBlockTitle,
  LayoutElementContentConfigurationEBNews,
  LayoutElementContentConfigurationFastNews,
  LayoutElementContentConfigurationGallery,
  LayoutElementContentConfigurationGastroOccasionExperienceSwiper,
  LayoutElementContentConfigurationGastroThematicRecommender,
  LayoutElementContentConfigurationGPNewsBox,
  LayoutElementContentConfigurationGuaranteeBox,
  LayoutElementContentConfigurationHighlightedSelection,
  LayoutElementContentConfigurationHtmlEmbed,
  LayoutElementContentConfigurationImage,
  LayoutElementContentConfigurationIngatlanbazarSearch,
  LayoutElementContentConfigurationIngredient,
  LayoutElementContentConfigurationLinkList,
  LayoutElementContentConfigurationMaestroBox,
  LayoutElementContentConfigurationMinuteToMinutes,
  LayoutElementContentConfigurationMoreArticles,
  LayoutElementContentConfigurationNote,
  LayoutElementContentConfigurationOfferBox,
  LayoutElementContentConfigurationOlimpiaArticleWithPodcast,
  LayoutElementContentConfigurationOlimpiaCountdownBlockTitle,
  LayoutElementContentConfigurationOlimpiaNews,
  LayoutElementContentConfigurationOpinion,
  LayoutElementContentConfigurationProgram,
  LayoutElementContentConfigurationPublicAuthors,
  LayoutElementContentConfigurationQuiz,
  LayoutElementContentConfigurationRecipe,
  LayoutElementContentConfigurationRecipeCategorySelect,
  LayoutElementContentConfigurationSecretDaysCalendar,
  LayoutElementContentConfigurationselectedDidYouKnowBox,
  LayoutElementContentConfigurationSelection,
  LayoutElementContentConfigurationServicesBox,
  LayoutElementContentConfigurationShortNews,
  LayoutElementContentConfigurationSorozatveto,
  LayoutElementContentConfigurationSportBlock,
  LayoutElementContentConfigurationTabsBlock,
  LayoutElementContentConfigurationTagStrip,
  LayoutElementContentConfigurationTeams,
  LayoutElementContentConfigurationTextBox,
  LayoutElementContentConfigurationTopTenTags,
  LayoutElementContentConfigurationTrendingTags,
  LayoutElementContentConfigurationTurpiBox,
  LayoutElementContentConfigurationTurpiCard,
  LayoutElementContentConfigurationVideo,
  LayoutElementContentConfigurationVote,
  LayoutElementContentConfigurationWithId,
} from 'src/app/modules/layout/layout-configuration.definitions';
import {
  LayoutElementContentArticlesWithPodcastContent,
  LayoutElementContentGallery,
  LayoutElementContentGPNewsBox,
  LayoutElementContentImage,
  LayoutElementContentOlimpiaArticlesWithPodcastContent,
  LayoutElementContentRelatedArticles,
  LayoutElementContentSelection,
  LayoutElementContentTabsBlock,
} from 'src/app/modules/layout/layout-content.definitions';
import {
  ContentElementItemType,
  DetectionType,
  LayoutElement,
  LayoutElementColumn,
  LayoutElementContent,
  LayoutElementContentType,
  LayoutElementRow,
  LayoutElementType,
} from 'src/app/modules/layout/layout-core.definitions';
import { DestroyService } from 'src/app/shared/services/destroy.service';
import { DomainKey } from '@core/modules/admin/admin.definitions';
import { cloneDeep } from 'lodash-es';
import { LAYOUT_CONFIGURATORS } from '@modules/layout/components/layout-configurator/@v2/layout-configurator-token';
import { LayoutConfiguratorBuilderComponent } from '@modules/layout/components/layout-configurator/@v2/components';
import {
  LayoutConfigurator,
  LayoutConfiguratorDef,
  LayoutConfiguratorOverwriteDef,
  LayoutElementDynamicConfig,
} from '@modules/layout/components/layout-configurator/@v2/layout-configurator-definitions';
import get from 'lodash/get';
import { LayoutConfiguratorBlockTitleComponent } from '@modules/layout/components/layout-configurator/@v2/components/configurators';

@Component({
  selector: 'app-layout-configurator',
  template: ``,
  providers: [DestroyService],
  standalone: false,
})
export class LayoutConfiguratorComponent implements OnInit, OnChanges {
  @Input() type: 'HomePage' | 'Column' | 'Opinion' | 'Sidebar' | 'ColumnSidebar' | 'CustomBuiltPage';
  @Input() structure: LayoutElementRow[] = [];
  @Input() configuration: LayoutElementDynamicConfig[] = [];

  @Input() columnId = '';
  @Input() isReadonly: boolean = false;
  @Input() isDisabled: boolean;

  @Input() set domain(domainKey: DomainKey) {
    this.domainKey = domainKey;
  }

  domainKey: DomainKey;
  LayoutElementContentType = LayoutElementContentType;

  private readonly configurators = inject<LayoutConfigurator[]>(LAYOUT_CONFIGURATORS, { optional: true });

  constructor(
    protected readonly modal: NzModalService,
    protected readonly viewContainerRef: ViewContainerRef,
    protected readonly api: ApiService,
    protected readonly domainService: DomainService,
    protected readonly destroy$: DestroyService,
    protected readonly sharedService: SharedService,
    protected readonly layoutIframeService: LayoutIframeService
  ) {}

  ngOnInit(): void {
    this.api.getArticleNetworkSlots().subscribe((s) => {
      console.log('%d article network slots loaded', s?.data?.length);
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.structure) {
      this.checkOrSetInitialConfigs(changes.structure.currentValue);
    }
  }

  /**
   * Globally handles the configuration modal for all existing elements.
   * Based on the received ${@link LayoutElementContent} type, it searches for the corresponding configurator.
   * The received configurator contains all the data necessary for constructing the editor.
   *
   * When the index received as a parameter is a real number, it loads the overwrite configuration if available.
   */
  openContentConfiguratorModal(element: LayoutElementContent, index?: number): void {
    const isOverwrite = !isNaN(index);
    const configurator = this.getConfiguratorByContentType(element.contentType);
    const configuratorInstance = isOverwrite ? configurator?.overwriteInstance : configurator?.instance;
    const layoutConfig = this.getConfig(element);
    const config = this.getConfigByModalType(layoutConfig, configuratorInstance, configurator?.allowFullOverwrite ? undefined : index);
    if (!configuratorInstance) {
      console.warn(`Missing configurator for ${element.contentType}, or bad contentType in openContentConfiguratorModal`);
      return;
    }
    if (configuratorInstance?.beforeOpen) {
      configuratorInstance.beforeOpen(config, element);
    }
    const customComponent = isOverwrite ? configurator?.overwriteCustomComponent : configurator?.customComponent;
    const modalRef = this.modal.create({
      nzTitle: configuratorInstance.title,
      nzContent: customComponent || LayoutConfiguratorBuilderComponent,
      nzWidth: configuratorInstance?.width || 900,
      nzClassName: 'configurator-modal',
      nzFooter: [
        {
          type: 'primary',
          label: `Adatok ${isOverwrite ? `felülírása` : `mentése`}`,
          onClick: ({ config, element: modifiedElement }) => {
            const { beforeClose, contentElementItemType, secondaryContentElementItemType } = configuratorInstance;
            const modifiedConfig = beforeClose ? beforeClose(config, element) : config;
            const configIndex = this.getConfigIndex(element);
            // Check if there is an empty required field in the configuration.
            if (this.hasMissingRequiredFields(configuratorInstance, modifiedConfig) || !modifiedConfig) {
              return;
            }
            if (isOverwrite && !configurator?.allowFullOverwrite) {
              const contentType = secondaryContentElementItemType || contentElementItemType;
              this.configuration[configIndex][contentType][index] = config;
            } else {
              this.configuration[configIndex] = modifiedConfig;
            }
            element.contentLength = modifiedElement.contentLength;
            modalRef.destroy();
          },
        },
      ],
      nzData: {
        element: cloneDeep(element),
        config,
        configurator: configuratorInstance,
        columnId: this.columnId,
      },
    });
  }

  openBlockTitleModal(element: LayoutElementContent): void {
    const modalRef = this.modal.create({
      nzTitle: `Blokk cím konfigurálása`,
      nzContent: LayoutConfiguratorBlockTitleComponent,
      nzWidth: 900,
      nzClassName: 'configurator-modal',
      nzFooter: [
        {
          type: 'primary',
          label: `Adatok mentése`,
          onClick: ({ blockTitleConfigurator, config }) => {
            const { canClose } = blockTitleConfigurator;
            // Check if there is an empty required field in the configuration.
            if (!canClose(config) || this.hasMissingRequiredFields(blockTitleConfigurator, config)) {
              return;
            }
            element.blockTitle = config.blockTitle;
            modalRef.destroy();
          },
        },
      ],
      nzData: {
        config: cloneDeep(element),
      },
    });
  }

  checkOrSetInitialConfigs(elements: LayoutElement[]): void {
    if (!elements?.length) {
      return;
    }
    elements.forEach((element: LayoutElement) => {
      const layoutElement = element as LayoutElementContent;
      if (!layoutElement) {
        return;
      }
      if ([LayoutElementType.ROW, LayoutElementType.COLUMN].includes(element.type)) {
        this.checkOrSetInitialConfigs((element as LayoutElementColumn).elements);
        return;
      }
      const configurator = this.getConfiguratorByContentType(layoutElement.contentType)?.instance;
      if (!configurator) {
        console.warn(`Missing configurator for ${layoutElement.contentType}, or bad contentType!`);
        return;
      }
      if (!layoutElement?.configurable && !configurator?.shouldForceInitialConfig) {
        return;
      }
      const config = this.getConfig(layoutElement);
      if (config) {
        return;
      }
      const initialConfig = configurator.initialConfig(layoutElement);
      this.configuration.push({
        layoutElementId: layoutElement.id,
        ...initialConfig,
      });
      if (configurator?.initialConfigAsync) {
        const initialConfig = this.getConfig(layoutElement);
        configurator.initialConfigAsync(initialConfig, layoutElement);
      }
    });
  }

  /**
   * Checks if there are any missing required fields in the given configurator for the provided configuration.
   * If any required field is missing, a notification is shown listing the missing fields.
   *
   * @returns `true` if there are missing required fields, otherwise `false`.
   * @private
   */
  private hasMissingRequiredFields(configurator: LayoutConfiguratorDef | LayoutConfiguratorOverwriteDef, config: LayoutElementDynamicConfig): boolean {
    const { contentElementsAreRequired, contentElementItemType } = configurator;
    // Pre-check: Verifies if the content elements are required and if they contain valid items.
    if (contentElementsAreRequired && contentElementItemType) {
      const content = config[contentElementItemType];
      if ((Array.isArray(content) && content.includes(null)) || content === null) {
        this.sharedService.showNotification(`error`, `Explicit tartalmak kitöltése kötelező!`);
        // Return true if invalid content is detected (null values).
        return true;
      }
    }
    // If the configurator does not have formInfo, there are no required fields to check.
    if (!configurator?.formInfo) {
      return false;
    }
    // Find the unfilled required fields by filtering formInfo based on required flag and missing binding property.
    const unfilledFields = configurator.formInfo
      .filter(({ required, bindingProperty }) => {
        const value = get(config, bindingProperty);
        if (Array.isArray(value)) {
          return required && !value?.length;
        }
        return required && !value;
      })
      .map(({ label }) => label);

    // If there are no unfilled required fields, return false.
    if (unfilledFields.length === 0) {
      return false;
    }
    // Show notification with the list of missing fields.
    this.sharedService.showNotification(`error`, `Nincs kitöltve minden kötelező mező! Hiányzó mezők: ${unfilledFields.join(`, `)}`);
    return true;
  }

  /**
   * Returns the full configuration for a layout element, or if the index is a real number,
   * it returns the element at the specified index from the configuration.
   *
   * @example
   * index = 0;
   * contentElementItemType = selectedArticles;
   * It returns config[selectedArticles][0];
   *
   * @private
   */
  private getConfigByModalType(
    config: LayoutElementDynamicConfig,
    configurator: LayoutConfiguratorDef | LayoutConfiguratorOverwriteDef,
    index: number
  ): LayoutElementDynamicConfig {
    if (!isNaN(index)) {
      const contentType = configurator?.secondaryContentElementItemType || configurator?.contentElementItemType;
      return cloneDeep(config?.[contentType]?.[index]);
    }
    return cloneDeep(config);
  }

  /**
   * Returns the configuration service associated with the given type.
   * @private
   */
  private getConfiguratorByContentType(contentType: LayoutElementContentType): LayoutConfigurator {
    return this.configurators.find((configurator) => configurator?.supportedContentTypes.includes(contentType));
  }

  public updateConfigElementLength(layoutElementId: string, contentType: LayoutElementContentType, itemType: ContentElementItemType) {
    let existingConfig: any;
    const modifiedItemType = itemType === 'blog' ? 'selectedBlogs' : itemType;

    let contentLengthMultiplier = 1;
    let fillUpWithNull = true;
    const contentElem = this.searchElement(this.structure, layoutElementId);
    switch (contentType) {
      case LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION:
      case LayoutElementContentType.ARTICLE:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationArticle;
        }
        break;
      case LayoutElementContentType.ARTICLE_SLIDER:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationSportBlock;
        }
        break;
      case LayoutElementContentType.SPORT_BLOCK:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationSportBlock;
        }
        break;
      case LayoutElementContentType.GP_NEWS_BOX:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationGPNewsBox;
        }
        existingConfig.canModifyContentLength = !(contentElem as LayoutElementContentGPNewsBox).turnOffPreviewImageGenerateByContentLength;
        break;
      case LayoutElementContentType.EB_NEWS:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationEBNews;
        }
        break;
      case LayoutElementContentType.BRANDING_BOX_ARTICLE:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationGPNewsBox;
        }
        existingConfig.canModifyContentLength = !(contentElem as LayoutElementContentGPNewsBox).turnOffPreviewImageGenerateByContentLength;
        break;
      case LayoutElementContentType.RECIPE_CATEGORY_SELECT:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationRecipeCategorySelect;
        }
        break;
      case LayoutElementContentType.MAESTRO_BOX:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationMaestroBox;
        }
        break;
      case LayoutElementContentType.PUBLIC_AUTHORS:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationPublicAuthors;
        }
        break;
      case LayoutElementContentType.HIGHLIGHTED_SELECTION:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationHighlightedSelection;
        }
        break;
      case LayoutElementContentType.SELECTION: {
        existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationSelection;
        contentLengthMultiplier = (contentElem as LayoutElementContentSelection).maxContentLength;
        fillUpWithNull = false;
      }
      case LayoutElementContentType.GASTRO_THEMATIC_RECOMMENDER:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationGastroThematicRecommender;
        }
        break;
      case LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION_SWIPER:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationGastroOccasionExperienceSwiper;
        }
        break;
      case LayoutElementContentType.DAILY_MENU:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationWithId;
        }
        break;
      case LayoutElementContentType.MEDIA_PANEL:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationArticle;
        }
        break;
      case LayoutElementContentType.RECIPE:
      case LayoutElementContentType.RECIPE_SWIPER:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationRecipe;
        }
        break;
      case LayoutElementContentType.GUARANTEE_BOX:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationGuaranteeBox;
        }
        break;
      case LayoutElementContentType.DOSSIER_LIST:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationDossierList;
        }
        break;
      case LayoutElementContentType.TURPI_BOX: {
        existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationTurpiBox;
        break;
      }
      case LayoutElementContentType.DETECTIONS:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationDetections;
        }
        break;
      case LayoutElementContentType.SHORT_NEWS:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationShortNews;
        }
        break;
      case LayoutElementContentType.TEXT_BOX:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationTextBox;
        }
        break;
      case LayoutElementContentType.TURPI_CARD:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationTurpiCard;
        }
        break;
      case LayoutElementContentType.FAST_NEWS:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationFastNews;
        }
        break;
      case LayoutElementContentType.MINUTE_TO_MINUTE:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationMinuteToMinutes;
        }
        break;
      case LayoutElementContentType.BLOG:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationBlog;
        }
        break;
      case LayoutElementContentType.PROGRAM:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationProgram;
        }
        break;
      case LayoutElementContentType.OPINION:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationOpinion;
        }
        break;
      case LayoutElementContentType.BAYER_BLOG:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationOpinion;
        }
        break;
      case LayoutElementContentType.WHERE_THE_BALL_WILL_BE:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationOpinion;
        }
        break;
      case LayoutElementContentType.INGREDIENT:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationIngredient;
        }
        break;
      case LayoutElementContentType.NOTE:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationNote;
        }
        break;
      case LayoutElementContentType.VIDEO:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationVideo;
        }
        break;
      case LayoutElementContentType.VOTE:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationVote;
        }
        break;
      case LayoutElementContentType.MORE_ARTICLES:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationMoreArticles;
        }
        break;
      case LayoutElementContentType.DOSSIER_REPEATER:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationDossierRepeater;
        }
        break;
      case LayoutElementContentType.GALLERY:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationGallery;
          const galleryContentElem = this.searchElement(this.structure, layoutElementId) as LayoutElementContentGallery;
          contentLengthMultiplier = galleryContentElem.galleryLength ? galleryContentElem.galleryLength : 1;
        }
        break;
      case LayoutElementContentType.BREAKING:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationBreaking;
        }
        break;
      case LayoutElementContentType.IMAGE: {
        existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationImage;
        const ImageContentElem = this.searchElement(this.structure, layoutElementId) as LayoutElementContentImage;
        const updatedConfig = {
          ...existingConfig,
          cancelMargin: ImageContentElem.cancelMargin ? ImageContentElem.cancelMargin : false,
        };
        const existingConfigIndex = this.getConfigIndex(contentElem);
        this.configuration[existingConfigIndex] = updatedConfig;
        return;
      }
      case LayoutElementContentType.BRANDING_BOX:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationBrandingBox;
        }
        break;
      case LayoutElementContentType.BRANDING_BOX_EX:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationBrandingBoxEx;
        }
        break;
      case LayoutElementContentType.DATA_BANK:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationArticle;
        }
        break;
      case LayoutElementContentType.TABS:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationTabsBlock;
          const tabsContentElem = this.searchElement(this.structure, layoutElementId) as LayoutElementContentTabsBlock;
          // const tabslength = tabsContentElem.tabs?.length;
          const tabs = tabsContentElem?.tabs?.filter((tab) => tab) || [];
          existingConfig = {
            ...existingConfig,
            tabs: tabs?.map((tab, tabIndex) => ({
              ...tab,
              tabIndex,
              selectedArticles: existingConfig?.tabs[tabIndex]?.selectedArticles || new Array(7).fill(null),
              autoFill: existingConfig?.tabs[tabIndex]?.autoFill || {
                filterColumns: [],
                filterPriorities: [],
                filterTags: [],
                filterSponsorships: [],
                filterArticleNetworkSlots: null,
                orderPriority: 'date',
                newest: false,
              },
              doAutoFill: existingConfig?.tabs[tabIndex]?.doAutoFill || true,
            })),
          };
        }
        break;
      case LayoutElementContentType.INGATLANBAZAR_SEARCH:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationIngatlanbazarSearch;
        }
        break;
      case LayoutElementContentType.OLIMPIA_ARTICLES_WITH_PODCAST_CONTENT: {
        existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationOlimpiaArticleWithPodcast;
        const podcastContentElem = this.searchElement(this.structure, layoutElementId) as LayoutElementContentOlimpiaArticlesWithPodcastContent;
        contentLengthMultiplier = podcastContentElem.articleLength ? podcastContentElem.articleLength : 1;

        break;
      }
      case LayoutElementContentType.PODCAST_ARTICLE_LIST: {
        existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationArticle;
        break;
      }
      case LayoutElementContentType.MOST_VIEWED:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationArticle;
        }
        break;
      case LayoutElementContentType.TOP_STORIES:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationArticle;
        }
        break;
      case LayoutElementContentType.SHORT_VIDEOS:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationShortVideos;
        }
        break;
      case LayoutElementContentType.OFFER_BOX:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationOfferBox;
        }
        break;
      case LayoutElementContentType.COUNTDOWN_BOX:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationCountdownBox;
        }
        break;
      case LayoutElementContentType.SERVICES_BOX:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationServicesBox;
        }
        break;
      case LayoutElementContentType.EB_COUNTDOWN_BLOCK_TITLE:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationEbCountdownBlockTitle;
        }
        break;
      case LayoutElementContentType.OLIMPIA_COUNTDOWN_BLOCK_TITLE:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationOlimpiaCountdownBlockTitle;
        }
        break;
      case LayoutElementContentType.OLIMPIA_NEWS:
        {
          existingConfig = this.getConfig(contentElem) as LayoutElementContentConfigurationOlimpiaNews;
        }
        break;
    }

    if (existingConfig && LayoutElementContentType.TABS !== contentType) {
      let updatedElementItems: any[] = existingConfig[modifiedItemType];
      if (updatedElementItems.length > contentElem.contentLength * contentLengthMultiplier) {
        updatedElementItems = updatedElementItems.slice(0, contentElem.contentLength * contentLengthMultiplier);
      } else if (updatedElementItems.length < contentElem.contentLength * contentLengthMultiplier && fillUpWithNull) {
        const diff = contentElem.contentLength * contentLengthMultiplier - updatedElementItems.length;
        for (let i = 0; i < diff; i++) {
          updatedElementItems.push(null);
        }
      }
      const updatedConfig = {
        ...existingConfig,
        [modifiedItemType]: updatedElementItems,
      };
      const existingConfigIndex = this.getConfigIndex(contentElem);
      this.configuration[existingConfigIndex] = updatedConfig;
    }
  }

  getConfig<T extends LayoutElementContentConfiguration>(element: LayoutElementContent): any {
    return this.configuration.find((c) => c?.layoutElementId === element.id) as T;
  }

  getConfigIndex(element: LayoutElementContent): number {
    return this.configuration.findIndex((c) => c?.layoutElementId === element.id);
  }

  handleDrop(type: string, id: string, layoutElement: LayoutElementContent, index: number): Promise<boolean> {
    return new Promise<boolean>((resolve, reject) => {
      let config;
      if (type === 'article') {
        config = this.getConfig(layoutElement) as LayoutElementContentConfigurationArticle;
        if (config.hasOwnProperty('selectedArticles')) {
          config.selectedArticles[index] = {
            id: id,
            overwrite: {
              title: '',
              image: null,
              lead: '',
            },
          };
          this.api.getArticleView(id).subscribe({
            error: () => {
              resolve(false);
            },
            next: (res) => {
              config.selectedArticles[index].original = res.data;
              resolve(true);
            },
          });
        }
      } else if (type === 'opinion') {
        config = this.getConfig(layoutElement) as LayoutElementContentConfigurationOpinion;
        if (config.hasOwnProperty('selectedOpinions')) {
          config.selectedOpinions[index] = {
            id: id,
            overwrite: {
              title: '',
              image: null,
              lead: '',
            },
          };
          this.api.getArticleView(id).subscribe({
            error: () => {
              resolve(false);
            },
            next: (res) => {
              config.selectedOpinions[index].original = res.data;
              resolve(true);
            },
          });
        }
      } else {
        resolve(false);
      }
    });
  }

  removeUnusedConfigs() {
    const unusedConfigs = [];
    this.configuration
      .filter((conf) => !!conf)
      .forEach((conf) => {
        if (conf) {
          const el = this.searchElement(this.structure, conf.layoutElementId);
          if (!el) {
            unusedConfigs.push(conf);
          }
        }
      });

    if (unusedConfigs?.length > 0) {
      const unusedLayoutElementIds = unusedConfigs.map((u) => u.layoutElementId);
      this.configuration = this.configuration.filter((c) => !c || (c && !unusedLayoutElementIds.includes(c.layoutElementId)));
    }
  }

  getNodeConfigOrder(elements: LayoutElement[]): LayoutElementContentConfiguration[] {
    const nodeConfigs = [];
    elements.forEach((el) => {
      // recursions on row & column children
      if (el.type === LayoutElementType.ROW || el.type === LayoutElementType.COLUMN) {
        nodeConfigs.push(...this.getNodeConfigOrder((el as LayoutElementRow | LayoutElementColumn).elements));
      } else {
        // if the element needs config but there isn't one, add it to config array
        const contentElem = el as LayoutElementContent;
        if (contentElem.configurable) {
          const config = this.getConfig(contentElem);
          nodeConfigs.push(config);
        }
      }
    });
    return nodeConfigs;
  }

  validateConfiguration(): boolean {
    let valid = true;
    const unusedConfigs = [];
    this.configuration.forEach((conf) => {
      if (conf) {
        const el = this.searchElement(this.structure, conf.layoutElementId);
        if (!el) {
          unusedConfigs.push(conf);
        }
        if (el && el.contentType === LayoutElementContentType.GALLERY) {
          const config = conf as LayoutElementContentConfigurationGallery;
          if (config.selectedGalleries.filter((g) => !g || g == null).length > 0 && !this.domainService.isNemzetiSport()) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres galériát!');
          }
        } else if (el && el.contentType === LayoutElementContentType.VOTE) {
          if (!conf.selectedVote) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres szavazást!');
          }
        } else if (el && el.contentType === LayoutElementContentType.LINK_LIST) {
          const config = conf as LayoutElementContentConfigurationLinkList;
          if (!config || config.linkList.length === 0) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres link listát!');
          }
        } else if (el && el.contentType === LayoutElementContentType.HTML_EMBED) {
          const config = conf as LayoutElementContentConfigurationHtmlEmbed;
          if (!config || !config.htmlContent || !(!!config.htmlContent.desktop || !!config.htmlContent.mobile)) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres beágyazott tartalom boxot!');
          }
        } else if (el && el.contentType === LayoutElementContentType.DOSSIER) {
          const config = conf as LayoutElementContentConfigurationDossier;
          if (!config || config.selectedDossiers.length === 0) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres dosszié elemet!');
          }
        } else if (el && el.contentType === LayoutElementContentType.CATEGORY_STEPPER) {
          const config = conf as LayoutElementContentConfigurationCategoryStepper;
          if (!config || config.selectedColumns.length === 0) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres kategória elemet!');
          }
        } else if (el && el.contentType === LayoutElementContentType.QUIZ) {
          const config = conf as LayoutElementContentConfigurationQuiz;
          if (!config || !config.selectedQuiz || !config.selectedQuiz === null) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres kvíz elemet!');
          }
        } else if (el && el.contentType === LayoutElementContentType.SPONSORED_QUIZ) {
          const config = conf as LayoutElementContentConfigurationQuiz;
          if (!config || !config.selectedQuiz || !config.selectedQuiz === null) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres szponzorált kvíz elemet!');
          }
        } else if (el && el.contentType === LayoutElementContentType.IMAGE) {
          const config = conf as LayoutElementContentConfigurationImage;
          if (!config || !config.selectedImage || !config.selectedImage === null) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres kép elemet!');
          }
        } else if (el && el.contentType === LayoutElementContentType.TRENDING_TAGS_BLOCK) {
          const config = conf as LayoutElementContentConfigurationTrendingTags;
          if (
            !config ||
            !config.trendingTagsMain ||
            !config.trendingTagsSecondary ||
            (config.trendingTagsMain?.length < 1 && config.trendingTagsSecondary?.length < 1)
          ) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres trending címkék elemet!');
          }
        } else if (el && el.contentType === LayoutElementContentType.TAG_BLOCK) {
          const config = conf as LayoutElementContentConfigurationTagStrip;
          if (config?.tags?.length < 1) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres címke szalag elemet!');
          }
        } else if (el && el.contentType === LayoutElementContentType.WEEKLY_NEWSPAPER_BOX) {
          if (conf?.selectedJournal === null) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres hetilap elemet!');
          }
        } else if (el && el.contentType === LayoutElementContentType.TEAMS) {
          if (['magyarNemzet', 'origo', 'mandiner'].includes(this.domainKey) || el?.['hideConfiguration']) {
            return true;
          }
          const config = conf as LayoutElementContentConfigurationTeams;
          if (!config?.selectedChampionship) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres csapatok elemet!');
          }
        } else if (el && el.contentType === LayoutElementContentType.DOSSIER_REPEATER) {
          if (conf?.selectedDossiers.includes(null)) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres dosszié elemet!');
          }
        } else if (el && el.contentType === LayoutElementContentType.DOSSIER_LIST) {
          const config = conf as LayoutElementContentConfigurationDossierList;
          if (config?.selectedDossiers.filter((d) => !d || false).length > 0) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres dosszié lista elemet!');
          }
        } else if (el && el.contentType === LayoutElementContentType.DETECTIONS) {
          const config = conf as LayoutElementContentConfigurationDetections;
          const element = el as DetectionType;
          if (!element.automataFill && config?.selectedDetections.includes(null)) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres észlelés elemet!');
          }
          if (element.automataFill && config?.selectedDetections.includes(null) && !config?.selectedDetections.every((e) => e === null)) {
            valid = false;
            this.sharedService.showNotification('error', 'A szöveges észlelésnél vagy minden észlelést ki kell választani, vagy egyet sem!');
          }
        } else if (el && el.contentType === LayoutElementContentType.NEWS_FEED) {
          const config = conf as LayoutElementContentConfigurationDossier;
          const selectedDossiers = config?.selectedDossiers?.[0];
          if (!selectedDossiers?.['id']) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres hírfolyam elemet!');
          }
        } else if (el && el.contentType === LayoutElementContentType.SOROZATVETO) {
          const config = conf as LayoutElementContentConfigurationSorozatveto;
          if (!config?.selectedArticles[0]?.id) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres így írnak ők elemet!');
          }
        } else if (el && el.contentType === LayoutElementContentType.AUTHOR) {
          const config = conf as LayoutElementContentConfigurationAuthor;
          if (config?.selectedAuthors?.filter((author) => !author?.title)?.length > 0) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres szerző elemet!');
          }
        } else if (el && el.contentType === LayoutElementContentType.DID_YOU_KNOW) {
          const config = conf as LayoutElementContentConfigurationselectedDidYouKnowBox;
          if (!config?.selectedDidYouKnowBox[0]?.id) {
            valid = false;
            this.sharedService.showNotification('error', 'Az elrendezés tartalmaz üres változó tartalmú branding box elemet!');
          }
        } else if (el && el.contentType === LayoutElementContentType.RECIPE_CATEGORY_SELECT) {
          const config = conf as LayoutElementContentConfigurationRecipeCategorySelect;
          config.recipeCategories.every((category) => {
            if (!category?.id) {
              valid = false;
              this.sharedService.showNotification('error', 'Recept kategória választó hiba: Nem választott ki minden recept kategóriát!');
              return false;
            }

            if (!category?.recipes || category?.recipes?.length === 0 || category?.recipes?.some((r) => !r)) {
              valid = false;
              this.sharedService.showNotification(
                'error',
                `Recept kategória választó hiba: A receptlista nem minden recept kategóriánál helyes (kategória: ${category?.title})!`
              );
            }
            return valid;
          });
        } else if (el && el.contentType === LayoutElementContentType.SELECTION) {
          if (!conf.selection?.id) {
            valid = false;
            this.sharedService.showNotification('error', 'Válogatás doboz hiba: Nem adott meg válogatást!');
            return;
          }
          if (!conf.selectedSelectionItems?.length) {
            valid = false;
            this.sharedService.showNotification('error', 'Válogatás doboz hiba: Legalább egy recept/cikk választása kötelező!');
          }
        } else if (el && el.contentType === LayoutElementContentType.MAESTRO_BOX) {
          const config = conf as LayoutElementContentConfigurationMaestroBox;
          if (config.authors?.length < 1) {
            valid = false;
            this.sharedService.showNotification('error', 'Maestro doboz hiba: Legalább egy maesto-t kell választani!');
          }
          config.authors.every((author) => {
            if (!author?.id) {
              valid = false;
              this.sharedService.showNotification('error', 'Maestro doboz hiba: Nem választott ki minden maestro-t!');
              return false;
            }

            if (!author.selectedArticlesAndRecipes || author.selectedArticlesAndRecipes.length === 0 || author.selectedArticlesAndRecipes.some((r) => !r?.id)) {
              valid = false;
              this.sharedService.showNotification(
                'error',
                `Maestro doboz hiba: A cikklista/receptlista nem minden maestro-nál helyes (maestro: ${author.name})!`
              );
            }
            return valid;
          });
        } else if (el && el.contentType === LayoutElementContentType.HIGHLIGHTED_SELECTION) {
          const config = conf as LayoutElementContentConfigurationHighlightedSelection;
          if (!config.selection?.id) {
            valid = false;
            this.sharedService.showNotification('error', 'Válogatás hiba: Nem adott meg válogatást!');
          }
        } else if (el && el.contentType === LayoutElementContentType.INGREDIENT) {
          const config = conf as LayoutElementContentConfigurationIngredient;
          config.selectedIngredients.every((ingredient) => {
            if (!ingredient?.id) {
              valid = false;
              this.sharedService.showNotification('error', 'Hozzávalók hiba! Nem minden hozzávalót választott ki.');
              return false;
            }

            return valid;
          });
        } else if (el && el.contentType === LayoutElementContentType.RELATED_ARTICLES) {
          if ((el as LayoutElementContentRelatedArticles)?.isOrigoSport) {
            return true;
          }

          conf.selectedArticles.every((article) => {
            if (!article?.id) {
              valid = false;
              this.sharedService.showNotification('error', 'Kapcsolódó cikkek hiba! Nem minden kapcsolódó cikket választott ki.');
              return false;
            }

            return valid;
          });
        } else if (el && el.contentType === LayoutElementContentType.ARTICLE_BLOCK) {
          const config = conf as LayoutElementContentConfigurationArticleBlock;
          if (el?.['hasAutoFill']) {
            return true;
          }
          config.selectedArticles.every((article) => {
            if (!article?.id) {
              valid = false;
              this.sharedService.showNotification('error', 'Cikk blokk hiba! Nem választott ki minden cikket.');
              return false;
            }

            return valid;
          });
        } else if (el && el.contentType === LayoutElementContentType.GUARANTEE_BOX) {
          const config = conf as LayoutElementContentConfigurationGuaranteeBox;
          if (!config?.selectedArticlesAndRecipes?.length || config?.selectedArticlesAndRecipes?.some((r) => !r?.id)) {
            valid = false;
            this.sharedService.showNotification('error', 'Garancia doboz hiba! Nem választott ki minden cikket/receptet.');
            return false;
          }
        } else if (el && el.contentType === LayoutElementContentType.TOP_TEN_TAGS) {
          const config = conf as LayoutElementContentConfigurationTopTenTags;
          if (!config?.tags?.length || config?.tags?.some((r) => !r?.id)) {
            valid = false;
            this.sharedService.showNotification('error', 'TOP 10 címke doboz hiba! Nem választott ki minden címkét.');
            return false;
          }
        } else if (el && el.contentType === LayoutElementContentType.SUB_COLUMNS) {
          if (!conf?.selectedColumns?.length || conf?.selectedColumns?.some((column) => !column?.id)) {
            valid = false;
            this.sharedService.showNotification('error', 'Alrovatok doboz hiba! Nem választott ki minden alrovatot.');
            return false;
          }
        } else if (el && el.contentType === LayoutElementContentType.SECRET_DAYS_CALENDAR) {
          const config = conf as LayoutElementContentConfigurationSecretDaysCalendar;
          if (!config?.selectedCalendar) {
            valid = false;
            this.sharedService.showNotification('error', 'Kalendárium hiba! Nem választott kalendáriumot.');
            return false;
          }
        }
      }
    });
    // remove unused configs
    this.configuration = this.configuration.filter((c) => !unusedConfigs.find((u) => u.layoutElementId === c.layoutElementId));
    return valid;
  }

  searchElement(elements: LayoutElement[], layoutElementId: number | string): LayoutElementContent {
    let elem;
    // TODO: review this search (!elem condition), forEach might not be the best solution for recursion (unbreakable)
    elements.forEach((el) => {
      // recursions on row & column children
      if ((el.type === LayoutElementType.ROW || el.type === LayoutElementType.COLUMN) && !elem) {
        elem = this.searchElement((el as LayoutElementRow | LayoutElementColumn).elements, layoutElementId);
      } else {
        if (el.id === layoutElementId) {
          elem = el;
        }
      }
    });
    return elem;
  }

  @HostListener('dragover', ['$event']) enableComponentToBeDropTarget(event: DragEvent) {
    event.preventDefault();
  }
}
