import { CdkDragDrop, CdkDragStart, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { Component, EventEmitter, HostListener, Input, OnChanges, OnInit, Output, signal, SimpleChanges, ViewContainerRef } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { PortalConfigService } from '@shared/services/portal-config.service';
import { ExchangeBoxDataType } from '@trendency/kesma-ui';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ModalOptions, NzModalService } from 'ng-zorro-antd/modal';
import { filter, takeUntil } from 'rxjs/operators';
import { DomainKey } from 'src/app/core/modules/admin/admin.definitions';
import { DomainService } from 'src/app/core/modules/admin/services/domain.service';
import {
  LayoutContentTabConfig,
  LayoutElementContentAd,
  LayoutElementContentArticle,
  LayoutElementContentArticleBlock,
  LayoutElementContentArticleSlider,
  LayoutElementContentArticlesWithPodcastContent,
  LayoutElementContentArticlesWithVideoContent,
  LayoutElementContentAstrologyBlock,
  LayoutElementContentAstronetHoroszkop,
  LayoutElementContentAstronetJoslas,
  LayoutElementContentBayerBlog,
  LayoutElementContentBestRecommender,
  LayoutElementContentBlockSeparator,
  LayoutElementContentBlog,
  LayoutElementContentBrandingBox,
  LayoutElementContentBrandingBoxArticle,
  LayoutElementContentBrandingBoxEx,
  LayoutElementContentBreakingBlock,
  LayoutElementContentBroadcastRecommender,
  LayoutElementContentCategoryStepper,
  LayoutElementContentChampionshipTable,
  LayoutElementContentColumnBlock,
  LayoutElementContentConference,
  LayoutElementContentConfigurableSponsoredBox,
  LayoutElementContentCountdownBox,
  LayoutElementContentCultureNation,
  LayoutElementContentDailyMenu,
  LayoutElementContentDataBank,
  LayoutElementContentDetections,
  LayoutElementContentDidYouKnow,
  LayoutElementContentDossier,
  LayoutElementContentDossierList,
  LayoutElementContentDossierRepeater,
  LayoutElementContentDrawnMapList,
  LayoutElementContentEbCountdownBlockTitle,
  LayoutElementContentEBNews,
  LayoutElementContentElectionsBox,
  LayoutElementContentEventCalendar,
  LayoutElementContentExperienceGift,
  LayoutElementContentExperienceOccasion,
  LayoutElementContentFastNews,
  LayoutElementContentFinalCountdown,
  LayoutElementContentFreshNews,
  LayoutElementContentGallery,
  LayoutElementContentGastroExperienceOccasionSwiper,
  LayoutElementContentGastroExperienceRecommendation,
  LayoutElementContentGastroOccasionRecommender,
  LayoutElementContentGastroThematicRecommender,
  LayoutElementContentGPNewsBox,
  LayoutElementContentGuaranteeBox,
  LayoutElementContentHelloBudapest,
  LayoutElementContentHero,
  LayoutElementContentHighlightedSelection,
  LayoutElementContentHtmlEmbed,
  LayoutElementContentImage,
  LayoutElementContentImageMapList,
  LayoutElementContentIngatlanbazar,
  LayoutElementContentIngatlanbazarConfigurable,
  LayoutElementContentIngatlanbazarSearch,
  LayoutElementContentIngredient,
  LayoutElementContentJobListings,
  LayoutElementContentKoponyeg,
  LayoutElementContentLatestNews,
  LayoutElementContentLinkList,
  LayoutElementContentLiveBar,
  LayoutElementContentMaestroBox,
  LayoutElementContentManualArticle,
  LayoutElementContentManualOpinion,
  LayoutElementContentMapRecommendations,
  LayoutElementContentMediaPanel,
  LayoutElementContentMedicalMeteorology,
  LayoutElementContentMinuteToMinutes,
  LayoutElementContentMoreArticles,
  LayoutElementContentMostViewed,
  LayoutElementContentMultiVote,
  LayoutElementContentNewsletterBlock,
  LayoutElementContentNewsletterBlockGong,
  LayoutElementContentNewspaper,
  LayoutElementContentOfferBox,
  LayoutElementContentOlimpiaArticlesWithPodcastContent,
  LayoutElementContentOlimpiaCountdownBlockTitle,
  LayoutElementContentOlimpiaHungarianCompetitions,
  LayoutElementContentOlimpiaHungarianTeam,
  LayoutElementContentOlimpiaLargeNavigator,
  LayoutElementContentOlimpiaNews,
  LayoutElementContentOlimpiaResultsBlock,
  LayoutElementContentOpinion,
  LayoutElementContentOpinionList,
  LayoutElementContentOpinionNewsletterBox,
  LayoutElementContentPdfBox,
  LayoutElementContentPodcastAppRecommender,
  LayoutElementContentPodcastList,
  LayoutElementContentPrBlock,
  LayoutElementContentProgram,
  LayoutElementContentPublicAuthors,
  LayoutElementContentQuiz,
  LayoutElementContentRecipe,
  LayoutElementContentRecipeCategorySelect,
  LayoutElementContentRecipeSwiper,
  LayoutElementContentRelatedArticles,
  LayoutElementContentRssBox,
  LayoutElementContentSecretDaysCalendar,
  LayoutElementContentSelection,
  LayoutElementContentServicesBox,
  LayoutElementContentShortVideos,
  LayoutElementContentSocialMedia,
  LayoutElementContentSongTopList,
  LayoutElementContentSorozatveto,
  LayoutElementContentSponsoredArticleBox,
  LayoutElementContentSponsoredQuiz,
  LayoutElementContentSponsoredVote,
  LayoutElementContentSportBlock,
  LayoutElementContentSpotlight,
  LayoutElementContentStockChart,
  LayoutElementContentSzakikereso,
  LayoutElementContentTabsBlock,
  LayoutElementContentTagBlock,
  LayoutElementContentTelekomVivicitta,
  LayoutElementContentTenyekBox,
  LayoutElementContentTextBox,
  LayoutElementContentTopicSuggestion,
  LayoutElementContentTopStories,
  LayoutElementContentTopTenTags,
  LayoutElementContentTrendingTagsBlock,
  LayoutElementContentTurpiBox,
  LayoutElementContentTurpiCard,
  LayoutElementContentTwelveDaysForecast,
  LayoutElementContentUpcomingMatches,
  LayoutElementContentVideo,
  LayoutElementContentVideoBlock,
  LayoutElementContentVisegradPost,
  LayoutElementContentVote,
  LayoutElementContentWaze,
  LayoutElementContentWeeklyNewspaper,
  LayoutElementContentWhereTheBallWillBe,
  LayoutElementContentWysiwyg,
} from 'src/app/modules/layout/layout-content.definitions';
import {
  BasicLayoutElementPreview,
  ContentElementItemType,
  DetectionType,
  LayoutEditorModalResult,
  LayoutEditorModalResultItems,
  LayoutEditorModalResultMulti,
  LayoutElement,
  LayoutElementArticlePreview,
  LayoutElementColumn,
  LayoutElementContent,
  LayoutElementContentTripBox,
  LayoutElementContentType,
  LayoutElementGalleryPreview,
  LayoutElementOpinionListPreview,
  LayoutElementOpinionPreview,
  LayoutElementRow,
  LayoutElementSelection,
  LayoutElementType,
  LayoutPageType,
  LayoutStructureChangedData,
} from 'src/app/modules/layout/layout-core.definitions';
import { SharedService } from 'src/app/shared/services/shared.service';
import { LayoutEditorSettings, LayoutElementContentTypeCondition, LayoutElementContentTypeConfig } from '../../layout-editor-configuration.definitions';
import { ExplicitArticleListManagerService } from '../../services/explicit-article-list-manager.service';
import { LayoutEditorService } from '../../services/layout-editor.service';
import { ModalColumnComponent } from './components/modals/modal-column/modal-column.component';
import { ModalContentAdComponent } from './components/modals/modal-content-ad/modal-content-ad.component';
import { ModalContentAgroKepListComponent } from './components/modals/modal-content-agro-kep-list/modal-content-agro-kep-list.component';
import { ModalContentAgroKepComponent } from './components/modals/modal-content-agro-kep/modal-content-agro-kep.component';
import { ModalContentArticleBlockComponent } from './components/modals/modal-content-article-block/modal-content-article-block.component';
import { ModalContentArticleSliderComponent } from './components/modals/modal-content-article-slider/modal-content-article-slider.component';
import { ModalContentArticleComponent } from './components/modals/modal-content-article/modal-content-article.component';
import { ModalContentArticlesWithPodcastContentComponent } from './components/modals/modal-content-articles-with-podcast-content/modal-content-articles-with-podcast-content.component';
import { ModalContentArticlesWithVideoComponent } from './components/modals/modal-content-articles-with-video-content/modal-content-articles-with-video.component';
import { ModalContentAstrologyComponent } from './components/modals/modal-content-astrology/modal-content-astrology.component';
import { ModalContentAstronetHoroszkopComponent } from './components/modals/modal-content-astronet-horoszkop/modal-content-astronet-horoszkop.component';
import { ModalContentAstronetJoslasComponent } from './components/modals/modal-content-astronet-joslas/modal-content-astronet-joslas.component';
import { ModalContentAuthorComponent } from './components/modals/modal-content-author/modal-content-author.component';
import { ModalContentBaseComponent } from './components/modals/modal-content-base/modal-content-base.component';
import { ModalContentBestRecommenderComponent } from './components/modals/modal-content-best-recommender/modal-content-best-recommender.component';
import { ModalContentBlogComponent } from './components/modals/modal-content-blog/modal-content-blog.component';
import { ModalContentBrandingBoxArticleComponent } from './components/modals/modal-content-branding-box-article/modal-content-branding-box-article.component';
import { ModalContentBrandingBoxExternalComponent } from './components/modals/modal-content-branding-box-external/modal-content-branding-box-external.component';
import { Brand, ModalContentBrandingBoxComponent } from './components/modals/modal-content-branding-box/modal-content-branding-box.component';
import { ModalContentBreakingComponent } from './components/modals/modal-content-breaking/modal-content-breaking.component';
import { ModalContentBroadcastRecommenderComponent } from './components/modals/modal-content-broadcast-recommender/modal-content-broadcast-recommender.component';
import { ModalContentCategoryStepperComponent } from './components/modals/modal-content-category-stepper/modal-content-category-stepper.component';
import { ModalContentChampionshipTableComponent } from './components/modals/modal-content-championship-table/modal-content-championship-table.component';
import { ModalContentConferenceComponent } from './components/modals/modal-content-conference/modal-content-conference.component';
import { ModalContentCultureNationComponent } from './components/modals/modal-content-culture-nation/modal-content-culture-nation.component';
import { ModalContentDailyMenuComponent } from './components/modals/modal-content-daily-menu/modal-content-daily-menu.component';
import { ModalContentDataBankComponent } from './components/modals/modal-content-data-bank/modal-content-data-bank.component';
import { ModalContentDetectionsComponent } from './components/modals/modal-content-detections/modal-content-detections.component';
import { ModalContentDidYouKnowComponent } from './components/modals/modal-content-did-you-know/modal-content-did-you-know.component';
import { ModalContentDossierListComponent } from './components/modals/modal-content-dossier-list/modal-content-dossier-list.component';
import { ModalContentDossierRepeaterComponent } from './components/modals/modal-content-dossier-repeater/modal-content-dossier-repeater.component';
import { ModalContentDossierComponent } from './components/modals/modal-content-dossier/modal-content-dossier.component';
import { ModalContentDrawnMapListComponent } from './components/modals/modal-content-drawn-map-list/modal-content-drawn-map-list.component';
import { ModalContentFastNewsComponent } from './components/modals/modal-content-fast-news/modal-content-fast-news.component';
import { ModalContentFinalCountdownComponent } from './components/modals/modal-content-final-countdown/modal-content-final-countdown.component';
import { ModalContentFreshNewsComponent } from './components/modals/modal-content-fresh-news/modal-content-fresh-news.component';
import { ModalContentGalleryComponent } from './components/modals/modal-content-gallery/modal-content-gallery.component';
import { ModalContentGongNewsletterComponent } from './components/modals/modal-content-gong-newsletter/modal-content-gong-newsletter.component';
import { ModalContentGpNewsBoxComponent } from './components/modals/modal-content-gp-news-box/modal-content-gp-news-box.component';
import { ModalContentGuaranteeBoxComponent } from './components/modals/modal-content-guarantee-box/modal-content-guarantee-box.component';
import { ModalContentHeaderComponent } from './components/modals/modal-content-header/modal-content.header.component';
import { ModalContentHelloBudapestComponent } from './components/modals/modal-content-hello-budapest/modal-content-hello-budapest.component';
import { ModalContentHeroComponent } from './components/modals/modal-content-hero/modal-content-hero.component';
import { ModalContentHighlightedSelectionComponent } from './components/modals/modal-content-highlighted-selection/modal-content-highlighted-selection.component';
import { ModalContentHtmlEmbedComponent } from './components/modals/modal-content-html-embed/modal-content-html-embed.component';
import { ModalContentImageMapListComponent } from './components/modals/modal-content-image-map-list/modal-content-image-map-list.component';
import { ModalContentImageComponent } from './components/modals/modal-content-image/modal-content-image.component';
import { ModalContentIngatlanbazarConfigurableComponent } from './components/modals/modal-content-ingatlanbazar-configurable/modal-content-ingatlanbazar-configurable.component';
import { ModalContentIngatlanbazarSearchComponent } from './components/modals/modal-content-ingatlanbazar-search/modal-content-ingatlanbazar-search.component';
import { ModalContentIngatlanbazarComponent } from './components/modals/modal-content-ingatlanbazar/modal-content-ingatlanbazar.component';
import { ModalContentIngredientComponent } from './components/modals/modal-content-ingredient/modal-content-ingredient.component';
import { ModalContentKoponyegComponent } from './components/modals/modal-content-koponyeg/modal-content-koponyeg.component';
import { ModalContentKulturnemzetComponent } from './components/modals/modal-content-kulturnemzet/modal-content-kulturnemzet.component';
import { ModalContentLatestNewsComponent } from './components/modals/modal-content-latest-news/modal-content-latest-news.component';
import { ModalContentLinkListComponent } from './components/modals/modal-content-link-list/modal-content-link-list.component';
import { ModalContentLiveBarComponent } from './components/modals/modal-content-live-bar/modal-content-live-bar.component';
import { ModalContentMaestroBoxComponent } from './components/modals/modal-content-maestro-box/modal-content-maestro-box.component';
import { ModalContentManualArticleComponent } from './components/modals/modal-content-manual-article/modal-content-manual-article.component';
import { ModalContentManualOpinionComponent } from './components/modals/modal-content-manual-opinion/modal-content-manual-opinion.component';
import { ModelContentMapRecommendationsComponent } from './components/modals/modal-content-map-recommendations/model-content-map-recommendations.component';
import { ModalContentMediaPanelComponent } from './components/modals/modal-content-media-panel/modal-content-media-panel.component';
import { ModalContentMedicalMeteorologyComponent } from './components/modals/modal-content-medical-meteorolgy/modal-content-medical-meteorology.component';
import { ModalContentMinuteToMinuteComponent } from './components/modals/modal-content-minute-to-minute/modal-content-minute-to-minute.component';
import { ModalContentMoreArticlesComponent } from './components/modals/modal-content-more-articles/modal-content-more-articles.component';
import { ModalContentMostViewedComponent } from './components/modals/modal-content-most-viewed/modal-content-most-viewed.component';
import { ModalContentNewsFeedComponent } from './components/modals/modal-content-news-feed/modal-content-news-feed.component';
import { ModalContentNewsletterComponent } from './components/modals/modal-content-newsletter/modal-content-newsletter.component';
import { ModalContentNewspaperComponent } from './components/modals/modal-content-newspaper/modal-content-newspaper.component';
import { ModalContentNoteComponent } from './components/modals/modal-content-note/modal-content-note.component';
import { ModalContentOfferBoxComponent } from './components/modals/modal-content-offer-box/modal-content-offer-box.component';
import { ModalContentOpinionListComponent } from './components/modals/modal-content-opinion-list/modal-content-opinion-list.component';
import { ModalContentOpinionNewsletterComponent } from './components/modals/modal-content-opinion-newsletter/modal-content-opinion-newsletter.component';
import { ModalContentOpinionComponent } from './components/modals/modal-content-opinion/modal-content-opinion.component';
import { ModalContentPdfBoxComponent } from './components/modals/modal-content-pdf-box/modal-content-pdf-box.component';
import { ModalContentPodcastArticleListComponent } from './components/modals/modal-content-podcast-article-list/modal-content-podcast-article-list.component';
import { ModalContentPodcastListComponent } from './components/modals/modal-content-podcast-list/modal-content-podcast-list.component';
import { ModalContentProgramComponent } from './components/modals/modal-content-program/modal-content-program.component';
import { ModalContentQuizComponent } from './components/modals/modal-content-quiz/modal-content-quiz.component';
import { ModalContentRecipeCardComponent } from './components/modals/modal-content-recipe-card/modal-content-recipe-card.component';
import { ModalContentRecipeCategoriesSelectComponent } from './components/modals/modal-content-recipe-categories-select/modal-content-recipe-categories-select.component';
import { ModalContentRecipeSwiperComponent } from './components/modals/modal-content-recipe-swiper/modal-content-recipe-swiper.component';
import { ModalContentRelatedArticlesComponent } from './components/modals/modal-content-related-articles/modal-content-related-articles.component';
import { ModalContentRssBoxComponent } from './components/modals/modal-content-rss-box/modal-content-rss-box.component';
import { ModalContentSelectionComponent } from './components/modals/modal-content-selection/modal-content-selection.component';
import { ModalContentSocialMediaComponent } from './components/modals/modal-content-social-media/modal-content-social-media.component';
import { ModalContentSongTopListComponent } from './components/modals/modal-content-song-top-list/modal-content-song-top-list.component';
import { ModalContentSorozatvetoComponent } from './components/modals/modal-content-sorozatveto/modal-content-sorozatveto.component';
import { ModalContentSponsoredArticleBoxComponent } from './components/modals/modal-content-sponsored-article-box/modal-content-sponsored-article-box.component';
import { ModalContentSportBlockComponent } from './components/modals/modal-content-sport-block/modal-content-sport-block.component';
import { ModalContentSpotlightComponent } from './components/modals/modal-content-spotlight/modal-content-spotlight.component';
import { ModalContentStocksChartComponent } from './components/modals/modal-content-stocks-chart/modal-content-stocks-chart.component';
import { ModalContentSzakikeresoComponent } from './components/modals/modal-content-szakikereso/modal-content-szakikereso.component';
import { ModalContentTabsComponent } from './components/modals/modal-content-tabs/modal-content-tabs.component';
import { ModalContentTagBlockComponent } from './components/modals/modal-content-tag-block/modal-content-tag-block.component';
import { ModalContentTelekomVivicittaComponent } from './components/modals/modal-content-telekom-vivicitta/modal-content-telekom-vivicitta.component';
import { ModalContentTenyekBoxComponent } from './components/modals/modal-content-tenyek-box/modal-content-tenyek-box.component';
import { ModalContentTextBoxComponent } from './components/modals/modal-content-text-box/modal-content-text-box.component';
import { ModalContentTopStoriesComponent } from './components/modals/modal-content-top-stories/modal-content-top-stories.component';
import { ModalContentTrendingTagsBlockComponent } from './components/modals/modal-content-trending-tags-block/modal-content-trending-tags-block.component';
import { ModalContentTripBoxComponent } from './components/modals/modal-content-trip-box/modal-content-trip-box.component';
import { ModalContentTurpiBoxComponent } from './components/modals/modal-content-turpi-box/modal-content-turpi-box.component';
import { ModalContentTurpiCardComponent } from './components/modals/modal-content-turpi-card/modal-content-turpi-card.component';
import { ModalContentTwelveDaysForecastComponent } from './components/modals/modal-content-twelve-days-forecast/modal-content-twelve-days-forecast.component';
import { ModalContentUpcomingMatchesComponent } from './components/modals/modal-content-upcoming-matches/modal-content-upcoming-matches.component';
import { ModalContentVideoBlockComponent } from './components/modals/modal-content-video-block/modal-content-video-block.component';
import { ModalContentVideoComponent } from './components/modals/modal-content-video/modal-content-video.component';
import { ModalContentVisegradPostComponent } from './components/modals/modal-content-visegrad-post/modal-content-visegrad-post.component';
import { ModalContentVoteComponent } from './components/modals/modal-content-vote/modal-content-vote.component';
import { ModalContentWazeComponent } from './components/modals/modal-content-waze/modal-content-waze.component';
import { ModalContentWeeklyNewspaperComponent } from './components/modals/modal-content-weekly-newspaper/modal-content-weekly-newspaper.component';
import { ModalContentWysiwygComponent } from './components/modals/modal-content-wysiwyg/modal-content-wysiwyg.component';
import { ModalRowComponent } from './components/modals/modal-row/modal-row.component';
import { DragAndDropManagerService } from './drag-and-drop/drag-and-drop-manager.service';

import { ActivatedRoute } from '@angular/router';
import { ModalContentExperienceGiftComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-experience-gift/modal-content-experience-gift.component';
import { ModalContentGastroExperienceOccasionSwiperComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-gastro-experience-occasion-swiper/modal-content-gastro-experience-occasion-swiper.component';
import { ModalContentGastroExperienceOccasionComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-gastro-experience-occasion/modal-content-gastro-experience-occasion.component';
import { ModalContentGastroExperienceRecommendationComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-gastro-experience-recommendation/modal-content-gastro-experience-recommendation.component';
import { ModalContentGastroOccasionRecommenderComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-gastro-occasion-recommender/modal-content-gastro-occasion-recommender.component';
import { ModalContentPodcastAppRecommenderComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-podcast-app-recommender/modal-content-podcast-app-recommender.component';
import { ModalContentPublicAuthorsComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-public-authors/modal-content-public-authors.component';
import { ModalContentServicesBoxComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-services-box/modal-content-services-box.component';
import { ModalContentThematicRecommenderComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-thematic-recommender/modal-content-thematic-recommender.component';
import { Subject } from 'rxjs';
import { ModalContentCountdownBoxComponent } from './components/modals/modal-content-countdown-box/modal-content-countdown-box.component';
import { ModalContentEbCountdownBlockTitleComponent } from './components/modals/modal-content-eb-countdown-block-title/modal-content-eb-countdown-block-title.component';
import { ModalContentEbNewsBoxComponent } from './components/modals/modal-content-eb-news-box/modal-content-eb-news-box.component';
import { ModalContentEbSingleEliminationComponent } from './components/modals/modal-content-eb-single-elimination/modal-content-eb-single-elimination.component';
import { ModalContentElectionsBoxComponent } from './components/modals/modal-content-elections-box/modal-content-elections-box.component';
import { ModalContentGalleryArticleListComponent } from './components/modals/modal-content-gallery-article-list/modal-content-gallery-article-list.component';
import { ModalContentMultiVoteComponent } from './components/modals/modal-content-multi-vote/modal-content-multi-vote.component';
import { ModalContentOlimpiaArticlesWithPodcastContentComponent } from './components/modals/modal-content-olimpia-articles-with-podcast-content/modal-content-olimpia-articles-with-podcast-content.component';
import { ModalContentOlimpiaCountdownBlockTitleComponent } from './components/modals/modal-content-olimpia-countdown-block-title/modal-content-olimpia-countdown-block-title.component';
import { ModalContentOlimpiaHungarianCompetitionsComponent } from './components/modals/modal-content-olimpia-hungarian-competitions/modal-content-olimpia-hungarian-competitions.component';
import { ModalContentOlimpiaHungarianTeamComponent } from './components/modals/modal-content-olimpia-hungarian-team/modal-content-olimpia-hungarian-team.component';
import { ModalContentOlimpiaLargeNavigatorComponent } from './components/modals/modal-content-olimpia-large-navigator/modal-content-olimpia-large-navigator.component';
import { ModalContentOlimpiaNewsBoxComponent } from './components/modals/modal-content-olimpia-news-box/modal-content-olimpia-news-box.component';
import { ModalContentOlimpiaResultsBlockComponent } from './components/modals/modal-content-olimpia-results-block/modal-content-olimpia-results-block.component';
import { ModalContentSponsoredQuizComponent } from './components/modals/modal-content-sponsored-quiz/modal-content-sponsored-quiz.component';
import { ModalContentTeamsComponent } from './components/modals/modal-content-teams/modal-content-teams.component';
import { koponyegDetectionCardSWithImgMock } from './components/portal-components';
import { ModalContentColumnBlockComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-column-block/modal-content-column-block.component';
import { ModalContentJobListingsComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-job-listings/modal-content-job-listings.component';
import { ModalContentTopTenTagsComponent } from './components/modals/modal-content-top-ten-tags/modal-content-top-ten-tags.component';
import { ModalContentSponsoredVoteComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-sponsored-vote/modal-content-sponsored-vote.component';
import { ModalContentSubColumnsComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-sub-columns/modal-content-sub-columns.component';
import { ModalContentAstronetBrandingBoxComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-astronet-branding-box/modal-content-astronet-branding-box.component';
import { ModalContentAstronetColumnsComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-astronet-columns/modal-content-astronet-columns.component';
import { ModalContentTopicSuggestionComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-topic-suggestion/modal-content-topic-suggestion.component';
import { ModalContentSecretDaysCalendarComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-secret-days-calendar/modal-content-secret-days-calendar.component';
import { ModalContentTopCommentedArticlesComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-top-commented-articles/modal-content-top-commented-articles.component';
import { ModalContentShortVideosComponent } from './components/modals/modal-content-short-videos/modal-content-short-videos.component';

@Component({
  selector: 'app-layout-editor',
  templateUrl: './layout-editor.component.html',
  styleUrls: ['./layout-editor.component.scss'],
  providers: [DragAndDropManagerService],
  standalone: false,
})
export class LayoutEditorComponent implements OnInit, OnChanges {
  readonly elementType = LayoutElementType;
  readonly contentType = LayoutElementContentType;
  readonly destroy$ = new Subject<void>();

  private _currentSettings: LayoutEditorSettings;
  get currentSettings(): LayoutEditorSettings {
    return this._currentSettings;
  }

  /**
   * Storing the provided settings and filtering down enabled content types.
   * Content types can have additional parameters which can describe the conditions that needs to pass in order to
   * enable them. For example you can set portal config keys that needs to be enabled in order to also enable the layout
   * content type too.
   * @param settings
   */
  @Input() set currentSettings(settings: LayoutEditorSettings) {
    const filteredSettings: LayoutEditorSettings = {
      ...settings,
      enabledContentTypes: settings.enabledContentTypes.filter((contentType) => {
        if (contentType === this.contentType.AD) {
          return this.hasAdvertisementEditRole;
        }
        if (typeof contentType === 'string') {
          return true;
        }
        const config = contentType as LayoutElementContentTypeConfig;
        if (config.requiredPortalConfigs) {
          return config.requiredPortalConfigs.every((portalConfig) => this.portalConfigService.isConfigSet(portalConfig));
        }
        const condition = contentType as LayoutElementContentTypeCondition;
        if (condition.isEnabled !== undefined) {
          return condition.isEnabled;
        }
        return true;
      }),
    };
    this._currentSettings = filteredSettings;
  }

  @Input() rootElements: LayoutElementRow[] = [];
  @Input() type: LayoutPageType;

  @Input() set domain(domainKey: DomainKey) {
    this.domainKey = domainKey;
    this.isMegyeiLap = this.domainService.isMegyeiLap(domainKey);
  }

  @Input() isDisabled: boolean;
  @Input() isReadonly: boolean = false;
  @Output() public structureItemChanged = new EventEmitter<LayoutStructureChangedData>();

  /**
   * This is emitted when added a new element to structure, or we deleted an item.
   */
  @Output() structureChanged = new EventEmitter<void>();

  activeElement: LayoutElementRow | LayoutElementColumn;

  private newItemInsertIndex: number = -1;
  public dragDropAddRowButtonPlaceholderVisible = false;

  public toolsOpened = true;
  public domainKey: DomainKey = 'vilaggazdasag';
  public isMegyeiLap: boolean;

  public explicitArtilcleLists$ = this.explicitArticleListManager.lists$;

  hasAdvertisementEditRole: boolean = this.route.snapshot?.data?.item?.meta?.hasAdvertisementEditRole ?? true;

  isLoading = signal<boolean>(true);

  constructor(
    private modal: NzModalService,
    private viewContainerRef: ViewContainerRef,
    private translate: TranslateService,
    private editorService: LayoutEditorService,
    private msg: NzMessageService,
    private domainService: DomainService,
    private sharedService: SharedService,
    private readonly portalConfigService: PortalConfigService,
    private readonly explicitArticleListManager: ExplicitArticleListManagerService,
    private readonly route: ActivatedRoute
  ) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.type && !(changes.rootElements && changes.rootElements.currentValue.length > 0) && changes.type.currentValue !== changes.type.previousValue) {
      this.reset();
    } else if (changes.type && (changes.type.currentValue === LayoutPageType.SIDEBAR || changes.type.currentValue === LayoutPageType.COLUMNSIDEBAR)) {
      this.activeElement = this.rootElements[0].elements[0] as LayoutElementColumn;
    }

    if (changes.rootElements) {
      this.recalculateWidthDesktopRecursive(this.rootElements);
    }

    if (!this.currentSettings) {
      this.sharedService.showNotification('error', 'Nem található beállítás a kiválasztott típushoz ezen a domainen. Lépjen kapcsolatba a fejlesztővel');
    }
  }

  hasPermissionToDeleteOrEdit(layoutElement) {
    const hasPermission = layoutElement?.elements?.map((element) => {
      if (element.contentType === this.contentType.AD) {
        return this.hasAdvertisementEditRole;
      }
      return element?.elements ? this.hasPermissionToDeleteOrEdit(element) : true;
    });

    // layoutElement can be layoutElementRow | layoutElementColumn | layoutElementContent | LayoutElement
    return hasPermission ? !hasPermission.includes(false) : layoutElement.contentType === this.contentType.AD ? this.hasAdvertisementEditRole : true;
  }

  public isAutoColsMasonry(element: LayoutElementContentDetections): boolean {
    return !(element.contentLength === 1 || element.parentWidth <= element.minParentWidth || this.type === 'Sidebar' || this.type === 'ColumnSidebar');
  }

  private reset() {
    if (this.type === LayoutPageType.SIDEBAR || this.type === LayoutPageType.COLUMNSIDEBAR) {
      this.initSidebar();
    } else {
      this.rootElements = [];
      this.activeElement = null;
    }
  }

  initSidebar() {
    this.rootElements = [
      {
        id: this.generateId(),
        type: LayoutElementType.ROW,
        widthDesktop: 12,
        widthDesktopRecursive: 12,
        backgroundColor: '',
        hideMobile: false,
        withBlockTitle: false,
        elements: [
          {
            id: this.generateId(),
            type: LayoutElementType.COLUMN,
            hideMobile: false,
            widthDesktop: 12,
            widthDesktopRecursive: 12,
            elements: [],
            withBlockTitle: false,
          },
        ],
      },
    ];
    this.activeElement = this.rootElements[0].elements[0] as LayoutElementColumn;
  }

  getContentTypeEnumString(input: LayoutElementContentType | LayoutElementContentTypeConfig) {
    if (typeof input === 'string') {
      return input as LayoutElementContentType;
    }
    const config = input as LayoutElementContentTypeConfig;
    return config.type;
  }

  toggleToolbar(type: string) {
    this.toolsOpened = type !== 'hide';
  }

  openRowModal(modifyItem?: LayoutElementRow, parent?: LayoutElementRow | LayoutElementColumn): void {
    if (this.type === 'Sidebar' || this.type === 'ColumnSidebar') {
      return;
    }

    let selectedColumnLayout: number[];
    if (modifyItem && modifyItem.elements) {
      selectedColumnLayout = modifyItem.elements.map((elem) => (elem as LayoutElementColumn).widthDesktop);
    }
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalRowComponent, !!modifyItem, 'Sor'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        selectedColumnLayout: modifyItem ? selectedColumnLayout : null,
        canHaveBackground: !this.activeElement || !!modifyItem,
        backgroundColor: modifyItem ? modifyItem.backgroundColor : '',
        hasLine: modifyItem?.hasLine ?? false,
        isMobileSideBySide: modifyItem?.isMobileSideBySide ?? false,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Kiválasztás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.selectedColumnLayout) {
              return;
            }

            const settingsOnClose = {
              selectedColumnLayout: componentInstance.selectedColumnLayout,
              withBlockTitle: componentInstance.withBlockTitle,
              hideMobile: componentInstance.hideMobile,
              backgroundColor: componentInstance.backgroundColor,
              hasLine: componentInstance.hasLine,
              isMobileSideBySide: componentInstance.isMobileSideBySide,
            };

            if (
              modifyItem &&
              !this.areColumnWidthsEqual(
                modifyItem.elements.filter((el) => el.type === LayoutElementType.COLUMN).map((c) => (c as LayoutElementColumn).widthDesktop),
                componentInstance.selectedColumnLayout
              )
            ) {
              const confirmModal = this.modal.confirm({
                nzTitle: this.translate.instant('CMS.confirm_modify_layout_element_title'),
                nzContent: this.translate.instant('CMS.confirm_delete_layout_element_lead'),
                nzOkText: this.translate.instant('CMS.amend'),
                nzCancelText: this.translate.instant('CMS.cancel'),
                nzIconType: '',
                nzOnOk: () =>
                  new Promise(() => {
                    confirmModal.destroy();
                    modal.destroy(settingsOnClose);
                  }),
              });
            } else {
              modal.destroy(settingsOnClose);
            }
          },
          type: 'primary',
        },
      ],
    });

    modal.afterClose
      .pipe(filter((result) => !!result))
      .subscribe((result) =>
        this.addRow(
          result?.selectedColumnLayout,
          result?.withBlockTitle,
          result?.hideMobile,
          result?.backgroundColor,
          result?.hasLine,
          result?.isMobileSideBySide,
          modifyItem,
          parent
        )
      );
  }

  private getTotalWidthInRow(rowElement: LayoutElementRow): number {
    return (
      rowElement.elements
        .filter((val) => (val as LayoutElementColumn).widthDesktop)
        .map((elem) => (elem as LayoutElementColumn).widthDesktop)
        .reduce((acc, val) => acc + val, 0) ?? 12
    );
  }

  openColumnModal(modifyItem?: LayoutElementColumn, parent?: LayoutElementRow): void {
    if (
      ((!this.activeElement || this.activeElement.type !== LayoutElementType.ROW) && !modifyItem) ||
      this.type === 'Sidebar' ||
      this.type === 'ColumnSidebar'
    ) {
      return;
    }

    // check if row is full on insertion
    if (
      !modifyItem &&
      (this.activeElement.elements.findIndex((child) => child.type === LayoutElementType.CONTENT) > -1 ||
        ((this.activeElement as LayoutElementRow).elements.filter((child) => child.type === LayoutElementType.COLUMN) as LayoutElementColumn[]).reduce(
          (a, b) => a + (b.widthDesktop || 0),
          0
        ) >= 10) //We use 10, as there is no option to select 1/5 as width.
    ) {
      this.modal.error({
        nzTitle: 'A sorban nincs hely több oszlop beszúrására.<br>Ha több oszlopot szeretnél használni, csökkentsd a sorban lévő oszlopok szélességét.',
        nzOkType: 'primary',
        nzOkDanger: true,
        nzOkText: this.translate.instant('CMS.ok'),
      });
      return;
    }

    const allocatedWidth = this.getTotalWidthInRow(parent ?? (this.activeElement as LayoutElementRow));

    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalColumnComponent, !!modifyItem, 'Oszlop'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        width: modifyItem ? modifyItem.widthDesktop : null,
        allocatedWidth: allocatedWidth ?? 0,
        hasRightMargin: modifyItem?.hasRightMargin ?? false,
        hasLeftMargin: modifyItem?.hasLeftMargin ?? false,
        mobileOrder: modifyItem?.mobileOrder ?? null,
        marginBorderColor: modifyItem?.marginBorderColor ?? 'gray',
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.widthControl.value || componentInstance?.mobilOrderControl?.value < 0) {
              return;
            }
            modal.destroy({
              width: componentInstance.widthControl.value,
              withBlockTitle: componentInstance.withBlockTitle,
              hideMobile: componentInstance.hideMobile,
              hasRightMargin: componentInstance?.hasRightMargin,
              hasLeftMargin: componentInstance?.hasLeftMargin,
              marginBorderColor: componentInstance?.marginBorderColor,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    modal.afterClose
      .pipe(filter((result) => !!result))
      .subscribe((result) =>
        this.addColumn(
          result.width,
          result.withBlockTitle,
          result.hideMobile,
          result.hasLeftMargin,
          result.hasRightMargin,
          result.marginBorderColor,
          result.mobileOrder,
          modifyItem,
          parent
        )
      );
  }

  openContentFastNewsModal(modifyItem?: LayoutElementContentFastNews, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentFastNewsComponent, !!modifyItem, 'Gyors hír lista'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (
              !componentInstance ||
              !componentInstance.lengthControl.value ||
              !componentInstance.selectedArticleType ||
              this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)
            ) {
              return;
            }

            modal.destroy({
              contentLength: componentInstance.lengthControl.value,
              ...componentInstance.selectedArticleType,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentFastNews(result, modifyItem, parent));
    }
  }

  openContentMinuteToMinuteModal(modifyItem?: LayoutElementContentMinuteToMinutes, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentMinuteToMinuteComponent, !!modifyItem, 'Percről Percre lista'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (
              !componentInstance ||
              !componentInstance.lengthControl.value ||
              !componentInstance.selectedArticleType ||
              this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)
            ) {
              return;
            }

            modal.destroy({
              contentLength: componentInstance.lengthControl.value,
              ...componentInstance.selectedArticleType,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              withHorizontalSeparator: componentInstance.withHorizontalSeparator,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentMinuteToMinutes(result, modifyItem, parent));
    }
  }

  openContentWeeklyNewspaperModal(modifyItem?: LayoutElementContentWeeklyNewspaper, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentWeeklyNewspaperComponent, !!modifyItem, 'Hetilap cikkei'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        weeklyNewspaperArticleTypeConfigs: (modifyItem as any)?.previewImages ? [...(modifyItem as any)?.previewImages] : [],
        contentLength: modifyItem?.contentLength || 1,
        styleId: modifyItem?.styleId || null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance.weeklyNewspaperArticleTypeConfigs.length) {
              this.msg.error('Hetilap cikkeinek kiválasztása és megadása kötelező!');
              return;
            }
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }

            modal.destroy({
              firstPreviewImage: '/assets/images/layout-frames/mandiner/journal01.png',
              previewImages: [...componentInstance.weeklyNewspaperArticleTypeConfigs],
              contentLength: componentInstance.contentLength,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentWeeklyNewspaper(result, modifyItem, parent));
    }
  }

  openContentQuizModal(modifyItem?: LayoutElementContentQuiz, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentQuizComponent, !!modifyItem, 'Kvíz lista'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (
              !componentInstance ||
              !componentInstance.lengthControl.value ||
              !componentInstance.selectedQuizType ||
              this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)
            ) {
              return;
            }
            modal.destroy({
              contentLength: componentInstance.lengthControl.value,
              ...componentInstance.selectedQuizType,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentQuiz(result, modifyItem, parent));
    }
  }
  openContentSponsoredQuizModal(modifyItem?: LayoutElementContentSponsoredQuiz, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentSponsoredQuizComponent, !!modifyItem, 'Szponzorált kvíz lista'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (
              !componentInstance ||
              !componentInstance.lengthControl.value ||
              !componentInstance.selectedQuizType ||
              this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)
            ) {
              return;
            }
            modal.destroy({
              contentLength: componentInstance.lengthControl.value,
              ...componentInstance.selectedQuizType,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentSponsoredQuiz(result, modifyItem, parent));
    }
  }

  openContentDrawnMapListModal(modifyItem?: LayoutElementContentDrawnMapList, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentDrawnMapListComponent, !!modifyItem, 'Rajzolt térképek'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        selectedType: modifyItem ? modifyItem : null,
        contentLength: modifyItem ? modifyItem.contentLength : 1,
        styleId: modifyItem ? modifyItem.styleId : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance?.selectedType) {
              this.sharedService.showNotification('warning', 'Térkép választása kötelező!');
              return;
            }

            modal.destroy({
              ...componentInstance.selectedType,
              contentLength: componentInstance.contentLength,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentDrawnMapList(result, modifyItem, parent));
    }
  }

  openContentImageMapListModal(modifyItem?: LayoutElementContentImageMapList, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentImageMapListComponent, !!modifyItem, 'Képlistás térképek'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        selectedType: modifyItem ? modifyItem : null,
        contentLength: modifyItem ? modifyItem.contentLength : 1,
        styleId: modifyItem ? modifyItem.styleId : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance?.selectedType) {
              this.sharedService.showNotification('warning', 'Térkép választása kötelező!');
              return;
            }

            modal.destroy({
              ...componentInstance.selectedType,
              contentLength: componentInstance.contentLength,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentImageMapList(result, modifyItem, parent));
    }
  }

  openContentArticleModal(
    modifyItem?: LayoutElementContentArticle,
    parent?: LayoutElementRow | LayoutElementColumn,
    secondaryContentType = LayoutElementContentType.ARTICLE
  ): void {
    const parentElement = parent ?? this.activeElement;
    const onClick = (componentInstance) => {
      if (
        !componentInstance ||
        !componentInstance.lengthControl.value ||
        !componentInstance.selectedArticleType ||
        this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)
      ) {
        return;
      }
      modal.destroy({
        mobileOrder: componentInstance.mobilOrderControl?.value,
        contentLength: componentInstance.lengthControl.value,
        ...componentInstance.selectedArticleType,
        hideMobile: componentInstance.hideMobile,
        withBlockTitle: componentInstance.withBlockTitle,
        withHorizontalSeparator: componentInstance.withHorizontalSeparator,
        withVerticalSeparator: componentInstance?.withVerticalSeparator,
        secondaryContentType: componentInstance.secondaryContentType,
        fontSize: componentInstance.fontSizeControl?.value,
        hasBackground: componentInstance?.hasBackground,
        isLight: componentInstance.isLight,
        hasLine: componentInstance.hasLine,
        hasOutline: componentInstance?.hasOutline,
      });
    };

    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentArticleComponent, !!modifyItem, ''),
      nzClosable: false,
      nzData: {
        ...this.getMobilorder(modifyItem),
        ...this.getCommonNzComponentParams(modifyItem),
        title:
          secondaryContentType === LayoutElementContentType.ARTICLE
            ? modifyItem
              ? 'Cikk lista módosítása'
              : 'Cikk lista hozzáadása'
            : this.translate.instant('CMS.Layouts.layout-content-type-' + secondaryContentType),
        buttonLabel: modifyItem ? 'Módosítás' : 'Hozzáadás',
        withHorizontalSeparator: modifyItem?.withHorizontalSeparator ?? false,
        withVerticalSeparator: modifyItem?.withVerticalSeparator ?? false,
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
        pageType: this.type,
        secondaryContentType,
        parentWidth: parentElement?.hasOwnProperty('widthDesktopRecursive') ? (parentElement as LayoutElementColumn).widthDesktopRecursive : null,
        fontSize: modifyItem?.fontSize || 20,
        isLight: modifyItem?.isLight,
        hasLine: modifyItem?.hasLine,
        hasBackground: modifyItem?.hasBackground || false,
        hasOutline: modifyItem?.hasOutline || false,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: onClick,
          type: 'primary',
        },
      ],
    });

    modal.componentInstance.saveClick.pipe(takeUntil(this.destroy$)).subscribe(() => onClick(modal.componentInstance));
    modal.componentInstance.closeClick.pipe(takeUntil(this.destroy$)).subscribe(() => modal.destroy());

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentArticle(result, modifyItem, parent));
    }
  }

  openContentNoteModal(modifyItem?: LayoutElementContentOpinion, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentNoteComponent, !!modifyItem, 'Jegyzet lista'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.lengthControl.value || !componentInstance.selectedNoteType) {
              return;
            }
            modal.destroy({
              contentLength: componentInstance.lengthControl.value,
              ...componentInstance.selectedNoteType,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentNote(result, modifyItem, parent));
    }
  }

  openContentOpinionListModal(modifyItem?: LayoutElementContentOpinionList, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentOpinionListComponent, !!modifyItem, 'Vélemény lista'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        withHorizontalSeparator: modifyItem?.withHorizontalSeparator ?? false,
        domain: this.domainKey,
        fontSize: modifyItem?.fontSize || 20,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.selectedOpinionListType || this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              contentLength: componentInstance.lengthControl.value,
              ...componentInstance.selectedOpinionListType,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              withHorizontalSeparator: componentInstance.withHorizontalSeparator,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              fontSize: componentInstance.fontSizeControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentOpinionList(result, modifyItem, parent));
    }
  }

  openContentOpinionModal(
    modifyItem?: LayoutElementContentOpinion,
    parent?: LayoutElementRow | LayoutElementColumn,
    secondaryContentType = LayoutElementContentType.OPINION
  ): void {
    const title =
      secondaryContentType == LayoutElementContentType.OPINION ? 'Vélemény' : this.translate.instant('CMS.Layouts.layout-content-type-' + secondaryContentType);
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentOpinionComponent, !!modifyItem, title),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
        showHeader: modifyItem ? modifyItem.showHeader : false,
        withVerticalSeparator: modifyItem ? modifyItem.withVerticalSeparator : false,
        secondaryContentType,
        fontSize: modifyItem?.fontSize || 20,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (
              !componentInstance ||
              !componentInstance.lengthControl.value ||
              !componentInstance.selectedOpinionType ||
              this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)
            ) {
              return;
            }
            modal.destroy({
              contentLength: componentInstance.lengthControl.value,
              ...componentInstance.selectedOpinionType,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              showHeader: componentInstance.showHeader,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              secondaryContentType: componentInstance.secondaryContentType,
              firstPreviewImage: componentInstance.showHeader ? '/assets/images/layout-frames/mandiner/opinion-header.png' : '',
              fontSize: componentInstance.fontSizeControl?.value,
              withVerticalSeparator: componentInstance?.withVerticalSeparator,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentOpinion(result, modifyItem, parent));
    }
  }

  openContentBayerBlogModal(
    modifyItem?: LayoutElementContentBayerBlog,
    parent?: LayoutElementRow | LayoutElementColumn,
    secondaryContentType = LayoutElementContentType.BAYER_BLOG
  ): void {
    const title = 'Bayer Zsolt Blogja';
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentOpinionComponent, !!modifyItem, title),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
        secondaryContentType,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (
              !componentInstance ||
              !componentInstance.lengthControl.value ||
              !componentInstance.selectedOpinionType ||
              this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)
            ) {
              return;
            }
            modal.destroy({
              contentLength: componentInstance.lengthControl.value,
              ...componentInstance.selectedOpinionType,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              showHeader: componentInstance.showHeader,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              secondaryContentType: componentInstance.secondaryContentType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentBayerBlog(result, modifyItem, parent));
    }
  }

  openContentWhereTheBallWillBe(
    modifyItem?: LayoutElementContentWhereTheBallWillBe,
    parent?: LayoutElementRow | LayoutElementColumn,
    secondaryContentType = LayoutElementContentType.WHERE_THE_BALL_WILL_BE
  ): void {
    const title = 'Ahol a labda lesz';
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentOpinionComponent, !!modifyItem, title),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
        secondaryContentType,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (
              !componentInstance ||
              !componentInstance.lengthControl.value ||
              !componentInstance.selectedOpinionType ||
              this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)
            ) {
              return;
            }
            modal.destroy({
              contentLength: componentInstance.lengthControl.value,
              ...componentInstance.selectedOpinionType,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              showHeader: componentInstance.showHeader,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              secondaryContentType: componentInstance.secondaryContentType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentWhereTheBallWillBe(result, modifyItem, parent));
    }
  }

  // TODO: add domain options
  openContentVideoModal(modifyItem?: LayoutElementContentVideo, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentVideoComponent, !!modifyItem, 'Videó lista'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.lengthControl.value || !componentInstance.selectedVideoType) {
              return;
            }
            modal.destroy({
              contentLength: componentInstance.lengthControl.value,
              ...componentInstance.selectedVideoType,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentVideo(result, modifyItem, parent));
    }
  }

  openContentShortVideoModal(modifyItem?: LayoutElementContentShortVideos, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentShortVideosComponent, !!modifyItem, 'Short videó lista'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.lengthControl.value || !componentInstance.selectedShortVideoType) {
              return;
            }
            modal.destroy({
              contentLength: componentInstance.lengthControl.value,
              ...componentInstance.selectedShortVideoType,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentShortVideo(result, modifyItem, parent));
    }
  }

  openContentNewsFeedModal(modifyItem?: LayoutElementContentDossier, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentNewsFeedComponent, !!modifyItem, 'Hírfolyam'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        domain: this.domainKey,
        selectedNewsfeedType: modifyItem ? modifyItem : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance.selectedNewsfeedType) {
              this.msg.error('Sablon kiválasztása kötelező!');
              return;
            }
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              ...componentInstance.selectedNewsfeedType,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              previewImage: componentInstance.selectedNewsfeedType.previewImage ?? '',
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => {
        this.addContentNewsFeed(result, modifyItem, parent);
      });
    }
  }

  openBlockSeparatorModal(modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn): void {
    if (modifyItem) {
      this.msg.info('Ennek az elemnek nincsenek szerkeszthető paraméterei.');
    }
    this.addBlockSeparator(
      {
        previewImage: '/assets/images/layout-frames/nso/block-separator.png',
      },
      modifyItem,
      parent
    );
  }

  openContentAuthorModal(modifyItem?: LayoutElementContent, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentAuthorComponent, !!modifyItem, 'Szerző'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        selectedType: modifyItem || null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance?.selectedType) {
              this.msg.error('Megjelenés választása kötelező!');
              return;
            }

            modal.destroy({
              ...componentInstance?.selectedType,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentAuthor(result, modifyItem, parent));
    }
  }

  openContentDossierModal(modifyItem?: LayoutElementContentDossier, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentDossierComponent, !!modifyItem, 'Dosszié'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        domain: this.domainKey,
        selectedDossierType: modifyItem ? modifyItem : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (
              !componentInstance ||
              !componentInstance.selectedDossierType ||
              componentInstance.selectedDossierType.maxSecondaryArticleCount.filter((count) => !count).length > 0
            ) {
              this.msg.error('Sablon kiválasztása és másodlagos cikkek számának megadása kötelező!');
              return;
            }
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }

            modal.destroy({
              ...componentInstance.selectedDossierType,
              hideMobile: componentInstance.hideMobile,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentDossier(result, modifyItem, parent));
    }
  }

  openContentArticlesWithVideoContent(modifyItem?: LayoutElementContentArticlesWithVideoContent, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentArticlesWithVideoComponent, !!modifyItem, 'Videós Cikkek'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        styleId: modifyItem?.styleId || null,
        articleCount: modifyItem?.articleCount || 3,
        pageType: this.type,
        domain: this.domainKey,
        ...this.getMobilorder(modifyItem),
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }

            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              contentLength: componentInstance.contentLength,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              articleCount: componentInstance.articleCount,
              pageType: this.type,
              ...componentInstance.selectedVideoBlock,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentArticleWithVideo(result, modifyItem, parent));
    }
  }

  openContentArticlesWithPodcastContent(modifyItem?: LayoutElementContentArticlesWithPodcastContent, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentArticlesWithPodcastContentComponent, !!modifyItem, 'Podcastos cikkek'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        styleId: modifyItem?.styleId || 1,
        btnUrl: modifyItem?.btnUrl || '',
        domain: this.domainKey,
        contentLength: modifyItem ? modifyItem.contentLength : null,
        hideMobile: modifyItem ? modifyItem.hideMobile : null,
        type: this.type,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance) || !componentInstance.selectedPodcastBlock) {
              return;
            }
            if (!componentInstance?.lengthControl?.value) {
              this.sharedService.showNotification('error', 'Nincs kiválasztva a cikkek száma!');
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              contentLength: componentInstance.lengthControl.value,
              btnUrl: componentInstance.urlControl.value || '',
              mobileOrder: componentInstance.mobilOrderControl?.value,
              articleCount: componentInstance.articleCount,
              ...componentInstance.selectedPodcastBlock,
              ...(this.domainKey === 'pesti_sracok' && {
                contentLength: componentInstance.lengthControl.value || 1,
              }),
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => {
        this.addContentArticleWithPodcast(result, modifyItem, parent);
      });
    }
  }

  openContentOlimpiaArticlesWithPodcastContent(
    modifyItem?: LayoutElementContentOlimpiaArticlesWithPodcastContent,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentOlimpiaArticlesWithPodcastContentComponent, !!modifyItem, 'Olimpia Podcastos cikkek'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        styleId: modifyItem?.styleId || 1,
        domain: this.domainKey,
        contentLength: modifyItem ? modifyItem.contentLength : null,
        articleLength: modifyItem ? modifyItem.articleLength : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              contentLength: 1,
              articleLength: componentInstance.articleLengthControl.value,
              ...componentInstance.selectedPodcastBlock,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => {
        this.addContentOlimpiaArticleWithPodcast(result, modifyItem, parent);
      });
    }
  }

  openContentPodcastArticleList(modifyItem?: LayoutElementContentArticle, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentPodcastArticleListComponent, !!modifyItem, 'Podcastos cikk lista'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        styleId: modifyItem?.styleId || null,
        domain: this.domainKey,
        contentLength: modifyItem ? modifyItem.contentLength : 1,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.lengthControl.value || !componentInstance.selectedPodcast) {
              this.msg.error('Sablon kiválasztása és cikkek számának megadása kötelező!');
              return;
            }
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              contentLength: componentInstance.lengthControl.value,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              ...componentInstance.selectedPodcast,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => {
        this.addContentPodcastArticleList(result, modifyItem, parent);
      });
    }
  }

  openContentSpotlight(modifyItem?: LayoutElementContentSpotlight, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentSpotlightComponent, !!modifyItem, 'Spotlight'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        styleId: modifyItem?.styleId || null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.selectedSpotlightType?.styleId) {
              this.sharedService.showNotification('error', 'Megjelenési formátum választása kötelező!');
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              contentLength: componentInstance.contentLength,
              ...componentInstance.selectedSpotlightType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentSpotlight(result, modifyItem, parent));
    }
  }

  openContentTurpiBox(modifyItem?: LayoutElementContentTurpiBox, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentTurpiBoxComponent, !!modifyItem, 'Turpi doboz'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        styleId: modifyItem?.styleId || null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance.selectedTurpiBoxType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentTurpiBox(result, modifyItem, parent));
    }
  }

  openContentDailyMenu(modifyItem?: LayoutElementContentDailyMenu, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentDailyMenuComponent, !!modifyItem, 'Napi menü'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        selectedStyle: {
          styleId: modifyItem?.styleId || null,
          previewImage: null,
        },
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance.selectedStyle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentDailyMenu(result, modifyItem, parent));
    }
  }

  openContentOfferBox(modifyItem?: LayoutElementContentOfferBox, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentOfferBoxComponent, !!modifyItem, 'Ajánlatok'),
      nzData: {
        domain: this.domainKey,
        selectedStyle: { styleId: modifyItem?.styleId },
        withBlockTitle: modifyItem?.withBlockTitle,
        hideMobile: modifyItem?.hideMobile,
        contentLength: modifyItem?.contentLength || 1,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || componentInstance.selectedStyle?.styleId === undefined) {
              return;
            }

            modal.destroy({
              ...componentInstance.selectedStyle,
              withBlockTitle: componentInstance.withBlockTitle,
              hideMobile: componentInstance.hideMobile,
              contentLength: componentInstance.contentLength,
            } as LayoutEditorModalResult<BasicLayoutElementPreview>);
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentOfferBox(result, modifyItem, parent));
    }
  }

  openContentMaestroBox(modifyItem?: LayoutElementContentMaestroBox, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentMaestroBoxComponent, !!modifyItem, 'Maestro doboz'),
      nzData: {
        domain: this.domainKey,
        selectedStyle: { styleId: modifyItem?.styleId },
        withBlockTitle: modifyItem?.withBlockTitle,
        hideMobile: modifyItem?.hideMobile,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || componentInstance.selectedStyle?.styleId === undefined) {
              return;
            }

            modal.destroy({
              ...componentInstance.selectedStyle,
              withBlockTitle: componentInstance.withBlockTitle,
              hideMobile: componentInstance.hideMobile,
              contentLength: 1,
            } as LayoutEditorModalResult<BasicLayoutElementPreview>);
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentMaestroBox(result, modifyItem, parent));
    }
  }

  openContentPublicAuthors(modifyItem?: LayoutElementContentPublicAuthors, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentPublicAuthorsComponent, !!modifyItem, 'Nyilvános szerzők'),
      nzData: {
        domain: this.domainKey,
        selectedStyle: { styleId: modifyItem?.styleId },
        withBlockTitle: modifyItem?.withBlockTitle,
        hideMobile: modifyItem?.hideMobile,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || componentInstance.selectedStyle?.styleId === undefined) {
              return;
            }

            modal.destroy({
              ...componentInstance.selectedStyle,
              withBlockTitle: componentInstance.withBlockTitle,
              hideMobile: componentInstance.hideMobile,
              contentLength: 1,
            } as LayoutEditorModalResult<BasicLayoutElementPreview>);
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentPublicAuthors(result, modifyItem, parent));
    }
  }

  openContentHighlightedSelection(modifyItem?: LayoutElementContentHighlightedSelection, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentHighlightedSelectionComponent, !!modifyItem, 'Válogatás'),
      nzData: {
        domain: this.domainKey,
        selectedStyle: { styleId: modifyItem?.styleId },
        withBlockTitle: modifyItem?.withBlockTitle,
        hideMobile: modifyItem?.hideMobile,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || componentInstance.selectedStyle?.styleId === undefined) {
              return;
            }

            modal.destroy({
              ...componentInstance.selectedStyle,
              withBlockTitle: componentInstance.withBlockTitle,
              hideMobile: componentInstance.hideMobile,
              contentLength: 1,
            } as LayoutEditorModalResult<BasicLayoutElementPreview>);
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentHighlightedSelection(result, modifyItem, parent));
    }
  }

  openContentSelection(modifyItem?: LayoutElementContentSelection, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentSelectionComponent, !!modifyItem, 'Válogatás'),
      nzData: {
        domain: this.domainKey,
        selectedStyle: { styleId: modifyItem?.styleId },
        withBlockTitle: modifyItem?.withBlockTitle,
        hideMobile: modifyItem?.hideMobile,
        contentLength: modifyItem?.contentLength || 1,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || componentInstance.selectedStyle?.styleId === undefined) {
              return;
            }

            modal.destroy({
              ...componentInstance.selectedStyle,
              withBlockTitle: componentInstance.withBlockTitle,
              hideMobile: componentInstance.hideMobile,
              contentLength: componentInstance.contentLength,
            } as LayoutEditorModalResult<LayoutElementSelection>);
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentSelection(result, modifyItem, parent));
    }
  }

  openContentVideoBlockModal(modifyItem?: LayoutElementContentVideoBlock, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentVideoBlockComponent, !!modifyItem, 'Videó blokk'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentVideoBlock(result, modifyItem, parent));
    }
  }

  openContentDataBankModal(modifyItem?: LayoutElementContentDataBank, parent?: LayoutElementRow | LayoutElementColumn): void {
    const parentElement = parent ?? this.activeElement;
    const title = this.translate.instant('CMS.Layouts.layout-content-type-' + LayoutElementContentType.DATA_BANK) + ' hozzáadása';

    const onClick = (componentInstance) => {
      if (
        !componentInstance ||
        !componentInstance.lengthControl.value ||
        !componentInstance.selectedArticleType ||
        this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)
      ) {
        return;
      }
      modal.destroy({
        mobileOrder: componentInstance.mobilOrderControl?.value,
        contentLength: componentInstance.lengthControl.value,
        ...componentInstance.selectedArticleType,
        hideMobile: componentInstance.hideMobile,
        withBlockTitle: componentInstance.withBlockTitle,
        secondaryContentType: componentInstance.secondaryContentType,
      });
    };

    const modal = this.modal.create({
      nzClosable: false,
      ...this.getCommonModalParams(ModalContentDataBankComponent, !!modifyItem, title),
      nzData: {
        ...this.getMobilorder(modifyItem),
        ...this.getCommonNzComponentParams(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
        pageType: this.type,
        title,
        buttonLabel: modifyItem ? 'Módosítás' : 'Hozzáadás',
        parentWidth: parentElement?.hasOwnProperty('widthDesktopRecursive') ? (parentElement as LayoutElementColumn).widthDesktopRecursive : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: onClick,
          type: 'primary',
        },
      ],
    });

    modal.componentInstance.saveClick.pipe(takeUntil(this.destroy$)).subscribe(() => onClick(modal.componentInstance));
    modal.componentInstance.closeClick.pipe(takeUntil(this.destroy$)).subscribe(() => modal.destroy());

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentDataBank(result, modifyItem, parent));
    }
  }

  openContentDossierRepeaterModal(modifyItem?: LayoutElementContentDossierRepeater, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentDossierRepeaterComponent, !!modifyItem, 'Dossziék'),
      nzData: {
        ...this.getMobilorder(modifyItem),
        ...this.getCommonNzComponentParams(modifyItem),
        itemCount: modifyItem?.itemCount || 1,
        selectedDossier: modifyItem ? modifyItem : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance?.selectedDossier) {
              this.sharedService.showNotification('error', 'Nincs kiválasztott megjelenés a dossziékhoz!');
              return;
            }
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }

            modal.destroy({
              ...componentInstance.selectedDossier,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              itemCount: componentInstance.itemCount,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentDossierRepeater(result, modifyItem, parent));
    }
  }

  openContentAstrologyModal(modifyItem?: LayoutElementContentAstrologyBlock, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentAstrologyComponent, !!modifyItem, 'Asztrológia blokk'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance.selectedAstrologyType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentAstrologyBlock(result, modifyItem, parent));
    }
  }

  openContentTabsModal(modifyItem?: LayoutElementContentTabsBlock, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentTabsComponent, !!modifyItem, 'Tab blokk'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        domain: this.domainKey,
        tabs: modifyItem?.tabs || [],
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            const result = componentInstance.result;

            if (result.content.contentLength > 0 && result.content.contents.includes(null)) {
              this.sharedService.showNotification('warning', 'Régió választása kötelező!');
              return;
            }

            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              tabs: result.content.contents,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentTabsBlock(result, modifyItem, parent));
    }
  }

  openContentManualArticleModal(modifyItem?: LayoutElementContentManualArticle, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentManualArticleComponent, !!modifyItem, 'Manuális cikk lista'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
        manualListOrder: modifyItem ? modifyItem.manualListOrder : null,
        manualArticleListList: this.explicitArticleListManager.lists
          .filter((list) => list.type === 'article')
          .map((list) => {
            return {
              id: list.id,
              order: list.order,
              title: list.title,
              type: list.type,
            };
          }),
        manualArticleList: modifyItem ? modifyItem.manualList : null,
        fontSize: modifyItem ? modifyItem.fontSize : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (
              !componentInstance ||
              !componentInstance.lengthControl.value ||
              !componentInstance.selectedArticleType ||
              this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)
            ) {
              return;
            }
            modal.destroy({
              contentLength: componentInstance.lengthControl.value,
              ...componentInstance.selectedArticleType,
              hideMobile: componentInstance.hideMobile,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              manualListOrder: componentInstance.manualListOrderControl.value,
              manualList: componentInstance.manualListControl.value,
              withBlockTitle: componentInstance.withBlockTitle,
              fontSize: componentInstance.fontSizeControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => {
        this.addContentManualArtical(result, modifyItem, parent);
      });
    }
  }

  openContentManualOpinionModal(modifyItem?: LayoutElementContentManualOpinion, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentManualOpinionComponent, !!modifyItem, 'Manuális vélemény lista'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
        manualListOrder: modifyItem ? modifyItem.manualListOrder : null,
        manualArticleListList: this.explicitArticleListManager.lists
          .filter((list) => list.type === 'opinion')
          .map((list) => {
            return {
              id: list.id,
              order: list.order,
              title: list.title,
              type: list.type,
            };
          }),
        manualArticleList: modifyItem ? modifyItem.manualList : null,
        desktopHeader: modifyItem ? modifyItem.desktopHeader : false,
        desktopFooter: modifyItem ? modifyItem.desktopFooter : false,
        mobileHeader: modifyItem ? modifyItem.mobileHeader : false,
        mobileFooter: modifyItem ? modifyItem.mobileFooter : false,
        fontSize: modifyItem ? modifyItem.fontSize : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (
              !componentInstance ||
              !componentInstance.lengthControl.value ||
              !componentInstance.selectedOpinionType ||
              this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)
            ) {
              return;
            }
            modal.destroy({
              contentLength: componentInstance.lengthControl.value,
              ...componentInstance.selectedOpinionType,
              hideMobile: componentInstance.hideMobile,
              manualListOrder: componentInstance.manualListOrderControl.value,
              manualList: componentInstance.manualListControl.value,
              withBlockTitle: componentInstance.withBlockTitle,
              desktopHeader: componentInstance.desktopHeader,
              desktopFooter: componentInstance.desktopFooter,
              mobileHeader: componentInstance.mobileHeader,
              mobileFooter: componentInstance.mobileFooter,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              firstPreviewImage: componentInstance.desktopHeader ? '/assets/images/layout-frames/mandiner/opinion-header.png' : '',
              lastviewImage: componentInstance.desktopFooter ? '/assets/images/layout-frames/mandiner/opinion-footer.png' : '',
              fontSize: componentInstance.fontSizeControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => {
        this.addContentManualOpinion(result, modifyItem, parent);
      });
    }
  }

  openContentBreakingModal(modifyItem?: LayoutElementContentBreakingBlock, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentBreakingComponent, !!modifyItem, 'Breaking blokk'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.selectedBreakingType || this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              ...componentInstance.selectedBreakingType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentBreakingBlock(result, modifyItem, parent));
    }
  }

  openContentImageModal(modifyItem?: LayoutElementContentImage, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentImageComponent, !!modifyItem, 'Kép blokk'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        cancelMargin: modifyItem ? modifyItem.cancelMargin : null,
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              cancelMargin: componentInstance.cancelMargin,
              withBlockTitle: componentInstance.withBlockTitle,
              contentLength: componentInstance.lengthControl.value,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              ...componentInstance.selectedImageType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentImageBlock(result, modifyItem, parent));
    }
  }

  openContentPodcastListModal(modifyItem?: LayoutElementContentPodcastList, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentPodcastListComponent, !!modifyItem, 'Podcast lista'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : null,
        domain: this.domainKey,
        selectedPodcastListType: modifyItem ? modifyItem : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.lengthControl.value) {
              return;
            }
            modal.destroy({
              ...componentInstance.selectedPodcastListType,
              contentLength: componentInstance.lengthControl.value,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentPodcastList(result, modifyItem, parent));
    }
  }

  openContentArticleSliderModal(modifyItem?: LayoutElementContentArticleSlider, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentArticleSliderComponent, !!modifyItem, 'Lapozható cikk'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        contentLength: modifyItem?.contentLength || 1,
        domain: this.domainKey,
        styleId: modifyItem ? modifyItem.styleId : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.lengthControl.value) {
              return;
            }
            modal.destroy({
              ...componentInstance.selectedArticleType,
              contentLength: componentInstance.lengthControl.value,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              styleId: componentInstance.selectedArticleType.styleId,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentArticleSlider(result, modifyItem, parent));
    }
  }

  openContentAdModal(modifyItem?: LayoutElementContentAd, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentAdComponent, !!modifyItem, 'Hirdetés'),
      nzData: {
        ...this.getMobilorder(modifyItem),
        medium: modifyItem ? modifyItem.medium : null,
        bannerName: modifyItem ? modifyItem.bannerName : null,
        pageType: this.mapTypeToAdType(this.type),
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (
              !componentInstance ||
              !componentInstance.mediumControl.value ||
              !componentInstance.bannerControl.value ||
              this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)
            ) {
              return;
            }
            modal.destroy({
              medium: componentInstance.mediumControl.value,
              bannerName: componentInstance.bannerControl.value,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentAd(result, modifyItem, parent));
    }
  }

  openContentWysiwygModal(modifyItem?: LayoutElementContentWysiwyg, parent?: LayoutElementRow | LayoutElementColumn) {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentWysiwygComponent, !!modifyItem, 'Szöveges tartalom'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentWysiwyg(result, modifyItem, parent));
    }
  }

  openContentNewsletterModal(modifyItem?: LayoutElementContentNewsletterBlock, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentNewsletterComponent, !!modifyItem, 'Hírlevél blokk'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.selectedNewsletterType || this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance.selectedNewsletterType,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentNewsletterBlock(result, modifyItem, parent));
    }
  }

  openContentGongNewsletterModal(modifyItem?: LayoutElementContentNewsletterBlockGong, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentGongNewsletterComponent, !!modifyItem, 'Gong Hírlevél blokk'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.selectedNewsletterType || this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance.selectedNewsletterType,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentNewsletterBlockGong(result, modifyItem, parent));
    }
  }

  openContentOpinionNewsletterModal(modifyItem?: LayoutElementContentOpinionNewsletterBox, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentOpinionNewsletterComponent, !!modifyItem, 'Heti vélemény hírlevél'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.selectedType || this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance.selectedType,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentOpinionNewsletterBox(result, modifyItem, parent));
    }
  }

  openContentAgroKepModal(modifyItem?: LayoutElementContentNewsletterBlock, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentAgroKepComponent, !!modifyItem, 'AgroKép blokk'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        styleId: modifyItem ? modifyItem.styleId : null,
        contentLength: 1,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.selectedAgroKepType || this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              mobileOrder: componentInstance.mobilOrderControl?.value,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              contentLength: 1,
              ...componentInstance.selectedAgroKepType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentAgroKepBlock(result, modifyItem, parent));
    }
  }

  openContentAgroKepListModal(modifyItem?: LayoutElementContentNewsletterBlock, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentAgroKepListComponent, !!modifyItem, 'Agrokép lista blokk'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        styleId: modifyItem ? modifyItem.styleId : null,
        contentLength: modifyItem ? modifyItem.contentLength : null,
        isMegyeiLap: this.isMegyeiLap,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.selectedAgroKepListType) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              contentLength: componentInstance.lengthControl.value,
              ...componentInstance.selectedAgroKepListType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentAgroKepListBlock(result, modifyItem, parent));
    }
  }

  openContentStocksChartModal(modifyItem?: LayoutElementContentStockChart, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentStocksChartComponent, !!modifyItem, 'Tőzsdeinfó'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
        border: modifyItem ? modifyItem.border : false,
        dataType: modifyItem ? modifyItem.dataType : ExchangeBoxDataType.STOCK,
        ...this.getMobilorder(modifyItem),
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              mobileOrder: componentInstance.mobilOrderControl?.value,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              styleId: componentInstance.styleId,
              contentLength: 1,
              previewImage: componentInstance.selectedOption.previewImage,
              border: componentInstance.border,
              dataType: componentInstance.dataType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentStockChart(result, modifyItem, parent));
    }
  }

  openContentFreshNewsModal(modifyItem?: LayoutElementContentFreshNews, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentFreshNewsComponent, !!modifyItem, '24 óra sáv'),
      nzData: {
        ...this.getMobilorder(modifyItem),
        ...this.getCommonNzComponentParams(modifyItem),
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentFreshNews(result, modifyItem, parent));
    }
  }

  openContentLatestNewsModal(modifyItem?: LayoutElementContentLatestNews, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentLatestNewsComponent, !!modifyItem, 'Friss hírek'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              numberOfArticles: componentInstance.numberOfArticles,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentLatestNews(result, modifyItem, parent));
    }
  }

  openContentLinkListModal(modifyItem?: LayoutElementContentLinkList, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentLinkListComponent, !!modifyItem, 'Link lista'),
      nzData: this.getCommonNzComponentParams(modifyItem),
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentLinkList(result, modifyItem, parent));
    }
  }
  openContentSponsoredVoteModal(modifyItem?: LayoutElementContentSponsoredVote, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentSponsoredVoteComponent, !!modifyItem, 'Szponzorált szavazás'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              previewImage: '/assets/images/layout-frames/shared/sponsored-vote.png',
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentSponsoredVote(result, modifyItem, parent));
    }
  }

  openContentVoteModal(modifyItem?: LayoutElementContentVote, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentVoteComponent, !!modifyItem, 'Szavazás'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        domain: this.domainKey,
        styleId: modifyItem ? modifyItem.styleId : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.selectedVoteType || this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              ...componentInstance.selectedVoteType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentVote(result, modifyItem, parent));
    }
  }

  openContentMultiVoteModal(modifyItem?: LayoutElementContentMultiVote, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentMultiVoteComponent, !!modifyItem, 'Multi Szavazás'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        domain: this.domainKey,
        styleId: modifyItem ? modifyItem.styleId : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.selectedMultiVoteType || this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              this.sharedService.showNotification('error', 'Mobil sorszám már létezik');
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              ...componentInstance.selectedMultiVoteType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentMultiVote(result, modifyItem, parent));
    }
  }

  openContentExperienceOccasionModal(modifyItem?: LayoutElementContentExperienceOccasion, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentGastroExperienceOccasionComponent, !!modifyItem, 'Gasztró Élmény alkalom'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : null,
        domain: this.domainKey,
        styleId: modifyItem ? modifyItem.styleId : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.selectedExperienceType || this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              this.sharedService.showNotification('error', 'Mobil sorszám már létezik');
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              contentLength: componentInstance.lengthControl.value,
              ...componentInstance.selectedExperienceType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentExperienceOccasion(result, modifyItem, parent));
    }
  }

  openContentVisegradPostModal(modifyItem?: LayoutElementContentVisegradPost, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentVisegradPostComponent, !!modifyItem, 'Visegrád Post'),
      nzData: this.getCommonNzComponentParams(modifyItem),
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              numberOfArticles: componentInstance.numberOfArticles,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentVisegradPost(result, modifyItem, parent));
    }
  }

  openContentProgramModel(modifyItem?: LayoutElementContentProgram, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentProgramComponent, !!modifyItem, 'Program lista'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.lengthControl.value || !componentInstance.selectedProgramType) {
              return;
            }
            modal.destroy({
              ...componentInstance.selectedProgramType,
              contentLength: componentInstance.lengthControl.value,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentProgram(result, modifyItem, parent));
    }
  }

  openContentCultureNationModal(modifyItem?: LayoutElementContentCultureNation, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentCultureNationComponent, !!modifyItem, 'Kúltúrnemzet'),
      nzData: this.getCommonNzComponentParams(modifyItem),
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentCultureNation(result, modifyItem, parent));
    }
  }

  openContentHtmlEmbedModal(modifyItem?: LayoutElementContentHtmlEmbed, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentHtmlEmbedComponent, !!modifyItem, 'Beágyazott tartalom'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              this.sharedService.showNotification('error', 'Mobil sorszám már létezik');
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentHtmlEmbed(result, modifyItem, parent));
    }
  }

  openContentNewspaperModal(modifyItem?: LayoutElementContentNewspaper, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentNewspaperComponent, !!modifyItem, 'Újság előfizetés'),
      nzData: this.getCommonNzComponentParams(modifyItem),
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentNewspaper(result, modifyItem, parent));
    }
  }

  openContentServicesBoxModal(modifyItem?: LayoutElementContentServicesBox, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentServicesBoxComponent, !!modifyItem, 'Szolgáltatások'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        styleId: modifyItem ? modifyItem.styleId : 1,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              styleId: componentInstance.styleId,
              previewImage: componentInstance.selectedServicesBoxType.previewImage,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentServicesBox(result, modifyItem, parent));
    }
  }

  openContentPodcastAppRecommenderModal(modifyItem?: LayoutElementContentPodcastAppRecommender, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentPodcastAppRecommenderComponent, !!modifyItem, 'Podcast alkalmazás terelő'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        styleId: modifyItem ? modifyItem.styleId : 1,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              styleId: componentInstance.styleId,
              previewImage: componentInstance.selectedOption.previewImage,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentPodcastAppRecommender(result, modifyItem, parent));
    }
  }

  openSponsoredArticleBoxModal(modifyItem?: LayoutElementContentSponsoredArticleBox, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentSponsoredArticleBoxComponent, !!modifyItem, 'Szponzorált cikk doboz'),
      nzData: this.getCommonNzComponentParams(modifyItem),
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentSponsoredArticleBox(result, modifyItem, parent));
    }
  }

  openContentDidYouKnowModal(modifyItem?: LayoutElementContentDidYouKnow, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentDidYouKnowComponent, !!modifyItem, 'Változó tartalmú branding box'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentDidYouKnow(result, modifyItem, parent));
    }
  }

  openContentOlimpiaHungarianCompetitionsModal(
    modifyItem?: LayoutElementContentOlimpiaHungarianCompetitions,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentOlimpiaHungarianCompetitionsComponent, !!modifyItem, 'Olimpia - Magyar versenyszámok'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentOlimpiaHungarianCompetitions(result, modifyItem, parent));
    }
  }

  openContentOlimpiaLargeNavigatorModal(modifyItem?: LayoutElementContentOlimpiaLargeNavigator, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentOlimpiaLargeNavigatorComponent, !!modifyItem, 'Olimpia - Nagy terelő'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        styleId: modifyItem ? modifyItem?.styleId : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              ...componentInstance?.selectedType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentOlimpiaLargeNavigator(result, modifyItem, parent));
    }
  }

  openContentMapRecommendations(modifyItem?: LayoutElementContentMapRecommendations, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModelContentMapRecommendationsComponent, !!modifyItem, 'Térképajánló'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        mapRecommendationsConfig: modifyItem?.mapConfig || [],
        contentLength: modifyItem ? modifyItem.contentLength : 1,
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              mapConfig: [...componentInstance.mapRecommendationsConfig],
              firstPreviewImage: '/assets/images/layout-frames/koponyeg/map-recommendations.png',
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              contentLength: componentInstance.contentLength,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentMapRecommendations(result, modifyItem, parent));
    }
  }

  openContentHero(modifyItem?: LayoutElementContentHero, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentHeroComponent, !!modifyItem, 'Hero'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              firstPreviewImage: '/assets/images/layout-frames/koponyeg/hero.jpg',
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentHero(result, modifyItem, parent));
    }
  }

  openContentMedicalMeteorology(modifyItem?: LayoutElementContentMedicalMeteorology, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentMedicalMeteorologyComponent, !!modifyItem, 'Orvosi meteorológia'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              firstPreviewImage: '/assets/images/layout-frames/koponyeg/orvosi-meteorologia.jpg',
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentMedicalMeteorology(result, modifyItem, parent));
    }
  }

  openContentTwelveDaysForecast(modifyItem?: LayoutElementContentTwelveDaysForecast, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentTwelveDaysForecastComponent, !!modifyItem, 'Orvosi meteorológia'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              previewImage: '/assets/images/layout-frames/koponyeg/tizenket-napos-elorejelzes.png',
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentTwelveDaysForecast(result, modifyItem, parent));
    }
  }

  openContentDetections(modifyItem?: LayoutElementContentDetections, parent?: LayoutElementRow | LayoutElementColumn): void {
    const parentElement = parent ?? this.activeElement;
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentDetectionsComponent, !!modifyItem, 'Észlelés'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        selectedType: modifyItem ? modifyItem : null,
        contentLength: modifyItem ? modifyItem.contentLength : 1,
        styleId: modifyItem ? modifyItem.styleId : null,
        layoutPageType: this.type,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance.selectedType) {
              this.sharedService.showNotification('warning', 'Észlelés választása kötelező!');
              return;
            }

            if ((componentInstance.selectedType as DetectionType).automataFill) {
              let automataFillItems = this.searchObjectArray(this.rootElements, ['contentType', 'automataFill'], [LayoutElementContentType.DETECTIONS, true]);
              automataFillItems = automataFillItems.filter((i) => i.id !== modifyItem?.id);
              if (automataFillItems.length > 0) {
                this.sharedService.showNotification('warning', 'Csak egy szöveges észlelés lehet a layouton!');
                return;
              }
            }

            modal.destroy({
              ...componentInstance.selectedType,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              contentLength: componentInstance.contentLength,
              parentWidth: (parentElement as LayoutElementColumn)?.widthDesktopRecursive || null,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentDetections(result, modifyItem, parent));
    }
  }

  openContentKulturNemzetModal(modifyItem?: LayoutElementContentArticle, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentKulturnemzetComponent, !!modifyItem, 'Kultúrnemzet'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.selectedArticleType) {
              return;
            }
            modal.destroy({
              ...componentInstance.selectedArticleType,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              contentLength: 1,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentKulturnemzet(result, modifyItem, parent));
    }
  }

  openContentSocialMediaModal(modifyItem?: LayoutElementContentSocialMedia, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentSocialMediaComponent, !!modifyItem, 'Social media box'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance.selectedOption,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentSocialMedia(result, modifyItem, parent));
    }
  }

  openExternalBrandingBox(modifyItem?: LayoutElementContentBrandingBoxEx, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentBrandingBoxExternalComponent, !!modifyItem, 'Branding box'),
      nzData: {
        ...this.getMobilorder(modifyItem),
        brand: modifyItem ? modifyItem.brand : null,
        articleLimit: modifyItem ? modifyItem.articleLimit : null,
        ...this.getCommonNzComponentParams(modifyItem),
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance.selectedBrandType) {
              componentInstance.notSelectedError = true;
              this.msg.error('Megjelenítési formátum választása kötelező!');
              return;
            }
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }

            modal.destroy({
              ...componentInstance.selectedBrandType,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentBrandingBoxExternal(result, modifyItem, parent));
    }
  }

  openContentJobListingsModal(modifyItem?: LayoutElementContentJobListings, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentJobListingsComponent, !!modifyItem, 'Álláshirdetések'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        domain: this.domainKey,
        selectedType: modifyItem?.previewImage ? modifyItem : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              ...componentInstance.selectedType,
              styleId: componentInstance.styleId,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentJobListings(result, modifyItem, parent));
    }
  }

  openContentTopTenTagsModal(modifyItem?: LayoutElementContentTopTenTags, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentTopTenTagsComponent, !!modifyItem, 'TOP 10 címke'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        domain: this.domainKey,
        selectedType: modifyItem?.previewImage ? modifyItem : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              ...componentInstance.selectedType,
              styleId: componentInstance.styleId,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentTopTenTags(result, modifyItem, parent));
    }
  }

  openContentTopicSuggestionModal(modifyItem?: LayoutElementContentTopicSuggestion, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentTopicSuggestionComponent, !!modifyItem, 'Téma beküldése'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        domain: this.domainKey,
        selectedType: modifyItem?.previewImage ? modifyItem : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              ...componentInstance.selectedType,
              styleId: componentInstance.styleId,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentTopicSuggestion(result, modifyItem, parent));
    }
  }

  openContentSecretDaysCalendarModal(modifyItem?: LayoutElementContentSecretDaysCalendar, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentSecretDaysCalendarComponent, !!modifyItem, 'Kalendárium'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        domain: this.domainKey,
        selectedType: modifyItem?.previewImage ? modifyItem : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              ...componentInstance.selectedType,
              styleId: componentInstance.styleId,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });
    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => {
        this.addContentSecretDaysCalendar(result, modifyItem, parent);
      });
    }
  }

  addContentBrandingBoxExternal(
    modalResult: LayoutEditorModalResult<LayoutElementContentBrandingBoxEx>,
    modifyItem?: LayoutElementContentBrandingBoxEx,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentBrandingBoxEx = {
      id: modifyItem?.id || this.generateId(),
      type: LayoutElementType.CONTENT,
      contentLength: 1,
      contentType: LayoutElementContentType.BRANDING_BOX_EX,
      configurable: false,
      ...modalResult,
    };
    this.updateParentElement(modifyItem, newElement, parent);
  }

  addContentJobListings(
    modalResult: LayoutEditorModalResult<LayoutElementContentJobListings>,
    modifyItem?: LayoutElementContentJobListings,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement: LayoutElementContentJobListings = {
      id: modifyItem?.id || this.generateId(),
      type: LayoutElementType.CONTENT,
      contentLength: 1,
      contentType: LayoutElementContentType.JOB_LISTINGS,
      configurable: false,
      ...modalResult,
    };
    this.updateParentElement(modifyItem, newElement, parent);
  }

  addContentTopTenTags(
    modalResult: LayoutEditorModalResult<LayoutElementContentTopTenTags>,
    modifyItem?: LayoutElementContentTopTenTags,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement: LayoutElementContentTopTenTags = {
      id: modifyItem?.id || this.generateId(),
      type: LayoutElementType.CONTENT,
      contentLength: 10,
      contentType: LayoutElementContentType.TOP_TEN_TAGS,
      configurable: true,
      ...modalResult,
    };
    this.updateParentElement(modifyItem, newElement, parent);
  }

  addContentTopicSuggestion(
    modalResult: LayoutEditorModalResult<LayoutElementContentTopicSuggestion>,
    modifyItem?: LayoutElementContentTopicSuggestion,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement: LayoutElementContentTopicSuggestion = {
      id: modifyItem?.id || this.generateId(),
      type: LayoutElementType.CONTENT,
      contentLength: 1,
      contentType: LayoutElementContentType.TOPIC_SUGGESTION,
      configurable: false,
      ...modalResult,
    };
    this.updateParentElement(modifyItem, newElement, parent);
  }

  addContentSecretDaysCalendar(
    modalResult: LayoutEditorModalResult<LayoutElementContentSecretDaysCalendar>,
    modifyItem?: LayoutElementContentSecretDaysCalendar,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement: LayoutElementContentSecretDaysCalendar = {
      id: modifyItem?.id || this.generateId(),
      type: LayoutElementType.CONTENT,
      contentLength: 1,
      contentType: LayoutElementContentType.SECRET_DAYS_CALENDAR,
      configurable: true,
      ...modalResult,
    };
    this.updateParentElement(modifyItem, newElement, parent);
  }

  openContentIngatlanBazarModal(modifyItem?: LayoutElementContentIngatlanbazar, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentIngatlanbazarComponent, !!modifyItem, 'Ingatlanbazár box'),
      nzData: this.getCommonNzComponentParams(modifyItem),
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentIngatlanbazar(result, modifyItem, parent));
    }
  }

  openContentIngatlanBazarConfigurableModal(modifyItem?: LayoutElementContentIngatlanbazarConfigurable, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentIngatlanbazarConfigurableComponent, !!modifyItem, 'Ingatlanbazár box'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        itemsToShow: modifyItem ? modifyItem.itemsToShow : 1,
        showHeader: modifyItem ? modifyItem.showHeader : false,
        xmlUrl: modifyItem ? modifyItem.xmlUrl : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              showHeader: componentInstance.showHeader,
              itemsToShow: componentInstance.itemsToShow,
              xmlUrl: componentInstance.xmlUrl,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentIngatlanbazarConfigurable(result, modifyItem, parent));
    }
  }

  openContentIngatlanBazarSearchModal(modifyItem?: LayoutElementContentIngatlanbazarSearch, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentIngatlanbazarSearchComponent, !!modifyItem, 'Ingatlanbazár kereső box'),
      nzData: this.getCommonNzComponentParams(modifyItem),
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentIngatlanbazarSearch(result, modifyItem, parent));
    }
  }

  openContentTagBlockModal(modifyItem?: LayoutElementContentTagBlock, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentTagBlockComponent, !!modifyItem, 'Címke szalag'),
      nzData: this.getCommonNzComponentParams(modifyItem),
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentTagBlock(result, modifyItem, parent));
    }
  }

  openContentTrendingTagsBlockModal(modifyItem?: LayoutElementContentTrendingTagsBlock, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentTrendingTagsBlockComponent, !!modifyItem, 'Trending címkék blokk'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentTrendingTagsBlock(result, modifyItem, parent));
    }
  }

  openContentBrandingBoxModal(modifyItem?: LayoutElementContentBrandingBox, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentBrandingBoxComponent, !!modifyItem, 'Branding box'),
      nzData: {
        ...this.getMobilorder(modifyItem),
        brand: modifyItem ? modifyItem.brand : null,
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.selectedBrandType || this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              ...componentInstance.selectedBrandType,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentBrandingBox(result, modifyItem, parent));
    }
  }

  openContentGalleryModal(modifyItem?: LayoutElementContentGallery, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentGalleryComponent, !!modifyItem, 'Galéria lista'),
      nzData: {
        ...this.getMobilorder(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : null,
        galleryLengthOverwrite: modifyItem ? modifyItem.galleryLengthOverwrite : null,
        isDisabledOverwriteContentLength: modifyItem ? modifyItem?.isDisabledOverwriteContentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (
              !componentInstance ||
              !componentInstance.lengthControl.value ||
              !componentInstance.selectedGalleryType ||
              this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)
            ) {
              return;
            }
            if (this.domainKey === 'magyarNemzet' && this.activeElement.widthDesktop !== 12 && componentInstance.galleryLengthOverwriteControl.value > 1) {
              this.sharedService.showNotification('warning', 'Ez az elem több galériával csak 1/1 elrendezéshez adható hozzá');
              return;
            }
            modal.destroy({
              mobileOrder: componentInstance.mobilOrderControl?.value,
              contentLength: componentInstance.lengthControl.value,
              ...componentInstance.selectedGalleryType,
              isDisabledOverwriteContentLength: componentInstance.getIsDisabledOverwriteContentLength(),
              galleryLengthOverwrite: componentInstance.galleryLengthOverwriteControl.value,
              galleryLength: componentInstance.galleryLengthOverwriteControl.value,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentGallery(result, modifyItem, parent));
    }
  }

  openContentPrBlockModal(modifyItem?: LayoutElementContentPrBlock, parent?: LayoutElementRow | LayoutElementColumn): void {
    const onClick = (componentInstance) => {
      if (
        !componentInstance ||
        !componentInstance.lengthControl.value ||
        !componentInstance.selectedArticleType ||
        this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)
      ) {
        return;
      }
      modal.destroy({
        mobileOrder: componentInstance.mobilOrderControl?.value,
        contentLength: componentInstance.lengthControl.value,
        ...componentInstance.selectedArticleType,
        hideMobile: componentInstance.hideMobile,
        withBlockTitle: componentInstance.withBlockTitle,
        withVerticalSeparator: componentInstance?.withVerticalSeparator,
      });
    };

    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentArticleComponent, !!modifyItem, 'PR blokk'),
      nzClosable: false,
      nzData: {
        ...this.getMobilorder(modifyItem),
        hasHorizontalSeparator: false,
        withVerticalSeparator: modifyItem?.withVerticalSeparator ?? false,
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        domain: this.domainKey,
        isPrBlock: true,
        buttonLabel: modifyItem ? 'Módosítás' : 'Hozzáadás',
        title: 'PR blokk hozzáadása',
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: onClick,
          type: 'primary',
        },
      ],
    });

    modal.componentInstance.saveClick.pipe(takeUntil(this.destroy$)).subscribe(() => onClick(modal.componentInstance));
    modal.componentInstance.closeClick.pipe(takeUntil(this.destroy$)).subscribe(() => modal.destroy());

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentPrBlock(result, modifyItem, parent));
    }
  }

  openContentBlogModal(modifyItem?: LayoutElementContentBlog, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentBlogComponent, !!modifyItem, 'Blog lista'),
      nzData: {
        ...this.getMobilorder(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (
              !componentInstance ||
              !componentInstance.lengthControl.value ||
              !componentInstance.selectedArticleType ||
              this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)
            ) {
              return;
            }
            modal.destroy({
              contentLength: componentInstance.lengthControl.value,
              ...componentInstance.selectedArticleType,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentBlog(result, modifyItem, parent));
    }
  }

  openContentKoponyegModal(modifyItem?: LayoutElementContentKoponyeg, parent?: LayoutElementRow | LayoutElementColumn) {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentKoponyegComponent, !!modifyItem, 'Köpönyeg beágyazás'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.selectedKoponyegType) {
              return;
            }
            modal.destroy({
              ...componentInstance.selectedKoponyegType,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentKoponyeg(result, modifyItem, parent));
    }
  }

  openAstronetColumns(modifyItem?: LayoutEditorModalResultMulti, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentAstronetColumnsComponent, !!modifyItem, 'Astronet Horoszkóp rovatok'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        domain: this.domainKey,
        selectedStyle: { styleId: modifyItem?.styleId },
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance): void => {
            if (!componentInstance || !componentInstance.selectedStyle?.styleId) {
              this.sharedService.showNotification('error', 'Megjelenési formátum választása kötelező!');
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance?.selectedStyle,
            });
          },
          type: 'primary',
        },
      ],
    });
    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentAstronetColumns(result, modifyItem, parent));
    }
  }

  openAstronetBrandingBox(modifyItem?: LayoutEditorModalResultMulti, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentAstronetBrandingBoxComponent, !!modifyItem, 'Astronet Branding Box'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        domain: this.domainKey,
        selectedStyle: { styleId: modifyItem?.styleId },
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance): void => {
            if (!componentInstance || !componentInstance.selectedStyle?.styleId) {
              this.sharedService.showNotification('error', 'Megjelenési formátum választása kötelező!');
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance?.selectedStyle,
            });
          },
          type: 'primary',
        },
      ],
    });
    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentAstronetBrandingBox(result, modifyItem, parent));
    }
  }

  openSubColumns(modifyItem?: LayoutEditorModalResultMulti, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentSubColumnsComponent, !!modifyItem, 'Alrovatok'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        domain: this.domainKey,
        selectedStyle: { styleId: modifyItem?.styleId },
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance): void => {
            if (!componentInstance || !componentInstance.selectedStyle?.styleId) {
              this.sharedService.showNotification('error', 'Megjelenési formátum választása kötelező!');
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance?.selectedStyle,
            });
          },
          type: 'primary',
        },
      ],
    });
    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentSubColumns(result, modifyItem, parent));
    }
  }

  openLatestAndMostReadArticles(modifyItem?: LayoutElementContent, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentBaseComponent, !!modifyItem, 'Legfrissebb és a legolvasottabb cikkek'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance): void => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });
    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentLatestAndMostReadArticles(result, modifyItem, parent));
    }
  }

  openStarBirthsModal(modifyItem?: LayoutElementContent, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentBaseComponent, !!modifyItem, 'Ezen a napon született sztárok'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance): void => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });
    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentStarBirths(result, modifyItem, parent));
    }
  }

  openContentWazeModal(modifyItem?: LayoutElementContentWaze, parent?: LayoutElementRow | LayoutElementColumn) {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentWazeComponent, !!modifyItem, 'Waze beágyazás'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });
    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentWaze(result, modifyItem, parent));
    }
  }

  openContentSzakikeresoModal(modifyItem?: LayoutElementContentSzakikereso, parent?: LayoutElementRow | LayoutElementColumn) {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentSzakikeresoComponent, !!modifyItem, 'Szakikereső beágyazás'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentSzakikereso(result, modifyItem, parent));
    }
  }

  openContentBestRecommenderModal(modifyItem?: LayoutElementContentBestRecommender, parent?: LayoutElementRow | LayoutElementColumn) {
    const modal = this.modal.create<ModalContentBestRecommenderComponent, LayoutEditorModalResultMulti>({
      ...this.getCommonModalParams(ModalContentBestRecommenderComponent, !!modifyItem, 'Metropol Best Ajánló'),
      nzData: {
        contentLength: modifyItem ? modifyItem.contentLength : 3,
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : true,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance: ModalContentBestRecommenderComponent) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              contentLength: componentInstance.lengthControl.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentBestRecommender(result, modifyItem, parent));
    }
  }

  openContentCategoryStepperModal(modifyItem?: LayoutElementContentCategoryStepper, parent?: LayoutElementRow | LayoutElementColumn) {
    const modal = this.modal.create<ModalContentCategoryStepperComponent, LayoutEditorModalResultMulti>({
      ...this.getCommonModalParams(ModalContentCategoryStepperComponent, !!modifyItem, 'Rovat blokk'),
      nzData: {
        ...this.getMobilorder(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : 6,
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : true,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance: ModalContentCategoryStepperComponent) => {
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              contentLength: componentInstance.lengthControl.value,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentCategoryStepper(result, modifyItem, parent));
    }
  }

  openContentAstronetHoroszkopModal(modifyItem?: LayoutElementContentAstronetHoroszkop, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentAstronetHoroszkopComponent, !!modifyItem, 'Astronet horoszkóp blokk'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : true,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance.selectedType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentAstronetHoroszkop(result, modifyItem, parent));
    }
  }

  openDailyProgramModal(modifyItem?: LayoutElementContent, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentHeaderComponent, !!modifyItem, 'Napi program'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        ...this.getMobilorder(modifyItem),
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) =>
        this.addDailyProgram(
          {
            previewImage: ((): string => {
              switch (this.domainKey) {
                case 'origo':
                case 'mandiner':
                  return '/assets/images/layout-frames/origo/daily-program.jpg';
                case 'magyarNemzet':
                  return '/assets/images/layout-frames/mno/daily-program.jpg';
                case 'nso':
                  return '/assets/images/layout-frames/nso/daily-program.jpg';
              }
            })(),
            ...result,
          },
          modifyItem,
          parent
        )
      );
    }
  }

  openTeamsModal(modifyItem?: LayoutElementContent, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentTeamsComponent, !!modifyItem, 'Csapatok blokk'),
      nzData: {
        ...modifyItem,
        ...this.getMobilorder(modifyItem),
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance) || !componentInstance.selectedOption) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              ...componentInstance?.selectedOption,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) =>
        this.addTeams(
          {
            ...result,
            previewImage: ((): string => {
              switch (this.domainKey) {
                case 'mandiner':
                case 'origo':
                  return '/assets/images/layout-frames/origo/teams.jpg';
                case 'magyarNemzet':
                  return '/assets/images/layout-frames/mno/teams01.png';
                default:
                  return result?.previewImage;
              }
            })(),
          },
          modifyItem,
          parent
        )
      );
    }
  }

  openContentConferenceModal(modifyItem?: LayoutElementContent, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentConferenceComponent, !!modifyItem, 'Konferencia blokk'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        domain: this.domainKey,
        ...this.getMobilorder(modifyItem),
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) =>
        this.addContentConference(
          {
            previewImage: '/assets/images/layout-frames/vg/conference01.png',
            ...result,
          },
          modifyItem,
          parent
        )
      );
    }
  }

  openLeadEditorsModal(modifyItem?: LayoutElementContent, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentHeaderComponent, !!modifyItem, 'Vezető szerkesztők blokk'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) =>
        this.addLeadEditors(
          {
            previewImage: '/assets/images/layout-frames/nso/lead-editors.png',
            ...result,
          },
          modifyItem,
          parent
        )
      );
    }
  }

  openContentSorozatvetoModal(modifyItem?: LayoutElementContentSorozatveto, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentSorozatvetoComponent, !!modifyItem, 'Így írnak ők'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        styleId: modifyItem ? modifyItem.styleId : -1,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (componentInstance.selectedType?.styleId === undefined) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance.selectedType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentSorozatveto(result, modifyItem, parent));
    }
  }

  openContentWriteToUsModal(modifyItem?: LayoutElementContent, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentBaseComponent, !!modifyItem, 'Írjon nekünk'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addWriteToUs(result, modifyItem, parent));
    }
  }

  openContentExperienceGiftModal(modifyItem?: LayoutElementContent, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentExperienceGiftComponent, !!modifyItem, 'Élményt ajándékba'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentExperienceGift(result, modifyItem, parent));
    }
  }

  openContentGastroOccasionRecommenderModal(modifyItem?: LayoutElementContent, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentGastroOccasionRecommenderComponent, !!modifyItem, 'Nagyképes élmény ajánló'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        styleId: modifyItem?.['selectedType']?.styleId,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance.selectedType) return;

            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              selectedType: componentInstance.selectedType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentGastroOccasionRecommender(result, modifyItem, parent));
    }
  }

  openContentGastroExperienceRecommendationModal(
    modifyItem?: LayoutElementContentGastroExperienceRecommendation,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentGastroExperienceRecommendationComponent, !!modifyItem, 'Élmény ajánló'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        contentLength: modifyItem ? modifyItem.contentLength : 1,
        selectedIndex: modifyItem ? modifyItem.styleId : 0,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              contentLength: componentInstance.lengthControl?.value || componentInstance.contentLength,
              ...componentInstance.selectedStyle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentGastroExperienceRecommendation(result, modifyItem, parent));
    }
  }

  openContentThematicRecommenderModal(modifyItem?: LayoutElementContentGastroThematicRecommender, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentThematicRecommenderComponent, !!modifyItem, 'Élmény tematikus ajánló'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        selectedStyle: {
          styleId: modifyItem?.styleId || null,
          previewImage: null,
        },
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance.selectedStyle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentThematicRecommender(result, modifyItem, parent));
    }
  }

  openContentGastroExperienceOccasionSwiperModal(
    modifyItem?: LayoutElementContentGastroExperienceOccasionSwiper,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentGastroExperienceOccasionSwiperComponent, !!modifyItem, 'Lapozható élmény alkalom'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        selectedStyle: {
          styleId: modifyItem?.styleId || null,
          previewImage: null,
        },
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance.selectedStyle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentGastroExperienceOccasionSwiper(result, modifyItem, parent));
    }
  }

  openContentEventCalendarModal(modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn): void {
    if (modifyItem) {
      this.msg.info('Ennek az elemnek nincsenek szerkeszthető paraméterei.');
    }
    this.addContentEventCalendar(null, modifyItem, parent);
  }

  openContentTopStoriesModal(modifyItem?: LayoutElementContentTopStories, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentTopStoriesComponent, !!modifyItem, 'Top stories'),
      nzData: {
        domain: this.domainKey,
        selectedType: modifyItem
          ? {
              styleId: modifyItem['styleId'],
            }
          : undefined,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              ...componentInstance.selectedType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentTopStories(result, modifyItem, parent));
    }
  }

  openContentTextBoxModal(modifyItem?: LayoutElementContentTextBox, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentTextBoxComponent, !!modifyItem, this.domainKey === 'vilaggazdasag' ? 'VG tudástár' : 'szöveg'),
      nzData: {
        domain: this.domainKey,
        selectedStyle: { styleId: modifyItem?.styleId },
        withBlockTitle: modifyItem?.withBlockTitle,
        hideMobile: modifyItem?.hideMobile,
        ...this.getMobilorder(modifyItem),
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (
              !componentInstance ||
              componentInstance.selectedStyle?.styleId === undefined ||
              this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)
            ) {
              return;
            }

            modal.destroy({
              ...componentInstance.selectedStyle,
              withBlockTitle: componentInstance.withBlockTitle,
              hideMobile: componentInstance.hideMobile,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            } as LayoutEditorModalResult<BasicLayoutElementPreview>);
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentTextBox(result, modifyItem, parent));
    }
  }

  openContentTurpiCardModal(modifyItem?: LayoutElementContentTurpiCard, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentTurpiCardComponent, !!modifyItem, 'Turpi kártya'),
      nzData: {
        domain: this.domainKey,
        selectedStyle: { styleId: modifyItem?.styleId },
        withBlockTitle: modifyItem?.withBlockTitle,
        hideMobile: modifyItem?.hideMobile,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || componentInstance.selectedStyle?.styleId === undefined) {
              return;
            }

            modal.destroy({
              ...componentInstance.selectedStyle,
              withBlockTitle: componentInstance.withBlockTitle,
              hideMobile: componentInstance.hideMobile,
            } as LayoutEditorModalResult<BasicLayoutElementPreview>);
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentTurpiCard(result, modifyItem, parent));
    }
  }

  openContentRecipeSwiperModal(modifyItem?: LayoutElementContentRecipeSwiper, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentRecipeSwiperComponent, !!modifyItem, 'Lapozható recept'),
      nzData: {
        domain: this.domainKey,
        selectedStyle: { styleId: modifyItem?.styleId },
        withBlockTitle: modifyItem?.withBlockTitle,
        hideMobile: modifyItem?.hideMobile,
        contentLength: modifyItem?.contentLength || 1,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || componentInstance.selectedStyle?.styleId === undefined) {
              return;
            }

            modal.destroy({
              ...componentInstance.selectedStyle,
              withBlockTitle: componentInstance.withBlockTitle,
              hideMobile: componentInstance.hideMobile,
              contentLength: componentInstance.contentLength,
            } as LayoutEditorModalResult<BasicLayoutElementPreview>);
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentRecipeSwiper(result, modifyItem, parent));
    }
  }

  openContentIngredientModal(modifyItem?: LayoutElementContentIngredient, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentIngredientComponent, !!modifyItem, 'Hozzávaló'),
      nzData: {
        domain: this.domainKey,
        selectedStyle: { styleId: modifyItem?.styleId },
        withBlockTitle: modifyItem?.withBlockTitle,
        hideMobile: modifyItem?.hideMobile,
        contentLength: modifyItem?.contentLength || 1,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || componentInstance.selectedStyle?.styleId === undefined) {
              return;
            }

            modal.destroy({
              ...componentInstance.selectedStyle,
              withBlockTitle: componentInstance.withBlockTitle,
              hideMobile: componentInstance.hideMobile,
              contentLength: componentInstance.contentLength,
            } as LayoutEditorModalResult<BasicLayoutElementPreview>);
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentIngredient(result, modifyItem, parent));
    }
  }

  openContentGuaranteeBoxModal(modifyItem?: LayoutElementContentGuaranteeBox, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentGuaranteeBoxComponent, !!modifyItem, 'Garancia ajánló'),
      nzData: {
        domain: this.domainKey,
        selectedStyle: { styleId: modifyItem?.styleId },
        withBlockTitle: modifyItem?.withBlockTitle,
        hideMobile: modifyItem?.hideMobile,
        contentLength: modifyItem?.contentLength || 1,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || componentInstance.selectedStyle?.styleId === undefined) {
              return;
            }
            if (componentInstance.contentLength > 15) {
              this.sharedService.showNotification('error', 'Receptek száma maximum 15 lehet!');
              return;
            }

            modal.destroy({
              ...componentInstance.selectedStyle,
              withBlockTitle: componentInstance.withBlockTitle,
              hideMobile: componentInstance.hideMobile,
              contentLength: componentInstance.contentLength,
            } as LayoutEditorModalResult<BasicLayoutElementPreview>);
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentGuaranteeBox(result, modifyItem, parent));
    }
  }

  openContentRecipeCategorySelectModal(modifyItem?: LayoutElementContentRecipeCategorySelect, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentRecipeCategoriesSelectComponent, !!modifyItem, 'Recept kategória választó'),
      nzData: {
        domain: this.domainKey,
        selectedRecipeCategory: !modifyItem
          ? null
          : {
              styleId: modifyItem.styleId,
              previewImage: modifyItem.previewImage,
            },
        withBlockTitle: modifyItem?.withBlockTitle,
        hideMobile: modifyItem?.hideMobile,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.selectedRecipeCategory?.styleId) {
              return;
            }

            modal.destroy({
              ...componentInstance.selectedRecipeCategory,
              withBlockTitle: componentInstance.withBlockTitle,
              hideMobile: componentInstance.hideMobile,
            } as LayoutEditorModalResult<BasicLayoutElementPreview>);
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentRecipeCategorySelect(result, modifyItem, parent));
    }
  }

  openGalleryArticleListModal(modifyItem: LayoutElementContent, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentGalleryArticleListComponent, !!modifyItem, 'Galériás cikk lista'),
      nzData: {
        domain: this.domainKey,
        withBlockTitle: modifyItem?.withBlockTitle,
        hideMobile: modifyItem?.hideMobile,
        selectedType: modifyItem?.previewImage ? modifyItem : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.selectedType?.styleId) {
              this.sharedService.showNotification('error', 'Megjelenési formátum választása kötelező!');
              return;
            }
            modal.destroy({
              ...componentInstance.selectedType,
              withBlockTitle: componentInstance.withBlockTitle,
              hideMobile: componentInstance.hideMobile,
            } as LayoutEditorModalResult<BasicLayoutElementPreview>);
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentGalleryArticleList(result, modifyItem, parent));
    }
  }

  openWeeklyMenuModal(modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn): void {
    if (modifyItem) {
      this.msg.info('Ennek az elemnek nincsenek szerkeszthető paraméterei.');
    }
    this.sharedService.showNotification('success', 'Heti menü komponens sikeresen hozzáadva!');
    this.addContentWeeklyMenu(
      {
        previewImage: '/assets/images/layout-frames/mme/weekly-menu01.png',
      },
      modifyItem,
      parent
    );
  }

  openContentMediaPanelModal(modifyItem?: LayoutElementContentMediaPanel, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentMediaPanelComponent, !!modifyItem, 'Kiemelt doboz'),
      nzData: {
        contentLength: modifyItem ? modifyItem.contentLength : 1,
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        hideArticleType: modifyItem ? modifyItem.hideArticleType : false,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance: ModalContentMediaPanelComponent) => {
            if (!componentInstance) {
              return;
            }
            const contentLength = componentInstance.lengthControl.value;
            if (contentLength > componentInstance.contentLengthMax || contentLength < componentInstance.contentLengthMin) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              hideArticleType: componentInstance.hideArticleType,
              contentLength,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentMediaPanel(result, modifyItem, parent));
    }
  }

  openContentColumnBlockModal(modifyItem?: LayoutElementContentColumnBlock, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentColumnBlockComponent, !!modifyItem, 'Rovat blokk'),
      nzData: {
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        ...this.getMobilorder(modifyItem),
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              contentLength: 6,
              styleId: 1,
              configurable: true,
              previewImage: '/assets/images/layout-frames/bors/column-block.png',
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentColumnBlock(result, modifyItem, parent));
    }
  }

  openContentMostViewedModal(modifyItem?: LayoutElementContentMostViewed, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentMostViewedComponent, !!modifyItem, 'Legolvasottabbak'),
      nzData: {
        contentLength: modifyItem ? modifyItem.contentLength : null,
        domain: this.domainKey,
        selectedType: modifyItem || null,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        ...this.getMobilorder(modifyItem),
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              mobileOrder: componentInstance.mobilOrderControl?.value,
              contentLength: componentInstance.lengthControl.value,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance.selectedType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentMostViewed(result, modifyItem, parent));
    }
  }

  openSportRadioModal(modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn): void {
    if (modifyItem) {
      this.msg.info('Ennek az elemnek nincsenek szerkeszthető paraméterei.');
    }
    this.addSportRadioPlayer(
      {
        previewImage: '/assets/images/layout-frames/nso/sport-radio.png',
      },
      modifyItem,
      parent
    );
  }

  openTopRankingGlossaryModal(modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn): void {
    if (modifyItem) {
      this.msg.info('Ennek az elemnek nincsenek szerkeszthető paraméterei.');
    }
    this.addTopRankingGlossary(
      {
        previewImage: '/assets/images/layout-frames/mme/top-ranking-glossary.png',
      },
      modifyItem,
      parent
    );
  }

  openContentConfigurableSponsoredBoxModal(modifyItem?: LayoutElementContentConfigurableSponsoredBox, parent?: LayoutElementRow | LayoutElementColumn): void {
    if (modifyItem) {
      this.msg.info('Ennek az elemnek nincsenek szerkeszthető paraméterei.');
    }
    this.addConfigurableSponsoredBox(
      {
        previewImage: '/assets/images/layout-frames/shared/configurable-sponsored-box.png',
      },
      modifyItem,
      parent
    );
  }

  openVisitorCounterModal(modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn): void {
    if (modifyItem) {
      this.msg.info('Ennek az elemnek nincsenek szerkeszthető paraméterei.');
    }
    this.addVisitorCounter(
      {
        previewImage: '/assets/images/layout-frames/nso/audience-widget.png',
      },
      modifyItem,
      parent
    );
  }

  openContentHelloBudapestModal(modifyItem?: LayoutElementContentHelloBudapest, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentHelloBudapestComponent, !!modifyItem, 'Hello Budapest'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance.selectedHelloBudapestType) {
              return;
            }

            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance.selectedHelloBudapestType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentHelloBudapest(result, modifyItem, parent));
    }
  }

  openContentAstronetJoslasModal(modifyItem?: LayoutElementContentAstronetJoslas, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentAstronetJoslasComponent, !!modifyItem, 'Astronet jóslás blokk'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : true,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance.selectedType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentAstronetJoslas(result, modifyItem, parent));
    }
  }

  openContentFinalCountdown(modifyItem?: LayoutElementContentFinalCountdown, parent?: LayoutElementRow | LayoutElementColumn) {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentFinalCountdownComponent, !!modifyItem, 'Visszaszámláló'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance.selectedCountdownType) {
              return;
            }

            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance.selectedCountdownType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentFinalCountdown(result, modifyItem, parent));
    }
  }

  openContentDossierListModal(modifyItem?: LayoutElementContentDossierList, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentDossierListComponent, !!modifyItem, 'Dosszié lista'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        selectedDossierList: modifyItem ? modifyItem : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              ...componentInstance.selectedDossierList,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => {
        this.addContentDossierList(result, modifyItem, parent);
      });
    }
  }

  openContentBroadcastRecommenderModal(modifyItem?: LayoutElementContentBroadcastRecommender, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentBroadcastRecommenderComponent, !!modifyItem, 'Adás ajánló'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              ...componentInstance.selectedOption,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentBroadcastRecommender(result, modifyItem, parent));
    }
  }

  openContentSongTopListModal(modifyItem?: LayoutElementContentSongTopList, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentSongTopListComponent, !!modifyItem, 'Zene toplista'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              ...componentInstance.selectedOption,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentSongTopList(result, modifyItem, parent));
    }
  }

  openContentPdfBoxModal(modifyItem?: LayoutElementContentPdfBox, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentPdfBoxComponent, !!modifyItem, 'Pdf box'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        styleId: modifyItem ? modifyItem.styleId : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || !componentInstance.selectedPdfBoxType) {
              return;
            }
            modal.destroy({
              contentLength: 1,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance.selectedPdfBoxType,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentPdfBox(result, modifyItem, parent));
    }
  }

  openContentRssBoxModal(modifyItem?: LayoutElementContentRssBox, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentRssBoxComponent, !!modifyItem, 'RSS doboz'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...modifyItem,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            const isValid = componentInstance.form.valid;
            if (!isValid) {
              componentInstance.validate();
              return;
            }
            modal.destroy({
              previewImage: '/assets/images/layout-frames/mno/brand-mindmegette.png',
              ...componentInstance.form.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentRssBox(result, modifyItem, parent));
    }
  }

  openContentTenyekBox(modifyItem?: LayoutElementContentTenyekBox, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentTenyekBoxComponent, !!modifyItem, 'Tények ajánló doboz'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });
    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentTenyekBox(result, modifyItem, parent));
    }
  }

  openContentUpcomingMatchesModal(modifyItem?: LayoutElementContentUpcomingMatches, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentUpcomingMatchesComponent, !!modifyItem, 'Közelgő mérkőzések'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        contentLength: modifyItem ? modifyItem.contentLength : 1,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance) {
              return;
            }
            modal.destroy({
              firstPreviewImage: '/assets/images/layout-frames/nso/upcoming-matches-header.png',
              previewImage: '/assets/images/layout-frames/nso/upcoming-matches02.png',
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              contentLength: componentInstance.lengthControl.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentUpcomingMatches(result, modifyItem, parent));
    }
  }

  openContentChampionshipTableModal(modifyItem?: LayoutElementContentChampionshipTable, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentChampionshipTableComponent, !!modifyItem, 'Bajnokság tabella'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance) {
              return;
            }
            modal.destroy({
              previewImage: '/assets/images/layout-frames/nso/championship-table01.png',
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentChampionshipTable(result, modifyItem, parent));
    }
  }

  openContentTripBoxModal(modifyItem?: LayoutElementContentTripBox, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentTripBoxComponent, !!modifyItem, 'Forduló box'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              previewImage: componentInstance.selectedTripBoxType.previewImage,
              matchesNumber: componentInstance.selectedTripBoxType.matchesNumber,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => {
        this.addContentTripBox(
          {
            previewImage: result.previewImage,
            ...result,
          },
          modifyItem,
          parent
        );
      });
    }
  }

  openContentGPNewsBoxModal(modifyItem?: LayoutElementContentGPNewsBox, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentGpNewsBoxComponent, !!modifyItem, 'GP Hírek doboz'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            const previewImages = new Array(componentInstance.lengthControl.value - 1);
            previewImages.fill({ previewImage: componentInstance.selectedGPNewsBoxType.previewImage });
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              previewImage: componentInstance.selectedGPNewsBoxType.previewImage,
              previewImages: !componentInstance.selectedGPNewsBoxType.turnOffPreviewImageGenerateByContentLength ? previewImages : null,
              firstPreviewImage: componentInstance.selectedGPNewsBoxType.firstPreviewImage,
              contentLength: componentInstance.lengthControl.value,
              styleId: componentInstance.selectedGPNewsBoxType.styleId,
              turnOffPreviewImageGenerateByContentLength: componentInstance.selectedGPNewsBoxType.turnOffPreviewImageGenerateByContentLength,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => {
        this.addContentGPNewsBox(
          {
            previewImage: result.previewImage,
            ...result,
          },
          modifyItem,
          parent
        );
      });
    }
  }

  openContentEbNewsModal(modifyItem?: LayoutElementContentEBNews, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentEbNewsBoxComponent, !!modifyItem, 'EB Hírek doboz'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        ...this.getMobilorder(modifyItem),
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              previewImage: componentInstance.selectedEBNewsBoxType.previewImage,
              contentLength: componentInstance.lengthControl.value,
              styleId: componentInstance.selectedEBNewsBoxType.styleId,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => {
        this.addContentEBNewsBox(
          {
            previewImage: result.previewImage,
            ...result,
          },
          modifyItem,
          parent
        );
      });
    }
  }

  openContentBrandingBoxArticleModal(modifyItem?: LayoutElementContentBrandingBoxArticle, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentBrandingBoxArticleComponent, !!modifyItem, 'Branding Box cikk lista'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            const previewImages = new Array(componentInstance.lengthControl.value - 1);
            previewImages.fill({ previewImage: componentInstance.selectedBrandingBoxArticleType.previewImage });
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              previewImage: componentInstance.selectedBrandingBoxArticleType.previewImage,
              contentLength: componentInstance.lengthControl.value,
              styleId: componentInstance.selectedBrandingBoxArticleType.styleId,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => {
        this.addContentBrandingBoxArticle(
          {
            previewImage: result.previewImage,
            ...result,
          },
          modifyItem,
          parent
        );
      });
    }
  }

  openContentLiveBarBox(modifyItem?: LayoutElementContentLiveBar, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentLiveBarComponent, !!modifyItem, 'Élő közvetítés'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              firstPreviewImage: '/assets/images/layout-frames/origo/livebar01.png',
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentLiveBar(result, modifyItem, parent));
    }
  }

  openContentMoreArticlesModal(modifyItem?: LayoutElementContentMoreArticles, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentMoreArticlesComponent, !!modifyItem, 'További cikkeink link'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        contentLength: modifyItem ? modifyItem.contentLength : null,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              styleId: componentInstance.selectedMoreArticlesType.styleId,
              previewImage: componentInstance.selectedMoreArticlesType.previewImage,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => {
        this.addContentMoreArticles(
          {
            previewImage: result.previewImage,
            ...result,
          },
          modifyItem,
          parent
        );
      });
    }
  }

  openContentRelatedArticlesModal(modifyItem?: LayoutElementContentRelatedArticles, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentRelatedArticlesComponent, !!modifyItem, 'Kapcsolódó cikkek'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        styleId: modifyItem ? modifyItem.styleId : null,
        domain: this.domainKey,
        contentLength: modifyItem ? modifyItem.contentLength : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              ...componentInstance.selectedRelatedArticlesType,
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              contentLength: 1,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => {
        this.addContentRelatedArticles(result, modifyItem, parent);
      });
    }
  }

  openContentSportBlockModal(modifyItem?: LayoutElementContentSportBlock, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentSportBlockComponent, !!modifyItem, 'Sport blokk'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              previewImage: componentInstance.selectedSportBlockType.previewImage,
              contentLength: componentInstance.lengthControl.value,
              styleId: componentInstance.selectedSportBlockType.styleId,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => {
        this.addContentSportBlock(
          {
            previewImage: result.previewImage,
            ...result,
          },
          modifyItem,
          parent
        );
      });
    }
  }

  openContentRecipeCardModal(modifyItem?: LayoutElementContentRecipe, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentRecipeCardComponent, !!modifyItem, 'Recept kártya'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        styleId: modifyItem ? modifyItem.styleId : null,
        hasBackground: modifyItem ? modifyItem.hasBackground : false,
        contentLength: modifyItem ? modifyItem.contentLength : 1,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance: ModalContentRecipeCardComponent) => {
            if (!componentInstance && !componentInstance.styleId) {
              return;
            }
            modal.destroy({
              styleId: componentInstance.styleId,
              hasBackground: componentInstance.hasBackground,
              previewImage: componentInstance.selectedOption.previewImage,
              contentLength: componentInstance.lengthControl.value,
              hasImg: componentInstance.selectedOption.hasImg,
              hasTitle: componentInstance.selectedOption.hasTitle,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentRecipeCard(result, modifyItem, parent));
    }
  }

  openContentArticleBlockModal(modifyItem?: LayoutElementContentArticleBlock, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentArticleBlockComponent, !!modifyItem, 'Cikk blokk'),
      nzData: {
        domain: this.domainKey,
        selectedStyle: { styleId: modifyItem?.styleId },
        withBlockTitle: modifyItem?.withBlockTitle,
        hideMobile: modifyItem?.hideMobile,
        contentLength: this.domainKey === 'magyarNemzet' ? 3 : 10,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || componentInstance.selectedStyle?.styleId === undefined) {
              return;
            }

            modal.destroy({
              ...componentInstance.selectedStyle,
              withBlockTitle: componentInstance.withBlockTitle,
              hideMobile: componentInstance.hideMobile,
              contentLength: componentInstance.contentLength,
            } as LayoutEditorModalResult<BasicLayoutElementPreview>);
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentArticleBlock(result, modifyItem, parent));
    }
  }

  openContentTelekomVivicittaModal(modifyItem?: LayoutElementContentTelekomVivicitta, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentTelekomVivicittaComponent, !!modifyItem, 'Telekom Vivicittá'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        styleId: modifyItem ? modifyItem.styleId : 1,
        domain: this.domainKey,
        title: modifyItem ? modifyItem.title : 'Telekom Vivicittá',
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              styleId: componentInstance.styleId,
              previewImage: componentInstance.selectedOption.previewImage,
              title: componentInstance.title,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentTelekomVivicitta(result, modifyItem, parent));
    }
  }

  openContentCountdownBoxModal(modifyItem?: LayoutElementContentCountdownBox, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentCountdownBoxComponent, !!modifyItem, 'Visszaszámláló doboz'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        styleId: modifyItem ? modifyItem.styleId : 1,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              styleId: componentInstance.styleId,
              previewImage: componentInstance.selectedOption.previewImage,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentCountdownBox(result, modifyItem, parent));
    }
  }

  openContentElectionsBoxModal(modifyItem?: LayoutElementContentElectionsBox, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentElectionsBoxComponent, !!modifyItem, 'Választások 2024 doboz'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        styleId: modifyItem ? modifyItem.styleId : 0,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              styleId: componentInstance.styleId,
              previewImage: componentInstance.selectedOption.previewImage,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentElectionsBox(result, modifyItem, parent));
    }
  }

  openContentEbCountdownBlockTitleModal(modifyItem?: LayoutElementContentEbCountdownBlockTitle, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentEbCountdownBlockTitleComponent, !!modifyItem, 'Foci EB 2024 - Visszaszámláló blokk cím'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        styleId: modifyItem ? modifyItem.styleId : 0,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              styleId: componentInstance.styleId,
              previewImage: componentInstance.selectedOption.previewImage,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentEbCountdownBlockTitle(result, modifyItem, parent));
    }
  }

  openContentEbSingleEliminationModal(modifyItem?: LayoutElementContent, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentEbSingleEliminationComponent, !!modifyItem, 'Foci EB 2024 - Egyenes kiesés'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        ...modifyItem,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              styleId: componentInstance.styleId,
              previewImage: componentInstance.selectedOption.previewImage,
              mobileOrder: componentInstance.mobilOrderControl?.value,
              title: componentInstance.title,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentEbSingleElimination(result, modifyItem, parent));
    }
  }

  openContentOlimpiaCountdownBlockTitleModal(
    modifyItem?: LayoutElementContentOlimpiaCountdownBlockTitle,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentOlimpiaCountdownBlockTitleComponent, !!modifyItem, 'Olimpia - Visszaszámláló blokk cím'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        styleId: modifyItem ? modifyItem.styleId : 0,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              styleId: componentInstance.styleId,
              previewImage: componentInstance.selectedOption.previewImage,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentOlimpiaCountdownBlockTitle(result, modifyItem, parent));
    }
  }

  openContentOlimpiaNewsModal(modifyItem?: LayoutElementContentOlimpiaNews, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentOlimpiaNewsBoxComponent, !!modifyItem, 'Olimpia - Hírek'),
      nzData: {
        ...this.getMobilorder(modifyItem),
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        contentLength: modifyItem ? modifyItem.contentLength : null,
        styleId: modifyItem ? modifyItem.styleId : null,
        ...this.getMobilorder(modifyItem),
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              previewImage: componentInstance.selectedOlimpiaNewsBoxType.previewImage,
              contentLength: componentInstance.lengthControl.value,
              styleId: componentInstance.selectedOlimpiaNewsBoxType.styleId,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => {
        this.addContentOlimpiaNewsBox(
          {
            previewImage: result.previewImage,
            ...result,
          },
          modifyItem,
          parent
        );
      });
    }
  }

  openContentOlimpiaHungarianTeamModal(modifyItem?: LayoutElementContentOlimpiaHungarianTeam, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentOlimpiaHungarianTeamComponent, !!modifyItem, 'Olimpia - Magyar csapat'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        styleId: modifyItem ? modifyItem.styleId : 0,
        withBlockTitle: modifyItem?.withBlockTitle ?? false,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              styleId: componentInstance.styleId,
              previewImage: componentInstance.selectedOption.previewImage,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentOlimpiaHungarianTeam(result, modifyItem, parent));
    }
  }

  openContentOlimpiaResultsBlockModal(modifyItem?: LayoutElementContentOlimpiaResultsBlock, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentOlimpiaResultsBlockComponent, !!modifyItem, 'Olimpia - Eredmények'),
      nzData: {
        ...this.getCommonNzComponentParams(modifyItem),
        ...this.getMobilorder(modifyItem),
        styleId: modifyItem ? modifyItem.styleId : 0,
        domain: this.domainKey,
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance) => {
            if (!componentInstance || this.mobileOrderValueAlreadyExists(modifyItem, componentInstance)) {
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              styleId: componentInstance.styleId,
              previewImage: componentInstance.selectedOption.previewImage,
              mobileOrder: componentInstance.mobilOrderControl?.value,
            });
          },
          type: 'primary',
        },
      ],
    });

    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentOlimpiaResultsBlock(result, modifyItem, parent));
    }
  }

  openTopCommentedArticles(modifyItem?: LayoutEditorModalResultMulti, parent?: LayoutElementRow | LayoutElementColumn): void {
    const modal = this.modal.create({
      ...this.getCommonModalParams(ModalContentTopCommentedArticlesComponent, !!modifyItem, 'Legtöbbet Kommentelt'),
      nzData: {
        hideMobile: modifyItem ? modifyItem.hideMobile : false,
        withBlockTitle: modifyItem ? modifyItem.withBlockTitle : false,
        domain: this.domainKey,
        selectedStyle: { styleId: modifyItem?.styleId },
      },
      nzFooter: [
        {
          label: modifyItem ? 'Módosítás' : 'Hozzáadás',
          onClick: (componentInstance): void => {
            if (!componentInstance || !componentInstance.selectedStyle?.styleId) {
              this.sharedService.showNotification('error', 'Megjelenési formátum választása kötelező!');
              return;
            }
            modal.destroy({
              hideMobile: componentInstance.hideMobile,
              withBlockTitle: componentInstance.withBlockTitle,
              ...componentInstance?.selectedStyle,
            });
          },
          type: 'primary',
        },
      ],
    });
    if (this.hasContentChange(modifyItem) && this.canAddContent(!!modifyItem)) {
      modal.afterClose.pipe(filter((result) => !!result)).subscribe((result) => this.addContentTopCommentedArticles(result, modifyItem, parent));
    }
  }

  // ------- [ handlers ] ------- //

  addRow(
    cols?: number[],
    title?: boolean,
    hideMobile?: boolean,
    backgroundColor?: string,
    hasLine?: boolean,
    isMobileSideBySide?: boolean,
    modifyItem?: LayoutElementRow,
    parentItem?: any
  ) {
    const initCols: LayoutElementColumn[] = [];
    const parentWidthDesktopRecursive = this.activeElement?.widthDesktopRecursive || 12;
    if (cols) {
      cols.forEach((colWidth) => {
        initCols.push({
          id: this.generateId(),
          type: LayoutElementType.COLUMN,
          elements: [],
          widthDesktop: colWidth,
          widthDesktopRecursive: (parentWidthDesktopRecursive * colWidth) / 12,
          withBlockTitle: false,
          hideMobile: false,
          blockTitle: null,
        });
      });
    }

    const newRowElement: LayoutElementRow = {
      id: this.generateId(),
      type: LayoutElementType.ROW,
      widthDesktop: 12,
      widthDesktopRecursive: parentWidthDesktopRecursive,
      elements:
        modifyItem &&
        this.areColumnWidthsEqual(
          modifyItem.elements.filter((el) => el.type === LayoutElementType.COLUMN).map((c) => (c as LayoutElementColumn).widthDesktop),
          initCols.map((c) => c.widthDesktop)
        )
          ? modifyItem.elements
          : initCols,
      withBlockTitle: title,
      hideMobile: hideMobile,
      backgroundColor: backgroundColor,
      hasLine: hasLine,
      blockTitle: modifyItem?.blockTitle ? modifyItem.blockTitle : null,
      isMobileSideBySide,
    };
    if (modifyItem) {
      const arrayToModify = parentItem === null ? this.rootElements : parentItem.elements;
      const elemIndex = arrayToModify.indexOf(modifyItem);
      arrayToModify[elemIndex] = newRowElement;
    } else {
      //If we drag an element to a specific index, add the row and short circuit.
      if (this.newItemInsertIndex >= 0) {
        if (this.activeElement?.elements?.length >= this.newItemInsertIndex) {
          this.activeElement.elements.splice(this.newItemInsertIndex, 0, newRowElement);
        } else {
          this.rootElements.splice(this.newItemInsertIndex, 0, newRowElement);
        }
        this.newItemInsertIndex = -1;
        return;
      }
      if (!this.activeElement || this.activeElement.type !== LayoutElementType.COLUMN) {
        let pushNewItemLast = true;
        if (this.activeElement && this.activeElement.type === LayoutElementType.ROW) {
          //If we selected a row, then add the new row after the selected one.
          //Get the parent element's elements array and insert the new element.
          const parentArray = this.findLayoutElementParentArray(this.activeElement, this.rootElements);
          if (parentArray) {
            const addAfterIndex = parentArray.indexOf(this.activeElement);
            parentArray.splice(addAfterIndex + 1, 0, newRowElement);
            pushNewItemLast = false;
          }
        }
        if (pushNewItemLast) {
          //If we want to add to the bottom.
          this.rootElements.push(newRowElement);
        }
      } else {
        //If we selected a column, then put the new row inside the selected column.
        (this.activeElement as LayoutElementColumn).elements.push(newRowElement);
      }
    }
  }

  addColumn(
    width: number,
    title: boolean,
    hideMobile: boolean,
    hasLeftMargin: boolean,
    hasRightMargin,
    marginBorderColor,
    mobileOrder?: number,
    modifyItem?: LayoutElementColumn,
    parent?: LayoutElementRow
  ) {
    if ((!this.activeElement || this.activeElement.type !== LayoutElementType.ROW) && !modifyItem) {
      return;
    }

    const parentWidthDesktopRecursive = parent?.widthDesktopRecursive || this.activeElement?.widthDesktopRecursive || 12;
    const newColumnElement: LayoutElementColumn = {
      id: this.generateId(),
      type: LayoutElementType.COLUMN,
      elements: [],
      widthDesktop: width,
      widthDesktopRecursive: (parentWidthDesktopRecursive * width) / 12,
      withBlockTitle: title,
      hideMobile,
      hasLeftMargin,
      hasRightMargin,
      marginBorderColor,
      mobileOrder,
      blockTitle: null,
    };
    if (modifyItem) {
      if (modifyItem.elements) {
        this.recalculateWidthDesktopRecursive(modifyItem.elements, newColumnElement.widthDesktopRecursive);
        newColumnElement.elements = modifyItem.elements;
      }
      const elemIndex = parent.elements.indexOf(modifyItem);
      parent.elements[elemIndex] = newColumnElement;

      if (newColumnElement.withBlockTitle && modifyItem.blockTitle) {
        newColumnElement.blockTitle = { ...modifyItem.blockTitle };
      }
    } else {
      (this.activeElement as LayoutElementRow).elements.push(newColumnElement);
    }
  }

  addContentFastNews(
    modalResult: LayoutEditorModalResult<LayoutElementContentFastNews>,
    modifyItem?: LayoutElementContentFastNews,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentFastNews = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      contentType: LayoutElementContentType.FAST_NEWS,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.FAST_NEWS);
  }

  addContentMinuteToMinutes(
    modalResult: LayoutEditorModalResult<LayoutElementContentMinuteToMinutes>,
    modifyItem?: LayoutElementContentMinuteToMinutes,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentMinuteToMinutes = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      contentType: LayoutElementContentType.MINUTE_TO_MINUTE,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentMapRecommendations(
    modalResult: LayoutEditorModalResult<LayoutElementContentMapRecommendations>,
    modifyItem?: LayoutElementContentMapRecommendations,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentMapRecommendations = {
      ...this.getCommonContentElementData(modifyItem, false, null),
      ...modalResult,
      contentType: LayoutElementContentType.MAP_RECOMMENDATIONS,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentHero(
    modalResult: LayoutEditorModalResult<LayoutElementContentHero>,
    modifyItem?: LayoutElementContentHero,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentHero = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      ...modalResult,
      contentType: LayoutElementContentType.HERO,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentMedicalMeteorology(
    modalResult: LayoutEditorModalResult<LayoutElementContentMedicalMeteorology>,
    modifyItem?: LayoutElementContentMedicalMeteorology,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentMedicalMeteorology = {
      ...this.getCommonContentElementData(modifyItem, false, null),
      ...modalResult,
      contentType: LayoutElementContentType.MEDICAL_METEOROLOGY,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentTwelveDaysForecast(
    modalResult: LayoutEditorModalResult<LayoutElementContentTwelveDaysForecast>,
    modifyItem?: LayoutElementContentTwelveDaysForecast,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentTwelveDaysForecast = {
      ...this.getCommonContentElementData(modifyItem, false),
      ...modalResult,
      contentType: LayoutElementContentType.TWELVE_DAYS_FORECAST,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentDetections(
    modalResult: LayoutEditorModalResult<LayoutElementContentDetections>,
    modifyItem?: LayoutElementContentDetections,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentDetections = {
      ...this.getCommonContentElementData(modifyItem, true),
      ...modalResult,
      contentType: LayoutElementContentType.DETECTIONS,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.DETECTIONS);
  }

  addContentWeeklyNewspaper(
    modalResult: LayoutEditorModalResult<LayoutElementContentWeeklyNewspaper>,
    modifyItem?: LayoutElementContentWeeklyNewspaper,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentWeeklyNewspaper = {
      ...this.getCommonContentElementData(modifyItem, true),
      ...modalResult,
      contentType: LayoutElementContentType.WEEKLY_NEWSPAPER_BOX,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentQuiz(
    modalResult: LayoutEditorModalResult<LayoutElementContentQuiz>,
    modifyItem?: LayoutElementContentQuiz,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentQuiz = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      contentType: LayoutElementContentType.QUIZ,
      ...modalResult,
    };
    this.updateParentElement(modifyItem, newElement, parent);
  }
  addContentSponsoredQuiz(
    modalResult: LayoutEditorModalResult<LayoutElementContentSponsoredQuiz>,
    modifyItem?: LayoutElementContentSponsoredQuiz,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentSponsoredQuiz = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      contentType: LayoutElementContentType.SPONSORED_QUIZ,
      ...modalResult,
    };
    this.updateParentElement(modifyItem, newElement, parent);
  }

  addContentArticleWithVideo(
    modalResult: LayoutEditorModalResultMulti<LayoutElementContentArticlesWithVideoContent>,
    modifyItem?: LayoutElementContentArticlesWithVideoContent,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentArticlesWithVideoContent = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentArticleWithPodcast(
    modalResult: LayoutEditorModalResultMulti<LayoutElementContentArticlesWithPodcastContent>,
    modifyItem?: LayoutElementContentArticlesWithPodcastContent,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentArticlesWithPodcastContent = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.ARTICLES_WITH_PODCAST_CONTENT,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentOlimpiaArticleWithPodcast(
    modalResult: LayoutEditorModalResultMulti<LayoutElementContentOlimpiaArticlesWithPodcastContent>,
    modifyItem?: LayoutElementContentOlimpiaArticlesWithPodcastContent,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentOlimpiaArticlesWithPodcastContent = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.OLIMPIA_ARTICLES_WITH_PODCAST_CONTENT,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentPodcastArticleList(
    modalResult: LayoutEditorModalResultItems<LayoutElementContentArticle>,
    modifyItem?: LayoutElementContentArticle,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentArticle = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.PODCAST_ARTICLE_LIST,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentSpotlight(
    modalResult: LayoutEditorModalResultMulti<LayoutElementContentSpotlight>,
    modifyItem?: LayoutElementContentSpotlight,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentSpotlight = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.SPOTLIGHT,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentTurpiBox(
    modalResult: LayoutEditorModalResultMulti<LayoutElementContentTurpiBox>,
    modifyItem?: LayoutElementContentTurpiBox,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentTurpiBox = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.TURPI_BOX,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentDailyMenu(
    modalResult: LayoutEditorModalResultMulti<LayoutElementContentDailyMenu>,
    modifyItem?: LayoutElementContentDailyMenu,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentDailyMenu = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.DAILY_MENU,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentOfferBox(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContentOfferBox,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentOfferBox = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.OFFER_BOX,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentMaestroBox(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContentMaestroBox,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentMaestroBox = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.MAESTRO_BOX,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentExperienceGift(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContent,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContent = {
      ...this.getCommonContentElementData(modifyItem, false),
      previewImage: `/assets/images/layout-frames/mme/gasztro/experience-gift.png`,
      contentType: LayoutElementContentType.EXPERIENCE_GIFT,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentGastroOccasionRecommender(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContent,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContent = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.GASTRO_OCCASION_RECOMMENDER,
      previewImage: modalResult?.['selectedType'].previewImage,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentGastroExperienceRecommendation(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContent,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContent = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.GASTRO_EXPERIENCE_RECOMMENDATION,
      configurable: true,
      previewImage: modalResult?.previewImage,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentThematicRecommender(
    modalResult: LayoutEditorModalResult<LayoutElementContentGastroThematicRecommender>,
    modifyItem?: LayoutElementContentGastroThematicRecommender,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentGastroThematicRecommender = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.GASTRO_THEMATIC_RECOMMENDER,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentGastroExperienceOccasionSwiper(
    modalResult: LayoutEditorModalResult<LayoutElementContentGastroExperienceOccasionSwiper>,
    modifyItem?: LayoutElementContentGastroExperienceOccasionSwiper,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentGastroExperienceOccasionSwiper = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION_SWIPER,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentEventCalendar(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContent,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContent = {
      ...this.getCommonContentElementData(modifyItem, false),
      previewImage: `/assets/images/layout-frames/mme/gasztro/experience-calendar.png`,
      contentType: LayoutElementContentType.EVENT_CALENDAR,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentPublicAuthors(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContentPublicAuthors,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentPublicAuthors = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.PUBLIC_AUTHORS,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentSelection(
    modalResult: LayoutEditorModalResult<LayoutElementSelection>,
    modifyItem?: LayoutElementContentSelection,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentSelection = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.SELECTION,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.SELECTION);
  }

  addContentHighlightedSelection(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContentHighlightedSelection,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentHighlightedSelection = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.HIGHLIGHTED_SELECTION,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentArticleSlider(
    modalResult: LayoutEditorModalResult<LayoutElementContentArticleSlider>,
    modifyItem?: LayoutElementContentArticleSlider,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentArticleSlider = {
      contentType: LayoutElementContentType.ARTICLE_SLIDER,
      configurable: true,
      ...this.getCommonContentElementData(modifyItem, true),
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentDrawnMapList(
    modalResult: LayoutEditorModalResult<LayoutElementContentDrawnMapList>,
    modifyItem?: LayoutElementContentDrawnMapList,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentDrawnMapList = {
      ...this.getCommonContentElementData(modifyItem, false),
      contentType: LayoutElementContentType.DRAWN_MAP_LIST,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentImageMapList(
    modalResult: LayoutEditorModalResult<LayoutElementContentImageMapList>,
    modifyItem?: LayoutElementContentImageMapList,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentImageMapList = {
      ...this.getCommonContentElementData(modifyItem, false),
      contentType: LayoutElementContentType.IMAGE_MAP_LIST,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentArticle(
    modalResult: LayoutEditorModalResultMulti<LayoutElementArticlePreview>,
    modifyItem?: LayoutElementContentArticle,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentArticle = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      contentType: LayoutElementContentType.ARTICLE,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentDataBank(
    modalResult: LayoutEditorModalResultMulti<LayoutElementArticlePreview>,
    modifyItem?: LayoutElementContentDataBank,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentDataBank = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      contentType: LayoutElementContentType.DATA_BANK,
      ...modalResult,
      secondaryContentType: LayoutElementContentType.DATA_BANK,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.DATA_BANK);
  }

  addContentKulturnemzet(
    modalResult: LayoutEditorModalResult<LayoutElementArticlePreview>,
    modifyItem?: LayoutElementContentArticle,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.KULTUR_NEMZET,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement as LayoutElementContentArticle, parent);
  }

  addContentUpcomingMatches(
    modalResult: LayoutEditorModalResult<LayoutElementContentUpcomingMatches>,
    modifyItem?: LayoutElementContentUpcomingMatches,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentUpcomingMatches = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      previewImage: '/assets/images/layout-frames/nso/upcoming-matches02.png',
      contentType: LayoutElementContentType.UPCOMING_MATCHES,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentChampionshipTable(
    modalResult: LayoutEditorModalResult<LayoutElementContentChampionshipTable>,
    modifyItem?: LayoutElementContentChampionshipTable,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentChampionshipTable = {
      ...this.getCommonContentElementData(modifyItem, true),
      previewImage: '/assets/images/layout-frames/nso/championship-table01.png',
      contentType: LayoutElementContentType.CHAMPIONSHIP_TABLE,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentHelloBudapest(
    modalResult: LayoutEditorModalResult<LayoutElementArticlePreview>,
    modifyItem?: LayoutElementContentHelloBudapest,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentHelloBudapest = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.HELLO_BUDAPEST,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.IMAGE);
  }

  addContentPrBlock(
    modalResult: LayoutEditorModalResult<LayoutElementArticlePreview>,
    modifyItem?: LayoutElementContentPrBlock,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentPrBlock = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      contentType: LayoutElementContentType.PR_BLOCK,
      ...modalResult,
    };
    this.updateParentElement(modifyItem, newElement, parent);
  }

  addContentAd(
    modalResult: {
      medium: string;
      bannerName: string;
      withBlockTitle: boolean;
    },
    modifyItem?: LayoutElementContentAd,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentAd = {
      configurable: false,
      ...this.getCommonContentElementData(modifyItem),
      previewImage: null, // <app-advertisement-placeholder>,
      contentType: LayoutElementContentType.AD,
      hideMobile: false,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentPodcastList(
    modalResult: LayoutEditorModalResult<LayoutElementContentPodcastList>,
    modifyItem?: LayoutElementContentPodcastList,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentPodcastList = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      contentType: LayoutElementContentType.PODCAST_LIST,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.PODCASTS);
  }

  addContentDossierRepeater(
    modalResult: LayoutEditorModalResultItems,
    modifyItem?: LayoutElementContentDossierRepeater,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement: LayoutElementContentDossierRepeater = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.DOSSIER_REPEATER,
      ...modalResult,
    } as LayoutElementContentDossierRepeater;
    this.updateParentElement(modifyItem, newElement, parent);
  }

  addContentVideoBlock(
    modalResult: LayoutEditorModalResultItems,
    modifyItem?: LayoutElementContentVideoBlock,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    let previewImage = '/assets/images/layout-frames/vg/video-block01.png';
    if (this.domainService.isCurrent('ripost')) {
      previewImage = '/assets/images/layout-frames/ripost/video-block01.png';
    }
    if (this.domainService.isCurrent('nso')) {
      previewImage = '/assets/images/layout-frames/nso/video-block01.png';
    }
    const newElement: LayoutElementContentVideoBlock = {
      ...this.getCommonContentElementData(modifyItem, true),
      previewImage,
      contentType: LayoutElementContentType.VIDEO_BLOCK,
      ...modalResult,
    };
    this.updateParentElement(modifyItem, newElement, parent);
  }

  addContentAstrologyBlock(
    modalResult: LayoutEditorModalResultItems<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContentAstrologyBlock,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentAstrologyBlock = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.ASTROLOGY,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentTabsBlock(
    modalResult: LayoutEditorModalResult<{ tabs: LayoutContentTabConfig[] }>,
    modifyItem?: LayoutElementContentTabsBlock,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentTabsBlock = {
      ...this.getCommonContentElementData(modifyItem, true),
      previewImage: '/assets/images/layout-frames/megyeilapok/tabs01.png',
      contentType: LayoutElementContentType.TABS,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.TABS);
  }

  addContentWysiwyg(modalResult: LayoutEditorModalResult, modifyItem?: LayoutElementContentWysiwyg, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: LayoutElementContentWysiwyg = {
      ...this.getCommonContentElementData(modifyItem, true),
      previewImage: '/assets/images/layout-frames/megyeilapok/wysiwyg.png',
      contentType: LayoutElementContentType.WYSIWYG,
      ...modalResult,
    };
    this.updateParentElement(modifyItem, newElement, parent);
  }

  addContentImageBlock(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview & { cancelMargin: boolean }>,
    modifyItem?: LayoutElementContentImage,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentImage = {
      ...this.getCommonContentElementData(modifyItem, true),
      previewImage: '/assets/images/layout-frames/megyeilapok/image-placeholder.png',
      contentType: LayoutElementContentType.IMAGE,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.IMAGE);
  }

  addContentBreakingBlock(
    modalResult: LayoutEditorModalResult<LayoutElementArticlePreview>,
    modifyItem?: LayoutElementContentBreakingBlock,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentBreakingBlock = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.BREAKING,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.BREAKING);
  }

  addContentMostViewed(
    modalResult: LayoutEditorModalResult<LayoutElementArticlePreview>,
    modifyItem?: LayoutElementContentMostViewed,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentMostViewed = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.MOST_VIEWED,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentTopStories(
    modalResult: LayoutEditorModalResult<LayoutElementArticlePreview>,
    modifyItem?: LayoutElementContentTopStories,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentTopStories = {
      ...this.getCommonContentElementData(modifyItem, false),
      contentType: LayoutElementContentType.TOP_STORIES,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentTextBox(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContentTextBox,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentTextBox = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.TEXT_BOX,
      contentLength: 1,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentTurpiCard(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContentTurpiCard,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentTurpiCard = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.TURPI_CARD,
      contentLength: 1,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentRecipeSwiper(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContentRecipeSwiper,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentRecipeSwiper = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.RECIPE_SWIPER,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.RECIPE);
  }

  addContentIngredient(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContentIngredient,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentIngredient = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.INGREDIENT,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.INGREDIENT);
  }

  addContentGuaranteeBox(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContentGuaranteeBox,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentGuaranteeBox = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.GUARANTEE_BOX,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.RECIPE);
  }

  addContentRecipeCategorySelect(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContentRecipeCategorySelect,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentRecipeCategorySelect = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.RECIPE_CATEGORY_SELECT,
      contentLength: 1,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentGalleryArticleList(
    modalResult: Partial<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContent,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.GALLERY_ARTICLE_LIST,
      ...modalResult,
    } as LayoutElementContent;
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentWeeklyMenu(modalResult: LayoutEditorModalResult<any>, modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.WEEKLY_MENU,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentColumnBlock(modalResult: LayoutEditorModalResult<any>, modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.COLUMN_BLOCK,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentProgram(
    modalResult: LayoutEditorModalResult<LayoutElementContentProgram>,
    modifyItem?: LayoutElementContentProgram,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentProgram = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.PROGRAM,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.PROGRAM);
  }

  addContentNewsletterBlock(
    modalResult: LayoutEditorModalResult<LayoutElementContentNewsletterBlock>,
    modifyItem?: LayoutElementContentNewsletterBlock,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentNewsletterBlock = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.NEWSLETTER_BLOCK,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentNewsletterBlockGong(
    modalResult: LayoutEditorModalResult<LayoutElementContentNewsletterBlockGong>,
    modifyItem?: LayoutElementContentNewsletterBlockGong,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentNewsletterBlockGong = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.NEWSLETTER_BLOCK_GONG,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentOpinionNewsletterBox(
    modalResult: LayoutEditorModalResult<LayoutElementContentOpinionNewsletterBox>,
    modifyItem?: LayoutElementContentOpinionNewsletterBox,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentOpinionNewsletterBox = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.OPINION_NEWSLETTER_BOX,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addTeams(
    modalResult: LayoutEditorModalResult<LayoutElementContent>,
    modifyItem?: LayoutElementContent,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement = {
      contentType: LayoutElementContentType.TEAMS,
      configurable: true,
      ...this.getCommonContentElementData(modifyItem, true),
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addDailyProgram(
    modalResult: LayoutEditorModalResult<LayoutElementContent>,
    modifyItem?: LayoutElementContent,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement = {
      contentType: LayoutElementContentType.DAILY_PROGRAM,
      configurable: false,
      ...this.getCommonContentElementData(modifyItem, false),
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentConference(modalResult: LayoutEditorModalResult<any>, modifyItem?: LayoutElementContent, parent?: LayoutElementRow | LayoutElementColumn): void {
    const newElement = {
      contentType: LayoutElementContentType.CONFERENCE,
      configurable: true,
      ...this.getCommonContentElementData(modifyItem, true),
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addLeadEditors(modalResult: LayoutEditorModalResult<any>, modifyItem?: LayoutElementContent, parent?: LayoutElementRow | LayoutElementColumn): void {
    const newElement = {
      contentType: LayoutElementContentType.LEAD_EDITORS,
      configurable: true,
      ...this.getCommonContentElementData(modifyItem, true),
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addWriteToUs(modalResult: LayoutEditorModalResult<LayoutElementContent>, modifyItem?: LayoutElementContent, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: LayoutElementContent = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.WRITE_TO_US,
      previewImage: `/assets/images/layout-frames/${this.domainKey}/write-to-us.png`,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentMediaPanel(
    modalResult: LayoutEditorModalResultMulti,
    modifyItem?: LayoutElementContentMediaPanel,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const previewImage = `/assets/images/layout-frames/${this.domainKey}/media-panel-${modalResult.contentLength}.png`;
    const newElement: LayoutElementContentMediaPanel = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.MEDIA_PANEL,
      ...modalResult,
      previewImage,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addSportRadioPlayer(modalResult: LayoutEditorModalResult<any>, modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: any = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.SPORT_RADIO_PLAYER,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addTopRankingGlossary(modalResult: LayoutEditorModalResult<any>, modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: any = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.TOP_RANKING_GLOSSARY,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addConfigurableSponsoredBox(modalResult: LayoutEditorModalResult<any>, modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: any = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addBlockSeparator(modalResult: LayoutEditorModalResult<any>, modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: any = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.BLOCK_SEPARATOR,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addVisitorCounter(modalResult: LayoutEditorModalResult<any>, modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: any = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.VISITOR_COUNTER,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentAgroKepBlock(
    modalResult: LayoutEditorModalResult<LayoutElementContentNewsletterBlock>,
    modifyItem?: LayoutElementContentNewsletterBlock,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentNewsletterBlock = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.AGROKEP,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentAgroKepListBlock(
    modalResult: LayoutEditorModalResult<LayoutElementContentNewsletterBlock>,
    modifyItem?: LayoutElementContentNewsletterBlock,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentNewsletterBlock = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.AGROKEP_LIST,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentOpinionList(
    modalResult: LayoutEditorModalResultMulti<LayoutElementOpinionListPreview>,
    modifyItem?: LayoutElementContentOpinionList,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentOpinionList = {
      ...this.getCommonContentElementData(modifyItem, false, null),
      withHorizontalSeparator: modifyItem?.withHorizontalSeparator ?? false,
      contentType: LayoutElementContentType.OPINION_LIST,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentOpinion(
    modalResult: LayoutEditorModalResultMulti<LayoutElementOpinionPreview>,
    modifyItem?: LayoutElementContentOpinion,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentOpinion = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      contentType: LayoutElementContentType.OPINION,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.OPINIONS);
  }

  addContentBayerBlog(
    modalResult: LayoutEditorModalResultMulti<LayoutElementOpinionPreview>,
    modifyItem?: LayoutElementContentBayerBlog,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentBayerBlog = {
      ...this.getCommonContentElementData(modifyItem, true, 1),
      contentType: LayoutElementContentType.BAYER_BLOG,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.OPINIONS);
  }

  addContentWhereTheBallWillBe(
    modalResult: LayoutEditorModalResultMulti<LayoutElementOpinionPreview>,
    modifyItem?: LayoutElementContentWhereTheBallWillBe,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement: LayoutElementContentWhereTheBallWillBe = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.WHERE_THE_BALL_WILL_BE,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.OPINIONS);
  }

  addContentNote(
    modalResult: LayoutEditorModalResult<LayoutElementContentOpinion>,
    modifyItem?: LayoutElementContentOpinion,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentOpinion = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      contentType: LayoutElementContentType.NOTE,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.NOTES);
  }

  addContentSorozatveto(
    modalResult: LayoutEditorModalResult<LayoutElementContentSorozatveto>,
    modifyItem?: LayoutElementContentSorozatveto,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentSorozatveto = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      contentLength: 1,
      contentType: LayoutElementContentType.SOROZATVETO,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentManualArtical(
    modalResult: LayoutEditorModalResult<LayoutElementContentManualArticle>,
    modifyItem?: LayoutElementContentManualArticle,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentManualArticle = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      contentType: LayoutElementContentType.MANUAL_ARTICLE,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentManualOpinion(
    modalResult: LayoutEditorModalResult<LayoutElementContentManualOpinion>,
    modifyItem?: LayoutElementContentManualOpinion,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentManualOpinion = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      contentType: LayoutElementContentType.MANUAL_OPINION,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.OPINIONS);
  }

  addContentVideo(
    modalResult: LayoutEditorModalResult<LayoutElementContentVideo>,
    modifyItem?: LayoutElementContentVideo,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentVideo = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      contentType: LayoutElementContentType.VIDEO,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.VIDEOS);
  }

  addContentShortVideo(
    modalResult: LayoutEditorModalResult<LayoutElementContentShortVideos>,
    modifyItem?: LayoutElementContentShortVideos,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentShortVideos = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      contentType: LayoutElementContentType.SHORT_VIDEOS,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.SHORT_VIDEOS);
  }

  addContentNewsFeed(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview & { maxSecondaryArticleCount: number[] }>,
    modifyItem?: LayoutElementContentDossier,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentDossier = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.NEWS_FEED,
      ...modalResult,
    } as unknown as LayoutElementContentDossier;
    this.updateParentElement(modifyItem, newElement, parent);
  }

  addContentAuthor(
    modalResult: LayoutEditorModalResult<LayoutElementContent>,
    modifyItem?: LayoutElementContent,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement: LayoutElementContent = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.AUTHOR,
      ...modalResult,
    };
    this.updateParentElement(modifyItem, newElement, parent);
  }

  addContentDossier(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview & { maxSecondaryArticleCount: number[] }>,
    modifyItem?: LayoutElementContentDossier,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentDossier = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.DOSSIER,
      ...modalResult,
    };
    this.updateParentElement(modifyItem, newElement, parent);
  }

  addContentStockChart(
    modalResult: LayoutEditorModalResult<LayoutElementContentStockChart>,
    modifyItem?: LayoutElementContentStockChart,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentStockChart = {
      ...this.getCommonContentElementData(modifyItem),
      previewImage: '/assets/images/layout-frames/vg/stock01.png',
      contentType: LayoutElementContentType.STOCK,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentFreshNews(
    modalResult: LayoutEditorModalResult<LayoutElementContentFreshNews>,
    modifyItem?: LayoutElementContentFreshNews,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    let previewImage = '';
    switch (this.domainKey) {
      case 'mandiner':
        previewImage = '/assets/images/layout-frames/mandiner/twenty-four-news01.png';
        break;
      case 'origo':
        previewImage = '/assets/images/layout-frames/origo/fresh-news01.png';
        break;
      default:
        previewImage = '/assets/images/layout-frames/mno/fresh-news01.png';
    }

    const newElement: LayoutElementContentFreshNews = {
      ...this.getCommonContentElementData(modifyItem, true),
      previewImage,
      contentType: LayoutElementContentType.FRESH_NEWS,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentLatestNews(
    modalResult: LayoutEditorModalResult<LayoutElementContentLatestNews>,
    modifyItem?: LayoutElementContentLatestNews,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    let previewImage = '';
    switch (this.domainKey) {
      case 'nso':
        previewImage = '/assets/images/layout-frames/nso/latest-news01.png';
        break;
      default:
        previewImage = '/assets/images/layout-frames/vg/latest-news01.png';
    }
    const newElement: LayoutElementContentLatestNews = {
      ...this.getCommonContentElementData(modifyItem, true),
      previewImage,
      contentType: LayoutElementContentType.LATEST_NEWS,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentLinkList(
    modalResult: LayoutEditorModalResult<LayoutElementContentLinkList>,
    modifyItem?: LayoutElementContentLinkList,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentLinkList = {
      ...this.getCommonContentElementData(modifyItem, true),
      previewImage: '/assets/images/layout-frames/mno/link-list01.png',
      contentType: LayoutElementContentType.LINK_LIST,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentVote(
    modalResult: LayoutEditorModalResult<LayoutElementContentVote>,
    modifyItem?: LayoutElementContentVote,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentVote = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.VOTE,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.VOTE);
  }

  addContentSponsoredVote(
    modalResult: LayoutEditorModalResult<LayoutElementContentSponsoredVote>,
    modifyItem?: LayoutElementContentSponsoredVote,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentSponsoredVote = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.SPONSORED_VOTE,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.VOTE);
  }

  addContentMultiVote(
    modalResult: LayoutEditorModalResult<LayoutElementContentMultiVote>,
    modifyItem?: LayoutElementContentMultiVote,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentMultiVote = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.MULTI_VOTE,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.MULTI_VOTE);
  }

  addContentExperienceOccasion(
    modalResult: LayoutEditorModalResult<LayoutElementContentExperienceOccasion>,
    modifyItem?: LayoutElementContentExperienceOccasion,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentExperienceOccasion = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.EXPERIENCE_OCCASION);
  }

  addContentVisegradPost(
    modalResult: LayoutEditorModalResult<LayoutElementContentVisegradPost>,
    modifyItem?: LayoutElementContentVisegradPost,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentVisegradPost = {
      ...this.getCommonContentElementData(modifyItem, true),
      previewImage: '/assets/images/layout-frames/mno/visegrad-post01.png',
      contentType: LayoutElementContentType.VISEGRAD_POST,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentCultureNation(
    modalResult: LayoutEditorModalResult,
    modifyItem?: LayoutElementContentCultureNation,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentCultureNation = {
      ...this.getCommonContentElementData(modifyItem),
      previewImage: '/assets/images/layout-frames/mno/culture-nation01.png',
      contentType: LayoutElementContentType.CULTURE_NATION,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentHtmlEmbed(modalResult: LayoutEditorModalResult, modifyItem?: LayoutElementContentHtmlEmbed, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: LayoutElementContentHtmlEmbed = {
      ...this.getCommonContentElementData(modifyItem, true),
      previewImage: '/assets/images/layout-frames/mno/html-embed01.png',
      contentType: LayoutElementContentType.HTML_EMBED,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentNewspaper(modalResult: LayoutEditorModalResult, modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: LayoutElementContentNewspaper = {
      ...this.getCommonContentElementData(modifyItem, true),
      previewImage:
        this.domainKey !== 'magyarNemzet'
          ? `/assets/images/layout-frames/${this.domainKey}/newspaper01.png`
          : '/assets/images/layout-frames/mno/newspaper01.png',
      contentType: LayoutElementContentType.NEWSPAPER,
      configurable: true,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentServicesBox(
    modalResult: LayoutEditorModalResult<LayoutElementContentServicesBox>,
    modifyItem?: any,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentServicesBox = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.SERVICES_BOX,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentPodcastAppRecommender(
    modalResult: LayoutEditorModalResult<LayoutElementContentPodcastAppRecommender>,
    modifyItem?: any,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentPodcastAppRecommender = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.PODCAST_APP_RECOMMENDER,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentSponsoredArticleBox(modalResult: LayoutEditorModalResult, modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: LayoutElementContentSponsoredArticleBox = {
      ...this.getCommonContentElementData(modifyItem, true),
      previewImage: `/assets/images/layout-frames/${this.domainKey}/sponsoredArticleBox.png`,
      contentType: LayoutElementContentType.SPONSORED_ARTICLE_BOX,
      configurable: true,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentDidYouKnow(modalResult: LayoutEditorModalResult, modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: LayoutElementContentDidYouKnow = {
      ...this.getCommonContentElementData(modifyItem, true),
      previewImage: `/assets/images/layout-frames/shared/variable-did-you-know.webp`,
      contentType: LayoutElementContentType.DID_YOU_KNOW,
      configurable: true,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentSocialMedia(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContentSocialMedia,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    let previewImage = this.domainKey === 'magyarNemzet' ? '/assets/images/layout-frames/mno/social-media02.png ' : null;

    const newElement: LayoutElementContentSocialMedia = {
      ...this.getCommonContentElementData(modifyItem, false),
      previewImage,
      contentType: LayoutElementContentType.SOCIAL_MEDIA,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentRssBox(modalResult: LayoutEditorModalResult<any>, modifyItem?: LayoutElementContentRssBox, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: LayoutElementContentRssBox = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.RSS_BOX,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentIngatlanbazar(modalResult: LayoutEditorModalResult, modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: LayoutElementContentIngatlanbazar = {
      ...this.getCommonContentElementData(modifyItem),
      previewImage:
        {
          metropol: '/assets/images/layout-frames/metropol/ingatlanbazar01.png',
          mandiner: '/assets/images/layout-frames/mandiner/ingatlanbazar01.png',
        }[this.domain] ?? '/assets/images/layout-frames/megyeilapok/ingatlanbazar.png',
      contentType: LayoutElementContentType.INGATLANBAZAR,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentIngatlanbazarConfigurable(modalResult: LayoutEditorModalResult, modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: LayoutElementContentIngatlanbazarConfigurable = {
      ...this.getCommonContentElementData(modifyItem),
      previewImage:
        {
          metropol: '/assets/images/layout-frames/metropol/ingatlanbazar-ingatlan.png',
          mandiner: '/assets/images/layout-frames/mandiner/ingatlanbazar01.png',
        }[this.domain] ?? '/assets/images/layout-frames/metropol/ingatlanbazar-ingatlan.png',
      contentType: LayoutElementContentType.INGATLANBAZAR_CONFIGURABLE,
      showHeader: modifyItem?.showHeader,
      xmlUrl: modifyItem?.xmlUrl,
      itemsToShow: modifyItem?.itemsToShow,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentIngatlanbazarSearch(modalResult: LayoutEditorModalResult, modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: LayoutElementContentIngatlanbazarSearch = {
      ...this.getCommonContentElementData(modifyItem, true),
      previewImage: '/assets/images/layout-frames/metropol/ingatlanbazar-search01.jpg',
      contentType: LayoutElementContentType.INGATLANBAZAR_SEARCH,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentTagBlock(modalResult: LayoutEditorModalResult, modifyItem?: LayoutElementContentTagBlock, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: LayoutElementContentTagBlock = {
      ...this.getCommonContentElementData(modifyItem, true),
      id: modifyItem?.id || this.generateId(),
      previewImage: this.domainKey === 'ripost' ? '/assets/images/layout-frames/ripost/tag-block01.png' : '/assets/images/layout-frames/origo/tag-block01.png',
      contentType: LayoutElementContentType.TAG_BLOCK,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentTrendingTagsBlock(
    modalResult: LayoutEditorModalResult,
    modifyItem?: LayoutElementContentTrendingTagsBlock,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentTrendingTagsBlock = {
      ...this.getCommonContentElementData(modifyItem, true),
      previewImage:
        this.domainKey === 'mandiner'
          ? '/assets/images/layout-frames/mandiner/trending-tags-block.png'
          : '/assets/images/layout-frames/ripost/trending-tags-block.png',
      contentType: LayoutElementContentType.TRENDING_TAGS_BLOCK,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentBrandingBox(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview & Brand>,
    modifyItem?: LayoutElementContentBrandingBox,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentBrandingBox = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.BRANDING_BOX,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentGallery(
    modalResult: LayoutEditorModalResult<LayoutElementGalleryPreview>,
    modifyItem?: LayoutElementContentGallery,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    if (this.domainKey === 'bors') {
      modalResult.galleryLength = modalResult.galleryLengthOverwrite;
    }

    const newElement: LayoutElementContentGallery = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      contentType: LayoutElementContentType.GALLERY,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.GALLERIES);
  }

  addContentBlog(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview & { hasTitle: boolean }>,
    modifyItem?: LayoutElementContentBlog,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentBlog = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      contentType: LayoutElementContentType.BLOG,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.BLOG);
  }

  addContentAstronetColumns(
    modalResult: LayoutEditorModalResult,
    modifyItem?: LayoutEditorModalResultMulti,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement = {
      ...this.getCommonContentElementData(modifyItem as LayoutElementContent, true),
      contentType: LayoutElementContentType.ASTRONET_COLUMNS,
      ...modalResult,
    } as LayoutElementContent;
    this.updateAndEmitEvent(modifyItem as LayoutElementContent, newElement, parent);
  }

  addContentAstronetBrandingBox(
    modalResult: LayoutEditorModalResult,
    modifyItem?: LayoutEditorModalResultMulti,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement = {
      ...this.getCommonContentElementData(modifyItem as LayoutElementContent, true),
      contentType: LayoutElementContentType.ASTRONET_BRANDING_BOX,
      ...modalResult,
    } as LayoutElementContent;
    this.updateAndEmitEvent(modifyItem as LayoutElementContent, newElement, parent);
  }

  addContentSubColumns(modalResult: LayoutEditorModalResult, modifyItem?: LayoutEditorModalResultMulti, parent?: LayoutElementRow | LayoutElementColumn): void {
    const newElement = {
      ...this.getCommonContentElementData(modifyItem as LayoutElementContent, true),
      contentType: LayoutElementContentType.SUB_COLUMNS,
      ...modalResult,
    } as LayoutElementContent;
    this.updateAndEmitEvent(modifyItem as LayoutElementContent, newElement, parent);
  }

  addContentLatestAndMostReadArticles(
    modalResult: LayoutEditorModalResult,
    modifyItem?: LayoutElementContent,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.LATEST_AND_MOST_READ_ARTICLES,
      previewImage: '/assets/images/layout-frames/bors/latest-and-most-read-articles.webp',
      ...modalResult,
    } as LayoutElementContent;
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentStarBirths(modalResult: LayoutEditorModalResult, modifyItem?: LayoutElementContent, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.STAR_BIRTHS,
      previewImage: '/assets/images/layout-frames/bors/star-births.webp',
      ...modalResult,
    } as LayoutElementContent;
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentKoponyeg(
    modalResult: LayoutEditorModalResultMulti<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContentKoponyeg,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentKoponyeg = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.KOPONYEG,
      configurable: this.domainKey === 'metropol',
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentWaze(modalResult: LayoutEditorModalResult, modifyItem?: LayoutElementContentWaze, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: LayoutElementContentWaze = {
      ...this.getCommonContentElementData(modifyItem),
      previewImage: '/assets/images/layout-frames/metropol/waze01.png',
      contentType: LayoutElementContentType.WAZE,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentSzakikereso(modalResult: LayoutEditorModalResult, modifyItem?: LayoutElementContentSzakikereso, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: LayoutElementContentSzakikereso = {
      ...this.getCommonContentElementData(modifyItem),
      previewImage: '/assets/images/layout-frames/metropol/szakikereso01.png',
      contentType: LayoutElementContentType.SZAKIKERESO,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentBestRecommender(
    modalResult: LayoutEditorModalResultMulti,
    modifyItem?: LayoutElementContentBestRecommender,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentBestRecommender = {
      ...this.getCommonContentElementData(modifyItem, true),
      previewImage: '/assets/images/layout-frames/metropol/bestrecommender01.png',
      contentType: LayoutElementContentType.BEST_RECOMMENDER,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentCategoryStepper(
    modalResult: LayoutEditorModalResultMulti,
    modifyItem?: LayoutElementContentCategoryStepper,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentCategoryStepper = {
      ...this.getCommonContentElementData(modifyItem, true),
      previewImage: '/assets/images/layout-frames/mandiner/category-stepper01.png',
      contentType: LayoutElementContentType.CATEGORY_STEPPER,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentAstronetHoroszkop(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContentAstronetHoroszkop,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentAstronetHoroszkop = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.ASTRONET_HOROSZKOP,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentAstronetJoslas(modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>, modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: LayoutElementContentAstronetJoslas = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.ASTRONET_JOSLAS,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentFinalCountdown(modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>, modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: LayoutElementContentFinalCountdown = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.FINAL_COUNTDOWN,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.IMAGE);
  }

  addContentDossierList(modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>, modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: LayoutElementContentDossierList = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.DOSSIER_LIST,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentBroadcastRecommender(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContentBroadcastRecommender,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentBroadcastRecommender = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.BROADCAST_RECOMMENDER,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentSongTopList(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContentSongTopList,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentSongTopList = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.SONG_TOP_LIST,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentPdfBox(
    modalResult: LayoutEditorModalResult<LayoutElementContentPdfBox>,
    modifyItem?: LayoutElementContentPdfBox,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentPdfBox = {
      ...this.getCommonContentElementData(modifyItem, true, null),
      contentType: LayoutElementContentType.PDF_BOX,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentTenyekBox(modalResult: LayoutEditorModalResult, modifyItem?: LayoutElementContentTenyekBox, parent?: LayoutElementRow | LayoutElementColumn): void {
    const newElement: LayoutElementContentTenyekBox = {
      ...this.getCommonContentElementData(modifyItem),
      previewImage: '/assets/images/layout-frames/shared/tenyek-box01.png',
      contentType: LayoutElementContentType.TENYEK_BOX,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentTripBox(
    modalResult: LayoutEditorModalResult<LayoutElementContentTripBox>,
    modifyItem?: LayoutElementContentTripBox,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement = {
      contentType: LayoutElementContentType.TRIP_BOX,
      configurable: true,
      ...this.getCommonContentElementData(modifyItem, true),
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentGPNewsBox(
    modalResult: LayoutEditorModalResult<LayoutElementContentGPNewsBox>,
    modifyItem?: LayoutElementContentGPNewsBox,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement = {
      contentType: LayoutElementContentType.GP_NEWS_BOX,
      configurable: true,
      ...this.getCommonContentElementData(modifyItem, true),
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentEBNewsBox(
    modalResult: LayoutEditorModalResult<LayoutElementContentEBNews>,
    modifyItem?: LayoutElementContentEBNews,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement = {
      contentType: LayoutElementContentType.EB_NEWS,
      configurable: true,
      ...this.getCommonContentElementData(modifyItem, true),
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentBrandingBoxArticle(
    modalResult: LayoutEditorModalResult<LayoutElementContentBrandingBoxArticle>,
    modifyItem?: LayoutElementContentBrandingBoxArticle,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement = {
      contentType: LayoutElementContentType.BRANDING_BOX_ARTICLE,
      configurable: true,
      ...this.getCommonContentElementData(modifyItem, true),
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentLiveBar(
    modalResult: LayoutEditorModalResult<LayoutElementContentLiveBar>,
    modifyItem?: LayoutElementContentLiveBar,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement = {
      contentType: LayoutElementContentType.LIVE_BAR,
      configurable: true,
      ...this.getCommonContentElementData(modifyItem, true),
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentMoreArticles(
    modalResult: LayoutEditorModalResult<LayoutElementContentMoreArticles>,
    modifyItem?: LayoutElementContentMoreArticles,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement = {
      contentType: LayoutElementContentType.MORE_ARTICLES,
      configurable: true,
      ...this.getCommonContentElementData(modifyItem, true),
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentRelatedArticles(
    modalResult: LayoutEditorModalResult<LayoutElementContentRelatedArticles>,
    modifyItem?: LayoutElementContentRelatedArticles,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement = {
      contentType: LayoutElementContentType.RELATED_ARTICLES,
      configurable: true,
      ...this.getCommonContentElementData(modifyItem, true),
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentSportBlock(
    modalResult: LayoutEditorModalResult<LayoutElementContentSportBlock>,
    modifyItem?: LayoutElementContentSportBlock,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement = {
      contentType: LayoutElementContentType.SPORT_BLOCK,
      configurable: true,
      ...this.getCommonContentElementData(modifyItem, true),
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentRecipeCard(modalResult: LayoutEditorModalResultMulti, modifyItem?: LayoutElementContentRecipe, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: LayoutElementContentRecipe = {
      ...this.getCommonContentElementData(modifyItem, true),
      styleId: modifyItem?.styleId,
      hasBackground: modifyItem?.hasBackground,
      contentType: LayoutElementContentType.RECIPE,
      previewImage: modifyItem?.previewImage,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.RECIPE);
  }

  addContentArticleBlock(
    modalResult: LayoutEditorModalResult<BasicLayoutElementPreview>,
    modifyItem?: LayoutElementContentArticleBlock,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentArticleBlock = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.ARTICLE_BLOCK,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentTelekomVivicitta(
    modalResult: LayoutEditorModalResult<LayoutElementContentTelekomVivicitta>,
    modifyItem?: LayoutElementContentTelekomVivicitta,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentTelekomVivicitta = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.TELEKOM_VIVICITTA,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentCountdownBox(
    modalResult: LayoutEditorModalResult<LayoutElementContentCountdownBox>,
    modifyItem?: LayoutElementContentCountdownBox,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentCountdownBox = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.COUNTDOWN_BOX,
      configurable: true,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentElectionsBox(
    modalResult: LayoutEditorModalResult<LayoutElementContentElectionsBox>,
    modifyItem?: LayoutElementContentElectionsBox,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentElectionsBox = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.ELECTIONS_BOX,
      configurable: false,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentEbCountdownBlockTitle(
    modalResult: LayoutEditorModalResult<LayoutElementContentEbCountdownBlockTitle>,
    modifyItem?: LayoutElementContentEbCountdownBlockTitle,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentEbCountdownBlockTitle = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.EB_COUNTDOWN_BLOCK_TITLE,
      configurable: true,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentEbSingleElimination(
    modalResult: LayoutEditorModalResult<LayoutElementContent>,
    modifyItem?: LayoutElementContent,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContent = {
      contentType: LayoutElementContentType.EB_SINGLE_ELIMINATION,
      configurable: true,
      ...this.getCommonContentElementData(modifyItem),
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentOlimpiaCountdownBlockTitle(
    modalResult: LayoutEditorModalResult<LayoutElementContentOlimpiaCountdownBlockTitle>,
    modifyItem?: LayoutElementContentOlimpiaCountdownBlockTitle,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentOlimpiaCountdownBlockTitle = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.OLIMPIA_COUNTDOWN_BLOCK_TITLE,
      configurable: true,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentOlimpiaNewsBox(
    modalResult: LayoutEditorModalResult<LayoutElementContentOlimpiaNews>,
    modifyItem?: LayoutElementContentOlimpiaNews,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement = {
      contentType: LayoutElementContentType.OLIMPIA_NEWS,
      configurable: true,
      ...this.getCommonContentElementData(modifyItem, true),
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent, ContentElementItemType.ARTICLES);
  }

  addContentOlimpiaHungarianCompetitions(modalResult: LayoutEditorModalResult, modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: LayoutElementContentOlimpiaHungarianCompetitions = {
      ...this.getCommonContentElementData(modifyItem, true),
      previewImage: `/assets/images/layout-frames/${this.domainKey === 'magyarNemzet' ? 'mno' : this.domainKey}/olimpia-hungarian-competitions.png`,
      contentType: LayoutElementContentType.OLIMPIA_HUNGARIAN_COMPETITIONS,
      configurable: true,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentOlimpiaLargeNavigator(modalResult: LayoutEditorModalResult, modifyItem?: any, parent?: LayoutElementRow | LayoutElementColumn) {
    const newElement: LayoutElementContentOlimpiaLargeNavigator = {
      ...this.getCommonContentElementData(modifyItem, true),
      contentType: LayoutElementContentType.OLIMPIA_LARGE_NAVIGATOR,
      configurable: true,
      ...modalResult,
    } as LayoutElementContentOlimpiaLargeNavigator;
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentOlimpiaHungarianTeam(
    modalResult: LayoutEditorModalResult<LayoutElementContentOlimpiaHungarianTeam>,
    modifyItem?: LayoutElementContentOlimpiaHungarianTeam,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentOlimpiaHungarianTeam = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.OLIMPIA_HUNGARIAN_TEAM,
      configurable: true,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentOlimpiaResultsBlock(
    modalResult: LayoutEditorModalResult<LayoutElementContentOlimpiaResultsBlock>,
    modifyItem?: LayoutElementContentOlimpiaResultsBlock,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const newElement: LayoutElementContentOlimpiaResultsBlock = {
      ...this.getCommonContentElementData(modifyItem),
      contentType: LayoutElementContentType.OLIMPIA_RESULTS_BLOCK,
      configurable: false,
      ...modalResult,
    };
    this.updateAndEmitEvent(modifyItem, newElement, parent);
  }

  addContentTopCommentedArticles(
    modalResult: LayoutEditorModalResult,
    modifyItem?: LayoutEditorModalResultMulti,
    parent?: LayoutElementRow | LayoutElementColumn
  ): void {
    const newElement = {
      ...this.getCommonContentElementData(modifyItem as LayoutElementContent, true),
      contentType: LayoutElementContentType.TOP_COMMENTED_ARTICLES,
      ...modalResult,
    } as LayoutElementContent;
    this.updateAndEmitEvent(modifyItem as LayoutElementContent, newElement, parent);
  }

  selectElement(event: MouseEvent, element: LayoutElementRow | LayoutElementColumn) {
    this.activeElement = element;
    event.stopPropagation();
  }

  deselectAll() {
    this.activeElement = null;
  }

  getIteratorArray(layoutElement: LayoutElementContent): number[] {
    if (layoutElement.contentType === LayoutElementContentType.GP_NEWS_BOX) {
      if ((layoutElement as LayoutElementContentGPNewsBox).turnOffPreviewImageGenerateByContentLength) {
        return [0];
      }
    }
    if (
      [
        LayoutElementContentType.KOMPOST_BLOCK,
        LayoutElementContentType.YESSFACTOR_BLOCK,
        LayoutElementContentType.BEST_RECOMMENDER,
        LayoutElementContentType.CATEGORY_STEPPER,
        LayoutElementContentType.RIPOST7_BLOCK,
        LayoutElementContentType.MANUAL_ARTICLE,
        LayoutElementContentType.MANUAL_OPINION,
        LayoutElementContentType.OPINION_BLOCK,
        LayoutElementContentType.KULTUR_NEMZET,
        LayoutElementContentType.MEDIA_PANEL,
        LayoutElementContentType.INGREDIENT,
        LayoutElementContentType.RECIPE_SWIPER,
        LayoutElementContentType.SPORT_BLOCK,
        LayoutElementContentType.ARTICLE_SLIDER,
        LayoutElementContentType.GUARANTEE_BOX,
        LayoutElementContentType.MOST_VIEWED,
        LayoutElementContentType.COLUMN_BLOCK,
        LayoutElementContentType.SPOTLIGHT,
        LayoutElementContentType.OFFER_BOX,
        LayoutElementContentType.ARTICLE_BLOCK,
        LayoutElementContentType.BRANDING_BOX_ARTICLE,
        LayoutElementContentType.ARTICLES_WITH_PODCAST_CONTENT,
        LayoutElementContentType.EB_NEWS,
        LayoutElementContentType.OLIMPIA_NEWS,
        LayoutElementContentType.BAYER_BLOG,
        LayoutElementContentType.GASTRO_EXPERIENCE_RECOMMENDATION,
        LayoutElementContentType.TOP_TEN_TAGS,
        LayoutElementContentType.SHORT_VIDEOS,
      ].includes(layoutElement.secondaryContentType ?? layoutElement.contentType)
    ) {
      return [0];
    }
    const arr: number[] = [];
    for (let i = 0; i < (layoutElement.contentLength ?? 0); i++) {
      arr.push(i);
    }
    return arr;
  }

  onDelete(layoutItem: LayoutElement, parentItem?: LayoutElementContent | LayoutElementRow | LayoutElementColumn) {
    if (!this.hasPermissionToDeleteOrEdit(layoutItem)) {
      this.sharedService.showNotification('error', 'Hirdetés tartalom! Nincs jogosultsága ehhez a művelethez.');
      return;
    }

    if (
      (layoutItem.type === LayoutElementType.ROW || layoutItem.type === LayoutElementType.COLUMN) &&
      (this.type === 'Sidebar' || this.type === 'ColumnSidebar')
    ) {
      return;
    }

    const confirmModal = this.modal.confirm({
      nzTitle: this.translate.instant('CMS.confirm_delete_layout_element_title'),
      nzContent: layoutItem.type !== LayoutElementType.CONTENT ? this.translate.instant('CMS.confirm_delete_layout_element_lead') : null,
      nzOkText: this.translate.instant('CMS.delete'),
      nzCancelText: this.translate.instant('CMS.cancel'),
      nzIconType: '',
      nzOnOk: () =>
        new Promise(() => {
          confirmModal.destroy();
          this.structureChanged.emit();
          const arrayToModify = parentItem === null ? this.rootElements : (parentItem as LayoutElementColumn).elements;
          const index = arrayToModify.indexOf(layoutItem as LayoutElementContent);
          if (index > -1) {
            arrayToModify.splice(index, 1);
          }
        }),
    });
  }

  onAddContentButtonClick(handleType: LayoutElementContentType) {
    this.openConfigModal(handleType);
  }

  onConfigButtonClick(
    handleType: LayoutElementType | LayoutElementContentType,
    layoutItem?: LayoutElementContent | LayoutElementRow | LayoutElementColumn,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    if (!this.hasPermissionToDeleteOrEdit(layoutItem)) {
      this.sharedService.showNotification('error', 'Hirdetés tartalom! Nincs jogosultsága ehhez a művelethez.');
      return;
    }
    this.openConfigModal(handleType, layoutItem, parent);
  }

  openConfigModal(
    handleType: LayoutElementType | LayoutElementContentType,
    layoutItem?: LayoutElementContent | LayoutElementRow | LayoutElementColumn,
    parent?: LayoutElementRow | LayoutElementColumn
  ) {
    const layoutElementType = (layoutItem as LayoutElementContentArticle)?.secondaryContentType ?? handleType;

    if (![LayoutElementType.ROW, LayoutElementType.COLUMN].some((type) => type === layoutElementType)) {
      if (!this.activeElement) {
        this.activeElement = parent;
      }
      if (!this.canOpenModal(layoutItem)) {
        return;
      }
    }

    switch (layoutElementType) {
      case LayoutElementType.ROW:
        this.openRowModal(layoutItem as LayoutElementRow, parent);
        break;
      case LayoutElementType.COLUMN:
        this.openColumnModal(layoutItem as LayoutElementColumn, parent as LayoutElementRow);
        break;
      default:
        this.openContentModal(layoutItem, parent, layoutElementType);
    }
  }

  getContentElementColumnRate(element: LayoutElementContent): number {
    // TODO: review with better case handling
    return [
      LayoutElementContentType.KOMPOST_BLOCK,
      LayoutElementContentType.YESSFACTOR_BLOCK,
      LayoutElementContentType.BEST_RECOMMENDER,
      LayoutElementContentType.RIPOST7_BLOCK,
      LayoutElementContentType.OPINION_BLOCK,
      LayoutElementContentType.COLUMN_BLOCK,
      LayoutElementContentType.ARTICLE_SLIDER,
      LayoutElementContentType.RECIPE_SWIPER,
      LayoutElementContentType.INGREDIENT,
      LayoutElementContentType.GUARANTEE_BOX,
      LayoutElementContentType.OFFER_BOX,
    ].includes(element.secondaryContentType ?? element.contentType)
      ? 12
      : Math.round(12 / (element?.contentLength ?? 1));
  }

  generateId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      /* eslint-disable no-bitwise */
      const r = (Math.random() * 16) | 0,
        v = c === 'x' ? r : (r & 0x3) | 0x8;
      /* eslint-enable no-bitwise */
      return v.toString(16);
    });
  }

  private hasContentChange<T>(modifyItem: T): boolean {
    return !!this.activeElement || !!modifyItem;
  }

  canAddContent(isModify: boolean) {
    if (this.activeElement?.type !== LayoutElementType.COLUMN) {
      if (isModify) {
        return (this.activeElement as LayoutElementRow).elements.length === 1;
      } else {
        return (this.activeElement as LayoutElementRow).elements.length === 0;
      }
    } else {
      return true;
    }
  }

  // returns true if the layout is valid, otherwise returns false
  validateLayout(): boolean {
    return this.validateBranch(this.rootElements);
  }

  validateBranch(elements: LayoutElement[]): boolean {
    let valid = true;
    // TODO: review foreach like in configurator (using valid to force recursion)
    elements.forEach((el) => {
      if (el.type === LayoutElementType.ROW) {
        const cols = (el as LayoutElementRow).elements.filter((child) => child.type === LayoutElementType.COLUMN) as LayoutElementColumn[];
        const contents = (el as LayoutElementRow).elements.filter((child) => child.type === LayoutElementType.CONTENT) as LayoutElementContent[];
        if (cols.reduce((a, b) => a + (b.widthDesktop || 0), 0) < 12 && contents.length === 0) {
          this.sharedService.showNotification('error', 'Kitöltetlen sor található a sablonban!');
          valid = false;
        } else if (cols.reduce((a, b) => a + (b.widthDesktop || 0), 0) > 12 && contents.length > 1) {
          this.sharedService.showNotification('error', 'Túlcsorduló sor található a sablonban!');
          valid = false;
        } else if (valid) {
          valid = this.validateBranch(cols);
        }
      } else if (el.type === LayoutElementType.COLUMN && valid) {
        valid = this.validateBranch((el as LayoutElementColumn).elements.filter((child) => child.type === LayoutElementType.ROW));
      }
    });
    return valid;
  }

  onDragStart(event: DragEvent, contentType: string) {
    event.dataTransfer.setData('contentType', contentType);
  }

  onDrop(event: DragEvent, layoutElement: LayoutElementRow | LayoutElementColumn): void {
    const contentType = event.dataTransfer.getData('contentType');
    this.activeElement = layoutElement;
    event.stopPropagation();

    this.openContentModal(null, null, contentType);
  }

  public openContentModal(
    layoutItem: LayoutElementContent | LayoutElementRow | LayoutElementColumn | null,
    parent: LayoutElementRow | LayoutElementColumn | null,
    layoutElementType: LayoutElementContentType | string = LayoutElementContentType.ARTICLE
  ): void {
    switch (layoutElementType) {
      case LayoutElementContentType.ARTICLE:
      case LayoutElementContentType.YESSFACTOR_BLOCK:
      case LayoutElementContentType.KOMPOST_BLOCK:
      case LayoutElementContentType.RIPOST7_BLOCK:
        this.openContentArticleModal(layoutItem as LayoutElementContentArticle, parent, layoutElementType as LayoutElementContentType);
        break;
      case LayoutElementContentType.AD:
        this.openContentAdModal(layoutItem as LayoutElementContentAd, parent);
        break;
      case LayoutElementContentType.OPINION_LIST:
        this.openContentOpinionListModal(layoutItem as LayoutElementContentOpinionList, parent);
        break;
      case LayoutElementContentType.OPINION:
      case LayoutElementContentType.OPINION_BLOCK:
        this.openContentOpinionModal(layoutItem as LayoutElementContentOpinion, parent, layoutElementType as LayoutElementContentType);
        break;
      case LayoutElementContentType.BAYER_BLOG:
        this.openContentBayerBlogModal(layoutItem as LayoutElementContentBayerBlog, parent, layoutElementType as LayoutElementContentType);
        break;
      case LayoutElementContentType.WHERE_THE_BALL_WILL_BE:
        this.openContentWhereTheBallWillBe(layoutItem as LayoutElementContentWhereTheBallWillBe, parent, layoutElementType as LayoutElementContentType);
        break;
      case LayoutElementContentType.WHERE_THE_BALL_WILL_BE:
        this.openContentWhereTheBallWillBe(layoutItem as LayoutElementContentWhereTheBallWillBe, parent, layoutElementType as LayoutElementContentType);
        break;
      case LayoutElementContentType.VIDEO:
        this.openContentVideoModal(layoutItem as LayoutElementContentVideo, parent);
        break;
      case LayoutElementContentType.SHORT_VIDEOS:
        if (this.domainKey === 'bors' && (parent?.widthDesktopRecursive || this.activeElement.widthDesktopRecursive) !== 12) {
          this.sharedService.showNotification('warning', 'Ez az elem csak 1/1-es elrendezéshez adható hozzá');
          break;
        }
        this.openContentShortVideoModal(layoutItem as LayoutElementContentShortVideos, parent);
        break;
      case LayoutElementContentType.DOSSIER:
        this.openContentDossierModal(layoutItem as LayoutElementContentDossier, parent);
        break;
      case LayoutElementContentType.VIDEO_BLOCK:
        this.openContentVideoBlockModal(layoutItem as LayoutElementContentVideoBlock, parent);
        break;
      case LayoutElementContentType.ARTICLE_SLIDER:
        this.openContentArticleSliderModal(layoutItem as LayoutElementContentArticleSlider, parent);
        break;
      case LayoutElementContentType.NEWSLETTER_BLOCK:
        this.openContentNewsletterModal(layoutItem as LayoutElementContentNewsletterBlock, parent);
        break;
      case LayoutElementContentType.NEWSLETTER_BLOCK_GONG:
        this.openContentGongNewsletterModal(layoutItem as LayoutElementContentNewsletterBlockGong, parent);
        break;
      case LayoutElementContentType.OPINION_NEWSLETTER_BOX:
        this.openContentOpinionNewsletterModal(layoutItem as LayoutElementContentOpinionNewsletterBox, parent);
        break;
      case LayoutElementContentType.STOCK:
        this.openContentStocksChartModal(layoutItem as LayoutElementContentStockChart, parent);
        break;
      case LayoutElementContentType.FRESH_NEWS:
        this.openContentFreshNewsModal(layoutItem as LayoutElementContentFreshNews, parent);
        break;
      case LayoutElementContentType.LATEST_NEWS:
        this.openContentLatestNewsModal(layoutItem as LayoutElementContentLatestNews, parent);
        break;
      case LayoutElementContentType.LINK_LIST:
        this.openContentLinkListModal(layoutItem as LayoutElementContentLinkList, parent);
        break;
      case LayoutElementContentType.VOTE:
        this.openContentVoteModal(layoutItem as LayoutElementContentVote, parent);
        break;
      case LayoutElementContentType.SPONSORED_VOTE:
        this.openContentSponsoredVoteModal(layoutItem as LayoutElementContentSponsoredVote, parent);
        break;
      case LayoutElementContentType.MULTI_VOTE:
        this.openContentMultiVoteModal(layoutItem as LayoutElementContentMultiVote, parent);
        break;
      case LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION:
        this.openContentExperienceOccasionModal(layoutItem as LayoutElementContentExperienceOccasion, parent);
        break;
      case LayoutElementContentType.GALLERY:
        this.openContentGalleryModal(layoutItem as LayoutElementContentGallery, parent);
        break;
      case LayoutElementContentType.BRANDING_BOX:
        this.openContentBrandingBoxModal(layoutItem as LayoutElementContentBrandingBox, parent);
        break;
      case LayoutElementContentType.VISEGRAD_POST:
        this.openContentVisegradPostModal(layoutItem as LayoutElementContentVisegradPost, parent);
        break;
      case LayoutElementContentType.CULTURE_NATION:
        this.openContentCultureNationModal(layoutItem as LayoutElementContentCultureNation, parent);
        break;
      case LayoutElementContentType.HTML_EMBED:
        this.openContentHtmlEmbedModal(layoutItem as LayoutElementContentHtmlEmbed, parent);
        break;
      case LayoutElementContentType.NEWSPAPER:
        this.openContentNewspaperModal(layoutItem as LayoutElementContentNewspaper, parent);
        break;
      case LayoutElementContentType.SERVICES_BOX:
        this.openContentServicesBoxModal(layoutItem as LayoutElementContentServicesBox, parent);
        break;
      case LayoutElementContentType.PODCAST_APP_RECOMMENDER:
        this.openContentPodcastAppRecommenderModal(layoutItem as LayoutElementContentPodcastAppRecommender, parent);
        break;
      case LayoutElementContentType.SOCIAL_MEDIA:
        this.openContentSocialMediaModal(layoutItem as LayoutElementContentSocialMedia, parent);
        break;
      case LayoutElementContentType.ASTROLOGY:
        this.openContentAstrologyModal(layoutItem as LayoutElementContentAstrologyBlock, parent);
        break;
      case LayoutElementContentType.TABS:
        this.openContentTabsModal(layoutItem as LayoutElementContentTabsBlock, parent);
        break;
      case LayoutElementContentType.BREAKING:
        this.openContentBreakingModal(layoutItem as LayoutElementContentBreakingBlock, parent);
        break;
      case LayoutElementContentType.IMAGE:
        this.openContentImageModal(layoutItem as LayoutElementContentImage, parent);
        break;
      case LayoutElementContentType.AGROKEP:
        this.openContentAgroKepModal(layoutItem as LayoutElementContentNewsletterBlock, parent);
        break;
      case LayoutElementContentType.AGROKEP_LIST:
        this.openContentAgroKepListModal(layoutItem as LayoutElementContentNewsletterBlock, parent);
        break;
      case LayoutElementContentType.PR_BLOCK:
        this.openContentPrBlockModal(layoutItem as LayoutElementContentPrBlock, parent);
        break;
      case LayoutElementContentType.NOTE:
        this.openContentNoteModal(layoutItem as LayoutElementContentOpinion, parent);
        break;
      case LayoutElementContentType.WYSIWYG:
        this.openContentWysiwygModal(layoutItem as LayoutElementContentWysiwyg, parent);
        break;
      case LayoutElementContentType.BLOG:
        this.openContentBlogModal(layoutItem as LayoutElementContentBlog, parent);
        break;
      case LayoutElementContentType.INGATLANBAZAR:
        this.openContentIngatlanBazarModal(layoutItem as LayoutElementContentIngatlanbazar, parent);
        break;
      case LayoutElementContentType.INGATLANBAZAR_CONFIGURABLE:
        this.openContentIngatlanBazarConfigurableModal(layoutItem as LayoutElementContentIngatlanbazarConfigurable, parent);
        break;
      case LayoutElementContentType.INGATLANBAZAR_SEARCH:
        this.openContentIngatlanBazarSearchModal(layoutItem as LayoutElementContentIngatlanbazarSearch, parent);
        break;
      case LayoutElementContentType.BRANDING_BOX_EX:
        this.openExternalBrandingBox(layoutItem as LayoutElementContentBrandingBoxEx, parent);
        break;
      case LayoutElementContentType.COLUMN_BLOCK:
        this.openContentColumnBlockModal(layoutItem as LayoutElementContentColumnBlock, parent);
        break;
      case LayoutElementContentType.JOB_LISTINGS:
        this.openContentJobListingsModal(layoutItem as LayoutElementContentJobListings, parent);
        break;
      case LayoutElementContentType.TAG_BLOCK:
        this.openContentTagBlockModal(layoutItem as LayoutElementContentTagBlock, parent);
        break;
      case LayoutElementContentType.TRENDING_TAGS_BLOCK:
        this.openContentTrendingTagsBlockModal(layoutItem as LayoutElementContentTrendingTagsBlock, parent);
        break;
      case LayoutElementContentType.FAST_NEWS:
        this.openContentFastNewsModal(layoutItem as LayoutElementContentFastNews, parent);
        break;
      case LayoutElementContentType.MINUTE_TO_MINUTE:
        this.openContentMinuteToMinuteModal(layoutItem as LayoutElementContentMinuteToMinutes, parent);
        break;
      case LayoutElementContentType.QUIZ:
        this.openContentQuizModal(layoutItem as LayoutElementContentQuiz, parent);
        break;
      case LayoutElementContentType.SPONSORED_QUIZ:
        this.openContentSponsoredQuizModal(layoutItem as LayoutElementContentSponsoredQuiz, parent);
        break;
      case LayoutElementContentType.PROGRAM:
        this.openContentProgramModel(layoutItem as LayoutElementContentProgram, parent);
        break;
      case LayoutElementContentType.KOPONYEG:
        this.openContentKoponyegModal(layoutItem as LayoutElementContentKoponyeg, parent);
        break;
      case LayoutElementContentType.WAZE:
        this.openContentWazeModal(layoutItem as LayoutElementContentWaze, parent);
        break;
      case LayoutElementContentType.SZAKIKERESO:
        this.openContentSzakikeresoModal(layoutItem as LayoutElementContentSzakikereso, parent);
        break;
      case LayoutElementContentType.BEST_RECOMMENDER:
        this.openContentBestRecommenderModal(layoutItem as LayoutElementContentBestRecommender, parent);
        break;
      case LayoutElementContentType.CATEGORY_STEPPER:
        this.openContentCategoryStepperModal(layoutItem as LayoutElementContentCategoryStepper, parent);
        break;
      case LayoutElementContentType.ASTRONET_HOROSZKOP:
        this.openContentAstronetHoroszkopModal(layoutItem as LayoutElementContentAstronetHoroszkop, parent);
        break;
      case LayoutElementContentType.ASTRONET_JOSLAS:
        this.openContentAstronetJoslasModal(layoutItem as LayoutElementContentAstronetJoslas, parent);
        break;
      case LayoutElementContentType.SPORT_RADIO_PLAYER:
        this.openSportRadioModal(layoutItem, parent);
        break;
      case LayoutElementContentType.VISITOR_COUNTER:
        this.openVisitorCounterModal(layoutItem, parent);
        break;
      case LayoutElementContentType.FINAL_COUNTDOWN:
        this.openContentFinalCountdown(layoutItem as LayoutElementContentFinalCountdown, parent);
        break;
      case LayoutElementContentType.HELLO_BUDAPEST:
        this.openContentHelloBudapestModal(layoutItem as LayoutElementContentHelloBudapest, parent);
        break;
      case LayoutElementContentType.DOSSIER_LIST:
        this.openContentDossierListModal(layoutItem as LayoutElementContentDossierList, parent);
        break;
      case LayoutElementContentType.BROADCAST_RECOMMENDER:
        this.openContentBroadcastRecommenderModal(layoutItem as LayoutElementContentBroadcastRecommender, parent);
        break;
      case LayoutElementContentType.SONG_TOP_LIST:
        this.openContentSongTopListModal(layoutItem as LayoutElementContentSongTopList, parent);
        break;
      case LayoutElementContentType.PODCAST_LIST:
        this.openContentPodcastListModal(layoutItem as LayoutElementContentPodcastList, parent);
        break;
      case LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT:
        if (this.domainKey === 'pesti_sracok' && (parent?.widthDesktopRecursive || this.activeElement.widthDesktopRecursive) < 5) {
          this.sharedService.showNotification('warning', 'Ez az elem csak 1/3-as elrendezésnél nagyobb elrendezéshez adható hozzá');
          break;
        }
        this.openContentArticlesWithVideoContent(layoutItem as LayoutElementContentArticlesWithVideoContent, parent);
        break;
      case LayoutElementContentType.ARTICLES_WITH_PODCAST_CONTENT:
        if (this.domainKey === 'pesti_sracok' && (parent?.widthDesktopRecursive || this.activeElement.widthDesktopRecursive) < 5) {
          this.sharedService.showNotification('warning', 'Ez az elem csak 1/3-as elrendezésnél nagyobb elrendezéshez adható hozzá');
          break;
        }
        this.openContentArticlesWithPodcastContent(layoutItem as LayoutElementContentArticlesWithPodcastContent, parent);
        break;
      case LayoutElementContentType.PODCAST_ARTICLE_LIST:
        this.openContentPodcastArticleList(layoutItem as LayoutElementContentArticle, parent);
        break;
      case LayoutElementContentType.SPOTLIGHT:
        this.openContentSpotlight(layoutItem as LayoutElementContentSpotlight, parent);
        break;
      case LayoutElementContentType.TURPI_BOX:
        this.openContentTurpiBox(layoutItem as LayoutElementContentTurpiBox, parent);
        break;
      case LayoutElementContentType.DAILY_MENU:
        if (
          (parent?.widthDesktopRecursive || this.activeElement.widthDesktopRecursive) !== 3 &&
          this.type !== LayoutPageType.SIDEBAR &&
          this.type !== LayoutPageType.COLUMNSIDEBAR
        ) {
          this.sharedService.showNotification('warning', 'Ez az elem csak 1/4 elrendezéshez adható hozzá');
          break;
        }
        this.openContentDailyMenu(layoutItem as LayoutElementContentDailyMenu, parent);
        break;
      case LayoutElementContentType.OFFER_BOX:
        this.openContentOfferBox(layoutItem as LayoutElementContentOfferBox, parent);
        break;
      case LayoutElementContentType.MAESTRO_BOX:
        if ((parent?.widthDesktopRecursive || this.activeElement.widthDesktopRecursive) !== 12) {
          this.sharedService.showNotification('warning', 'Ez az elem csak 1/1 elrendezéshez adható hozzá');
          break;
        }
        this.openContentMaestroBox(layoutItem as LayoutElementContentMaestroBox, parent);
        break;
      case LayoutElementContentType.PUBLIC_AUTHORS:
        if ((parent?.widthDesktopRecursive || this.activeElement.widthDesktopRecursive) !== 12) {
          this.sharedService.showNotification('warning', 'Ez az elem csak 1/1 elrendezéshez adható hozzá');
          break;
        }
        this.openContentPublicAuthors(layoutItem as LayoutElementContentPublicAuthors, parent);
        break;
      case LayoutElementContentType.HIGHLIGHTED_SELECTION:
        this.openContentHighlightedSelection(layoutItem as LayoutElementContentHighlightedSelection, parent);
        break;
      case LayoutElementContentType.SELECTION:
        this.openContentSelection(layoutItem as LayoutElementContentSelection, parent);
        break;
      case LayoutElementContentType.HIGHLIGHTED_SELECTION:
        this.openContentHighlightedSelection(layoutItem as LayoutElementContentHighlightedSelection, parent);
        break;
      case LayoutElementContentType.PDF_BOX:
        this.openContentPdfBoxModal(layoutItem as LayoutElementContentPdfBox, parent);
        break;
      case LayoutElementContentType.DOSSIER_REPEATER:
        this.openContentDossierRepeaterModal(layoutItem as LayoutElementContentDossierRepeater, parent);
        break;
      case LayoutElementContentType.WEEKLY_NEWSPAPER_BOX:
        this.openContentWeeklyNewspaperModal(layoutItem as LayoutElementContentWeeklyNewspaper, parent);
        break;
      case LayoutElementContentType.MANUAL_ARTICLE:
        this.openContentManualArticleModal(layoutItem as LayoutElementContentManualArticle, parent);
        break;
      case LayoutElementContentType.MANUAL_OPINION:
        this.openContentManualOpinionModal(layoutItem as LayoutElementContentManualOpinion, parent);
        break;
      case LayoutElementContentType.KULTUR_NEMZET:
        this.openContentKulturNemzetModal(layoutItem as LayoutElementContentArticle, parent);
        break;
      case LayoutElementContentType.RSS_BOX:
        this.openContentRssBoxModal(layoutItem as LayoutElementContentRssBox, parent);
        break;
      case LayoutElementContentType.MAP_RECOMMENDATIONS:
        this.openContentMapRecommendations(layoutItem as LayoutElementContentMapRecommendations, parent);
        break;
      case LayoutElementContentType.HERO:
        this.openContentHero(layoutItem as LayoutElementContentHero, parent);
        break;
      case LayoutElementContentType.MEDICAL_METEOROLOGY:
        this.openContentMedicalMeteorology(layoutItem as LayoutElementContentMedicalMeteorology, parent);
        break;
      case LayoutElementContentType.TWELVE_DAYS_FORECAST:
        this.openContentTwelveDaysForecast(layoutItem as LayoutElementContentTwelveDaysForecast, parent);
        break;
      case LayoutElementContentType.DETECTIONS:
        this.openContentDetections(layoutItem as LayoutElementContentDetections, parent);
        break;
      case LayoutElementContentType.TENYEK_BOX:
        this.openContentTenyekBox(layoutItem as LayoutElementContentTenyekBox, parent);
        break;
      case LayoutElementContentType.IMAGE_MAP_LIST:
        this.openContentImageMapListModal(layoutItem as LayoutElementContentImageMapList, parent);
        break;
      case LayoutElementContentType.DRAWN_MAP_LIST:
        this.openContentDrawnMapListModal(layoutItem as LayoutElementContentDrawnMapList, parent);
        break;
      case LayoutElementContentType.NEWS_FEED:
        this.openContentNewsFeedModal(layoutItem as LayoutElementContentDossier, parent);
        break;
      case LayoutElementContentType.DATA_BANK:
        this.openContentDataBankModal(layoutItem as LayoutElementContentDataBank, parent);
        break;
      case LayoutElementContentType.CONFERENCE:
        this.openContentConferenceModal(layoutItem as LayoutElementContentConference, parent);
        break;
      case LayoutElementContentType.LEAD_EDITORS:
        this.openLeadEditorsModal(layoutItem as LayoutElementContent, parent);
        break;
      case LayoutElementContentType.UPCOMING_MATCHES:
        this.openContentUpcomingMatchesModal(layoutItem as LayoutElementContentUpcomingMatches, parent);
        break;
      case LayoutElementContentType.TEAMS:
        this.openTeamsModal(layoutItem as LayoutElementContent, parent);
        break;
      case LayoutElementContentType.DAILY_PROGRAM:
        this.openDailyProgramModal(layoutItem as LayoutElementContent, parent);
        break;
      case LayoutElementContentType.TRIP_BOX:
        this.openContentTripBoxModal(layoutItem as LayoutElementContentTripBox, parent);
        break;
      case LayoutElementContentType.GP_NEWS_BOX:
        this.openContentGPNewsBoxModal(layoutItem as LayoutElementContentGPNewsBox, parent);
        break;
      case LayoutElementContentType.BRANDING_BOX_ARTICLE:
        this.openContentBrandingBoxArticleModal(layoutItem as LayoutElementContentBrandingBoxArticle, parent);
        break;
      case LayoutElementContentType.LIVE_BAR:
        this.openContentLiveBarBox(layoutItem as LayoutElementContentLiveBar, parent);
        break;
      case LayoutElementContentType.MORE_ARTICLES:
        this.openContentMoreArticlesModal(layoutItem as LayoutElementContentMoreArticles, parent);
        break;
      case LayoutElementContentType.RELATED_ARTICLES:
        this.openContentRelatedArticlesModal(layoutItem as LayoutElementContentRelatedArticles, parent);
        break;
      case LayoutElementContentType.GALLERY_ARTICLE_LIST:
        if (this.domainKey === 'pesti_sracok' && (parent?.widthDesktopRecursive || this.activeElement.widthDesktopRecursive) < 5) {
          this.sharedService.showNotification('warning', 'Ez az elem csak 1/3-as elrendezésnél nagyobb elrendezéshez adható hozzá');
          break;
        }
        this.openGalleryArticleListModal(layoutItem as LayoutElementContent, parent);
        break;
      case LayoutElementContentType.SPORT_BLOCK:
        this.openContentSportBlockModal(layoutItem as LayoutElementContentSportBlock, parent);
        break;
      case LayoutElementContentType.CHAMPIONSHIP_TABLE:
        this.openContentChampionshipTableModal(layoutItem as LayoutElementContentChampionshipTable, parent);
        break;
      case LayoutElementContentType.SOROZATVETO:
        this.openContentSorozatvetoModal(layoutItem as LayoutElementContentSorozatveto, parent);
        break;
      case LayoutElementContentType.WRITE_TO_US:
        this.openContentWriteToUsModal(layoutItem as LayoutElementContent, parent);
        break;
      case LayoutElementContentType.MEDIA_PANEL:
        this.openContentMediaPanelModal(layoutItem as LayoutElementContentMediaPanel, parent);
        break;
      case LayoutElementContentType.MOST_VIEWED:
        this.openContentMostViewedModal(layoutItem as LayoutElementContentMostViewed, parent);
        break;
      case LayoutElementContentType.TOP_STORIES:
        this.openContentTopStoriesModal(layoutItem as LayoutElementContentTopStories, parent);
        break;
      case LayoutElementContentType.RECIPE_CATEGORY_SELECT:
        this.openContentRecipeCategorySelectModal(layoutItem as LayoutElementContentRecipeCategorySelect, parent);
        break;
      case LayoutElementContentType.WEEKLY_MENU:
        this.openWeeklyMenuModal(layoutItem, parent);
        break;
      case LayoutElementContentType.TEXT_BOX:
        this.openContentTextBoxModal(layoutItem as LayoutElementContentTextBox, parent);
        break;
      case LayoutElementContentType.TURPI_CARD:
        this.openContentTurpiCardModal(layoutItem as LayoutElementContentTurpiCard, parent);
        break;
      case LayoutElementContentType.RECIPE_SWIPER:
        this.openContentRecipeSwiperModal(layoutItem as LayoutElementContentRecipeSwiper, parent);
        break;
      case LayoutElementContentType.INGREDIENT:
        this.openContentIngredientModal(layoutItem as LayoutElementContentIngredient, parent);
        break;
      case LayoutElementContentType.GUARANTEE_BOX:
        this.openContentGuaranteeBoxModal(layoutItem as LayoutElementContentGuaranteeBox, parent);
        break;
      case LayoutElementContentType.AUTHOR:
        this.openContentAuthorModal(layoutItem as LayoutElementContent, parent);
        break;
      case LayoutElementContentType.RECIPE:
        this.openContentRecipeCardModal(layoutItem as LayoutElementContentRecipe, parent);
        break;
      case LayoutElementContentType.ARTICLE_BLOCK:
        this.openContentArticleBlockModal(layoutItem as LayoutElementContentArticleBlock, parent);
        break;
      case LayoutElementContentType.DID_YOU_KNOW:
        this.openContentDidYouKnowModal(layoutItem as LayoutElementContentDidYouKnow, parent);
        break;
      case LayoutElementContentType.BLOCK_SEPARATOR:
        this.openBlockSeparatorModal(layoutItem as LayoutElementContentBlockSeparator, parent);
        break;
      case LayoutElementContentType.SPONSORED_ARTICLE_BOX:
        this.openSponsoredArticleBoxModal(layoutItem as LayoutElementContentSponsoredArticleBox, parent);
        break;
      case LayoutElementContentType.TELEKOM_VIVICITTA:
        this.openContentTelekomVivicittaModal(layoutItem as LayoutElementContentTelekomVivicitta, parent);
        break;
      case LayoutElementContentType.EB_NEWS:
        this.openContentEbNewsModal(layoutItem as LayoutElementContentEBNews, parent);
        break;
      case LayoutElementContentType.COUNTDOWN_BOX:
        this.openContentCountdownBoxModal(layoutItem as LayoutElementContentCountdownBox, parent);
        break;
      case LayoutElementContentType.ELECTIONS_BOX:
        this.openContentElectionsBoxModal(layoutItem as LayoutElementContentElectionsBox, parent);
        break;
      case LayoutElementContentType.EB_COUNTDOWN_BLOCK_TITLE:
        this.openContentEbCountdownBlockTitleModal(layoutItem as LayoutElementContentEbCountdownBlockTitle, parent);
        break;
      case LayoutElementContentType.EB_SINGLE_ELIMINATION:
        this.openContentEbSingleEliminationModal(layoutItem as LayoutElementContent, parent);
        break;
      case LayoutElementContentType.OLIMPIA_COUNTDOWN_BLOCK_TITLE:
        this.openContentOlimpiaCountdownBlockTitleModal(layoutItem as LayoutElementContentOlimpiaCountdownBlockTitle, parent);
        break;
      case LayoutElementContentType.OLIMPIA_NEWS:
        this.openContentOlimpiaNewsModal(layoutItem as LayoutElementContentOlimpiaNews, parent);
        break;
      case LayoutElementContentType.OLIMPIA_HUNGARIAN_COMPETITIONS:
        this.openContentOlimpiaHungarianCompetitionsModal(layoutItem as LayoutElementContentOlimpiaHungarianCompetitions, parent);
        break;
      case LayoutElementContentType.OLIMPIA_HUNGARIAN_TEAM:
        this.openContentOlimpiaHungarianTeamModal(layoutItem as LayoutElementContentOlimpiaHungarianTeam, parent);
        break;
      case LayoutElementContentType.OLIMPIA_RESULTS_BLOCK:
        this.openContentOlimpiaResultsBlockModal(layoutItem as LayoutElementContentOlimpiaResultsBlock, parent);
        break;
      case LayoutElementContentType.OLIMPIA_ARTICLES_WITH_PODCAST_CONTENT:
        this.openContentOlimpiaArticlesWithPodcastContent(layoutItem as LayoutElementContentOlimpiaArticlesWithPodcastContent, parent);
        break;
      case LayoutElementContentType.OLIMPIA_LARGE_NAVIGATOR:
        this.openContentOlimpiaLargeNavigatorModal(layoutItem as LayoutElementContentOlimpiaLargeNavigator, parent);
        break;
      case LayoutElementContentType.EXPERIENCE_GIFT:
        this.openContentExperienceGiftModal(layoutItem as LayoutElementContentExperienceGift, parent);
        break;
      case LayoutElementContentType.EVENT_CALENDAR:
        this.openContentEventCalendarModal(layoutItem as LayoutElementContentEventCalendar, parent);
        break;
      case LayoutElementContentType.GASTRO_OCCASION_RECOMMENDER:
        this.openContentGastroOccasionRecommenderModal(layoutItem as LayoutElementContentGastroOccasionRecommender, parent);
        break;
      case LayoutElementContentType.GASTRO_EXPERIENCE_RECOMMENDATION:
        this.openContentGastroExperienceRecommendationModal(layoutItem as LayoutElementContentGastroExperienceRecommendation, parent);
        break;
      case LayoutElementContentType.GASTRO_THEMATIC_RECOMMENDER:
        this.openContentThematicRecommenderModal(layoutItem as LayoutElementContentGastroThematicRecommender, parent);
        break;
      case LayoutElementContentType.GASTRO_EXPERIENCE_OCCASION_SWIPER:
        this.openContentGastroExperienceOccasionSwiperModal(layoutItem as LayoutElementContentGastroExperienceOccasionSwiper, parent);
        break;
      case LayoutElementContentType.TOP_RANKING_GLOSSARY:
        this.openTopRankingGlossaryModal(layoutItem, parent);
        break;
      case LayoutElementContentType.TOP_TEN_TAGS:
        this.openContentTopTenTagsModal(layoutItem as LayoutElementContentTopTenTags, parent);
        break;
      case LayoutElementContentType.TOPIC_SUGGESTION:
        this.openContentTopicSuggestionModal(layoutItem as LayoutElementContentTopicSuggestion, parent);
        break;
      case LayoutElementContentType.SECRET_DAYS_CALENDAR:
        this.openContentSecretDaysCalendarModal(layoutItem as LayoutElementContentSecretDaysCalendar, parent);
        break;
      case LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX:
        this.openContentConfigurableSponsoredBoxModal(layoutItem as LayoutElementContentConfigurableSponsoredBox, parent);
        break;
      case LayoutElementContentType.STAR_BIRTHS:
        this.openStarBirthsModal(layoutItem as LayoutElementContent, parent);
        break;
      case LayoutElementContentType.LATEST_AND_MOST_READ_ARTICLES:
        this.openLatestAndMostReadArticles(layoutItem as LayoutElementContent, parent);
        break;
      case LayoutElementContentType.SUB_COLUMNS:
        this.openSubColumns(layoutItem as LayoutElementContent, parent);
        break;
      case LayoutElementContentType.ASTRONET_BRANDING_BOX:
        this.openAstronetBrandingBox(layoutItem as LayoutElementContent, parent);
        break;
      case LayoutElementContentType.ASTRONET_COLUMNS:
        this.openAstronetColumns(layoutItem as LayoutElementContent, parent);
        break;
      case LayoutElementContentType.TOP_COMMENTED_ARTICLES:
        this.openTopCommentedArticles(layoutItem as LayoutElementContent, parent);
    }
  }

  swapChildElements(event, parent: LayoutElementRow | LayoutElementColumn, index1: number, index2: number) {
    event.stopPropagation();

    // root row elements are being swapped
    if (!parent) {
      if (index1 >= this.rootElements.length || index2 >= this.rootElements.length || index1 < 0 || index2 < 0) {
        return;
      }

      const temp = JSON.parse(JSON.stringify(this.rootElements[index1]));
      this.rootElements[index1] = this.rootElements[index2];
      this.rootElements[index2] = temp;
    } else {
      if (index1 >= parent.elements.length || index2 >= parent.elements.length || index1 < 0 || index2 < 0) {
        return;
      }

      const temp = JSON.parse(JSON.stringify(parent.elements[index1]));
      parent.elements[index1] = parent.elements[index2];
      parent.elements[index2] = temp;
    }
  }

  areColumnWidthsEqual(cols1: number[], cols2: number[]): boolean {
    if (cols1.length !== cols2.length) {
      return false;
    }

    return cols1.every((col, i) => cols2[i] === col);
  }

  private findLayoutElementParentArray(
    item: LayoutElementColumn | LayoutElementRow,
    array: (LayoutElementRow | LayoutElementColumn | LayoutElementContent)[]
  ): (LayoutElementRow | LayoutElementContent | LayoutElementColumn)[] | undefined {
    if (array.includes(item)) {
      return array;
    }
    for (const node of array) {
      if ((node.type === LayoutElementType.ROW || node.type === LayoutElementType.COLUMN) && node.elements) {
        const child = this.findLayoutElementParentArray(item, node.elements);
        if (child) {
          return child;
        }
      }
    }
  }

  // Checks if eligible to add element
  private canOpenModal(modifyItem: LayoutElementContent | LayoutElementRow | LayoutElementColumn): boolean {
    if (!modifyItem) {
      if (!this.activeElement || !this.canAddContent(!!modifyItem)) {
        return false;
      }
    }
    return true;
  }

  private getCommonModalParams(component: NzSafeAny, modifyItem: boolean, title: string): Partial<ModalOptions> {
    return {
      nzContent: component,
      nzTitle: `${title} ${modifyItem ? 'módosítása' : 'hozzáadása'}`,
      nzViewContainerRef: this.viewContainerRef,
      nzWidth: 800,
    };
  }

  private getCommonNzComponentParams(modifyItem: LayoutElementContent | LayoutElementRow | LayoutElementColumn): {
    hideMobile: boolean;
    withBlockTitle: boolean;
  } {
    return {
      hideMobile: modifyItem?.hideMobile ?? false,
      withBlockTitle: modifyItem?.withBlockTitle ?? false,
    };
  }

  private getCommonContentElementData<T extends LayoutElementContent>(
    modifyItem: T,
    configurable: boolean = false,
    contentLength: number = 1
  ): Pick<T, 'id' | 'type' | 'contentLength' | 'configurable'> {
    return {
      id: modifyItem?.id || this.generateId(),
      type: LayoutElementType.CONTENT,
      contentLength,
      configurable,
    } as Pick<T, 'id' | 'type' | 'contentLength' | 'configurable'>;
  }

  private updateAndEmitEvent<T extends LayoutElementContent>(
    modifyItem: T,
    newElement: T,
    parent: LayoutElementRow | LayoutElementColumn,
    type: ContentElementItemType = null
  ): void {
    this.updateParentElement(modifyItem, newElement, parent);
    this.structureItemChange(newElement.id, !modifyItem, newElement.contentType, type);
  }

  // Updates or adds element inside the selected element
  private updateParentElement<T extends LayoutElementContent>(modifyItem: T, newElement: T, parent: LayoutElementRow | LayoutElementColumn): void {
    if (modifyItem) {
      const elemIndex = parent.elements.indexOf(modifyItem);
      parent.elements[elemIndex] = newElement;
    } else {
      if (this.newItemInsertIndex >= 0 && this.newItemInsertIndex < this.activeElement.elements.length) {
        this.activeElement.elements.splice(this.newItemInsertIndex, 0, newElement);
        this.newItemInsertIndex = -1;
      } else {
        this.newItemInsertIndex = -1;
        this.activeElement.elements.push(newElement);
      }
    }
  }

  private structureItemChange(id: string, isCreate: boolean, contentType: LayoutElementContentType, itemType: ContentElementItemType) {
    this.structureChanged.emit();
    this.structureItemChanged.emit({
      id,
      isCreate,
      contentType,
      itemType,
    });
  }

  mapTypeToAdType(type: string): string {
    switch (type) {
      case 'HomePage':
        return 'main_page';
      case 'Column':
        return 'column_';
      case 'Opinion':
        return 'opinion';
      case 'Sidebar':
        return 'sidebar';
      case 'ColumnSidebar':
        return 'sidebar';
      case 'CustomBuiltPage':
        return 'custom_built_page';
      default:
        return 'invalid';
    }
  }

  @HostListener('dragover', ['$event']) enableComponentToBeDropTarget(event: DragEvent) {
    event.preventDefault();
  }

  dropped(event: CdkDragDrop<any>) {
    //When we drag a new content element
    if (event.item.data?.type === 'content' && event.item.data?.hasOwnProperty('init')) {
      this.activeElement = event.container.data.parent;
      this.newItemInsertIndex = event.currentIndex;
      this.openConfigModal(event.item.data.init);
      return;
    }

    //When we drag a new row
    if (event.item.data?.type === 'row' && event.item.data?.hasOwnProperty('init')) {
      this.activeElement = event.container.data.parent;
      this.newItemInsertIndex = event.currentIndex;
      this.openRowModal();
      return;
    }

    //When we move items or reorder them.
    if (event.previousContainer.data) {
      if (event.previousContainer.data !== event.container.data) {
        transferArrayItem(event.previousContainer.data.elements, event.container.data.elements, event.previousIndex, event.currentIndex);
        this.recalculateWidthDesktopRecursive([event.container.data.elements[event.currentIndex]], event.container.data.parent?.widthDesktopRecursive);
      } else {
        moveItemInArray(event.container.data.elements, event.previousIndex, event.currentIndex);
      }
    }
  }

  private recalculateWidthDesktopRecursive(elements: LayoutElement[], parentWidthDesktopRecursive = 12) {
    if (!elements?.length) {
      return;
    }
    elements.forEach((element: LayoutElementRow | LayoutElementColumn) => {
      if (element.type !== LayoutElementType.ROW && element.type !== LayoutElementType.COLUMN) {
        return;
      }
      const elementWidth = !element.widthDesktop && element.type === LayoutElementType.ROW ? 12 : element.widthDesktop;
      element.widthDesktopRecursive = (parentWidthDesktopRecursive * elementWidth) / 12;
      this.recalculateWidthDesktopRecursive(element.elements, element.widthDesktopRecursive);
    });
  }

  getKoponyegMasonryMockData() {
    return koponyegDetectionCardSWithImgMock;
  }

  toolboxContentItemDragStarted(event: CdkDragStart, dataList, contentType, index) {
    dataList.splice(index, 0, contentType);
  }

  toolboxContentItemDropped(event, dataList, index) {
    dataList.splice(index, 1);
  }

  layoutItemRowDragStarted(event: CdkDragStart) {
    this.dragDropAddRowButtonPlaceholderVisible = true;
  }

  layoutItemRowDragDropped(event) {
    this.dragDropAddRowButtonPlaceholderVisible = false;
  }

  mobileOrderValueAlreadyExists(modifyItem: LayoutElementContent, componentInstance: any): boolean {
    const mobileOrder = componentInstance?.['mobilOrderControl']?.value;

    if (!this.useMobileOrder() || componentInstance?.['hideMobile'] || mobileOrder === null) {
      componentInstance?.['mobilOrderControl'].setValue(null);
      return false;
    }

    const rootElementsAsString = JSON.stringify(this.rootElements);
    const regExp = new RegExp(`mobileOrder\\\":${mobileOrder},`, 'gi');
    const matchResult = [...rootElementsAsString.matchAll(regExp)];

    if ((modifyItem?.mobileOrder === mobileOrder && matchResult.length > 1) || (modifyItem?.mobileOrder !== mobileOrder && matchResult.length)) {
      this.sharedService.showNotification('error', 'Ez a mobil sorszám másik komponensnél már használatban van, kérlek adj meg egy másik számot!');
      return true;
    }
    return false;
  }

  useMobileOrder(): boolean {
    return this.domainKey == 'mandiner' && this.type == LayoutPageType.HOME;
  }

  getMobilorder(modifyItem: LayoutElementContent | LayoutElementColumn) {
    return {
      useMobileOrder: this.useMobileOrder(),
      mobileOrder: this.useMobileOrder() ? (modifyItem?.mobileOrder ? modifyItem.mobileOrder : null) : undefined,
    };
  }

  searchObjectArray(layoutElements: LayoutElement[], keys: string[], values: any[]) {
    if (keys.length !== values.length) {
      throw new Error('Keys and values length must be equals');
    }
    let result = [];

    layoutElements.forEach((layoutElement) => {
      if (keys.every((key, index) => layoutElement[key] === values[index])) {
        result.push(layoutElement);
      }
      if (layoutElement.type === LayoutElementType.COLUMN || layoutElement.type === LayoutElementType.ROW) {
        result = result.concat(this.searchObjectArray((layoutElement as LayoutElementRow).elements, keys, values));
      }
    });

    return result;
  }
}
