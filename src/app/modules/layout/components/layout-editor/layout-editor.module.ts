import { DragDropModule } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { FormModule } from '@external/form';
import { FormControlsModule } from 'src/app/shared/modules/form-controls/form-controls.module';
import { NgZorroModule } from 'src/app/shared/modules/ng-zorro/ng-zorro.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { ToolboxModule } from '../../toolbox/toolbox.module';
import { ModalColumnComponent } from './components/modals/modal-column/modal-column.component';
import { ModalContentBestRecommenderComponent } from './components/modals/modal-content-best-recommender/modal-content-best-recommender.component';
import { ModalContentSzakikeresoComponent } from './components/modals/modal-content-szakikereso/modal-content-szakikereso.component';
import { ModalContentWazeComponent } from './components/modals/modal-content-waze/modal-content-waze.component';
import { ModalRowComponent } from './components/modals/modal-row/modal-row.component';
import { LayoutEditorComponent } from './layout-editor.component';
import { ModalContentArticleComponent } from './components/modals/modal-content-article/modal-content-article.component';
import { ModalContentAdComponent } from './components/modals/modal-content-ad/modal-content-ad.component';
import { ModalContentOpinionComponent } from './components/modals/modal-content-opinion/modal-content-opinion.component';
import { ModalContentVideoComponent } from './components/modals/modal-content-video/modal-content-video.component';
import { ModalContentDossierComponent } from './components/modals/modal-content-dossier/modal-content-dossier.component';
import { ModalContentNewsletterComponent } from './components/modals/modal-content-newsletter/modal-content-newsletter.component';
import { ModalContentGongNewsletterComponent } from './components/modals/modal-content-gong-newsletter/modal-content-gong-newsletter.component';
import { ModalContentStocksChartComponent } from './components/modals/modal-content-stocks-chart/modal-content-stocks-chart.component';
import { ModalContentVideoBlockComponent } from './components/modals/modal-content-video-block/modal-content-video-block.component';
import { ModalContentFreshNewsComponent } from './components/modals/modal-content-fresh-news/modal-content-fresh-news.component';
import { ModalContentLinkListComponent } from './components/modals/modal-content-link-list/modal-content-link-list.component';
import { ModalContentVoteComponent } from './components/modals/modal-content-vote/modal-content-vote.component';
import { ModalContentVisegradPostComponent } from './components/modals/modal-content-visegrad-post/modal-content-visegrad-post.component';
import { ModalContentCultureNationComponent } from './components/modals/modal-content-culture-nation/modal-content-culture-nation.component';
import { ModalContentHtmlEmbedComponent } from './components/modals/modal-content-html-embed/modal-content-html-embed.component';
import { ModalContentGalleryComponent } from './components/modals/modal-content-gallery/modal-content-gallery.component';
import { ModalContentBrandingBoxComponent } from './components/modals/modal-content-branding-box/modal-content-branding-box.component';
import { ModalContentNewspaperComponent } from './components/modals/modal-content-newspaper/modal-content-newspaper.component';
import { ModalContentSocialMediaComponent } from './components/modals/modal-content-social-media/modal-content-social-media.component';
import { ModalContentAstrologyComponent } from './components/modals/modal-content-astrology/modal-content-astrology.component';
import { ModalContentTabsComponent } from './components/modals/modal-content-tabs/modal-content-tabs.component';
import { ModalContentBreakingComponent } from './components/modals/modal-content-breaking/modal-content-breaking.component';
import { ModalContentOpinionPagesComponent } from './components/modals/modal-content-opinion-pages/modal-content-opinion-pages.component';
import { ModalContentArticlePagesComponent } from './components/modals/modal-content-article-pages/modal-content-article-pages.component';
import { LayoutContentNamePipe } from '../../pipes/layout-content-name.pipe';
import { ModalContentAgroKepComponent } from './components/modals/modal-content-agro-kep/modal-content-agro-kep.component';
import { ModalContentAgroKepListComponent } from './components/modals/modal-content-agro-kep-list/modal-content-agro-kep-list.component';
import { ModalContentPrBlockComponent } from './components/modals/modal-content-pr-block/modal-content-pr-block.component';
import { ModalContentNoteComponent } from './components/modals/modal-content-note/modal-content-note.component';
import { ModalContentWysiwygComponent } from './components/modals/modal-content-wysiwyg/modal-content-wysiwyg.component';
import { ModalContentImageComponent } from './components/modals/modal-content-image/modal-content-image.component';
import { ModalContentBlogComponent } from './components/modals/modal-content-blog/modal-content-blog.component';
import { ModalContentIngatlanbazarComponent } from './components/modals/modal-content-ingatlanbazar/modal-content-ingatlanbazar.component';
import { ModalContentBrandingBoxExternalComponent } from './components/modals/modal-content-branding-box-external/modal-content-branding-box-external.component';
import { ModalContentTagBlockComponent } from './components/modals/modal-content-tag-block/modal-content-tag-block.component';
import { ModalContentTrendingTagsBlockComponent } from './components/modals/modal-content-trending-tags-block/modal-content-trending-tags-block.component';
import { ModalContentShortNewsComponent } from './components/modals/modal-content-short-news/modal-content-short-news.component';
import { ModalContentQuizComponent } from './components/modals/modal-content-quiz/modal-content-quiz.component';
import { ModalContentProgramComponent } from './components/modals/modal-content-program/modal-content-program.component';
import { ModalContentKoponyegComponent } from './components/modals/modal-content-koponyeg/modal-content-koponyeg.component';
import { ModalContentAstronetHoroszkopComponent } from './components/modals/modal-content-astronet-horoszkop/modal-content-astronet-horoszkop.component';
import { ModalContentAstronetJoslasComponent } from './components/modals/modal-content-astronet-joslas/modal-content-astronet-joslas.component';
import { DragAndDropLayoutDirective } from './drag-and-drop/drag-and-drop-layout.directive';
import { ModalContentHelloBudapestComponent } from './components/modals/modal-content-hello-budapest/modal-content-hello-budapest.component';
import { ModalContentFinalCountdownComponent } from './components/modals/modal-content-final-countdown/modal-content-final-countdown.component';
import { ModalContentFastNewsComponent } from './components/modals/modal-content-fast-news/modal-content-fast-news.component';
import { ModalContentIngatlanbazarConfigurableComponent } from './components/modals/modal-content-ingatlanbazar-configurable/modal-content-ingatlanbazar-configurable.component';
import { ModalContentIngatlanbazarSearchComponent } from './components/modals/modal-content-ingatlanbazar-search/modal-content-ingatlanbazar-search.component';
import { ModalContentDossierListComponent } from './components/modals/modal-content-dossier-list/modal-content-dossier-list.component';
import { ModalContentBroadcastRecommenderComponent } from './components/modals/modal-content-broadcast-recommender/modal-content-broadcast-recommender.component';
import { ModalContentSongTopListComponent } from './components/modals/modal-content-song-top-list/modal-content-song-top-list.component';
import { ModalContentOpinionListComponent } from './components/modals/modal-content-opinion-list/modal-content-opinion-list.component';
import { ModalContentPodcastListComponent } from './components/modals/modal-content-podcast-list/modal-content-podcast-list.component';
import { ModalContentCategoryStepperComponent } from './components/modals/modal-content-category-stepper/modal-content-category-stepper.component';
import { ModalContentArticlesWithVideoComponent } from './components/modals/modal-content-articles-with-video-content/modal-content-articles-with-video.component';
import { ModalContentPdfBoxComponent } from './components/modals/modal-content-pdf-box/modal-content-pdf-box.component';
import { ModalContentWeeklyNewspaperComponent } from './components/modals/modal-content-weekly-newspaper/modal-content-weekly-newspaper.component';
import { ModalContentDossierRepeaterComponent } from './components/modals/modal-content-dossier-repeater/modal-content-dossier-repeater.component';
import { ModalContentManualArticleComponent } from './components/modals/modal-content-manual-article/modal-content-manual-article.component';
import { ModalContentManualOpinionComponent } from './components/modals/modal-content-manual-opinion/modal-content-manual-opinion.component';
import { DragAndDropExplicitListDirective } from './drag-and-drop/drag-and-drop-explicit-list.directive';
import { ModalContentKulturnemzetComponent } from './components/modals/modal-content-kulturnemzet/modal-content-kulturnemzet.component';
import { ModalContentArticlesWithPodcastContentComponent } from './components/modals/modal-content-articles-with-podcast-content/modal-content-articles-with-podcast-content.component';
import { ModalContentRssBoxComponent } from './components/modals/modal-content-rss-box/modal-content-rss-box.component';
import { ValidateRssDirective } from './components/modals/modal-content-rss-box/validate-rss.directive';
import { ModelContentMapRecommendationsComponent } from './components/modals/modal-content-map-recommendations/model-content-map-recommendations.component';
import { ModalContentTenyekBoxComponent } from './components/modals/modal-content-tenyek-box/modal-content-tenyek-box.component';
import { ModalContentHeroComponent } from './components/modals/modal-content-hero/modal-content-hero.component';
import { ModalContentMedicalMeteorologyComponent } from './components/modals/modal-content-medical-meteorolgy/modal-content-medical-meteorology.component';
import { ModalContentTwelveDaysForecastComponent } from './components/modals/modal-content-twelve-days-forecast/modal-content-twelve-days-forecast.component';
import { ModalContentDetectionsComponent } from './components/modals/modal-content-detections/modal-content-detections.component';
import { ModalContentImageMapListComponent } from './components/modals/modal-content-image-map-list/modal-content-image-map-list.component';
import { ModalContentHeaderComponent } from './components/modals/modal-content-header/modal-content.header.component';
import { ModalContentDataBankComponent } from './components/modals/modal-content-data-bank/modal-content-data-bank.component';
import { ModalContentUpcomingMatchesComponent } from './components/modals/modal-content-upcoming-matches/modal-content-upcoming-matches.component';
import { ModalContentTripBoxComponent } from './components/modals/modal-content-trip-box/modal-content-trip-box.component';
import { ModalContentChampionshipTableComponent } from './components/modals/modal-content-championship-table/modal-content-championship-table.component';
import { ModalContentSorozatvetoComponent } from './components/modals/modal-content-sorozatveto/modal-content-sorozatveto.component';
import { ModalContentDrawnMapListComponent } from './components/modals/modal-content-drawn-map-list/modal-content-drawn-map-list.component';
import { ModalContentMostViewedComponent } from './components/modals/modal-content-most-viewed/modal-content-most-viewed.component';
import { ModalContentTopStoriesComponent } from './components/modals/modal-content-top-stories/modal-content-top-stories.component';
import { ModalContentMediaPanelComponent } from './components/modals/modal-content-media-panel/modal-content-media-panel.component';
import { ModalContentBaseComponent } from './components/modals/modal-content-base/modal-content-base.component';
import { ModalContentLatestNewsComponent } from './components/modals/modal-content-latest-news/modal-content-latest-news.component';
import { ModalContentSpotlightComponent } from './components/modals/modal-content-spotlight/modal-content-spotlight.component';
import { ModalContentGpNewsBoxComponent } from './components/modals/modal-content-gp-news-box/modal-content-gp-news-box.component';
import { ModalContentSportBlockComponent } from './components/modals/modal-content-sport-block/modal-content-sport-block.component';
import { ModalContentRecipeCategoriesSelectComponent } from './components/modals/modal-content-recipe-categories-select/modal-content-recipe-categories-select.component';
import { ModalContentMoreArticlesComponent } from './components/modals/modal-content-more-articles/modal-content-more-articles.component';
import { ModalContentArticleSliderComponent } from './components/modals/modal-content-article-slider/modal-content-article-slider.component';
import { ModalContentTextBoxComponent } from './components/modals/modal-content-text-box/modal-content-text-box.component';
import { ModalContentTurpiBoxComponent } from './components/modals/modal-content-turpi-box/modal-content-turpi-box.component';
import { ModalContentAuthorComponent } from './components/modals/modal-content-author/modal-content-author.component';
import { ModalContentTurpiCardComponent } from './components/modals/modal-content-turpi-card/modal-content-turpi-card.component';
import { ModalContentIngredientComponent } from './components/modals/modal-content-ingredient/modal-content-ingredient.component';
import { ModalContentRecipeCardComponent } from './components/modals/modal-content-recipe-card/modal-content-recipe-card.component';
import { ModalContentRecipeSwiperComponent } from './components/modals/modal-content-recipe-swiper/modal-content-recipe-swiper.component';
import { ModalContentRelatedArticlesComponent } from './components/modals/modal-content-related-articles/modal-content-related-articles.component';
import { ModalContentLiveBarComponent } from './components/modals/modal-content-live-bar/modal-content-live-bar.component';
import { ModalContentGuaranteeBoxComponent } from './components/modals/modal-content-guarantee-box/modal-content-guarantee-box.component';
import { ModalContentNewsFeedComponent } from './components/modals/modal-content-news-feed/modal-content-news-feed.component';
import { ModalContentMinuteToMinuteComponent } from './components/modals/modal-content-minute-to-minute/modal-content-minute-to-minute.component';
import { ModalContentOfferBoxComponent } from './components/modals/modal-content-offer-box/modal-content-offer-box.component';
import { ModalContentMaestroBoxComponent } from './components/modals/modal-content-maestro-box/modal-content-maestro-box.component';
import { ModalContentArticleBlockComponent } from './components/modals/modal-content-article-block/modal-content-article-block.component';
import { ModalContentDidYouKnowComponent } from './components/modals/modal-content-did-you-know/modal-content-did-you-know.component';
import { ModalContentSponsoredArticleBoxComponent } from './components/modals/modal-content-sponsored-article-box/modal-content-sponsored-article-box.component';
import { ModalContentHighlightedSelectionComponent } from './components/modals/modal-content-highlighted-selection/modal-content-highlighted-selection.component';
import { ModalContentBrandingBoxArticleComponent } from './components/modals/modal-content-branding-box-article/modal-content-branding-box-article.component';
import { ModalContentDailyMenuComponent } from './components/modals/modal-content-daily-menu/modal-content-daily-menu.component';
import { ModalContentOpinionNewsletterComponent } from './components/modals/modal-content-opinion-newsletter/modal-content-opinion-newsletter.component';
import { ModalContentTelekomVivicittaComponent } from './components/modals/modal-content-telekom-vivicitta/modal-content-telekom-vivicitta.component';
import { ModalContentSelectionComponent } from './components/modals/modal-content-selection/modal-content-selection.component';
import { ModalContentConferenceComponent } from './components/modals/modal-content-conference/modal-content-conference.component';
import { ModalContentPodcastArticleListComponent } from './components/modals/modal-content-podcast-article-list/modal-content-podcast-article-list.component';
import { ModalContentEbNewsBoxComponent } from './components/modals/modal-content-eb-news-box/modal-content-eb-news-box.component';
import { ModalContentCountdownBoxComponent } from './components/modals/modal-content-countdown-box/modal-content-countdown-box.component';
import { ModalContentEbCountdownBlockTitleComponent } from './components/modals/modal-content-eb-countdown-block-title/modal-content-eb-countdown-block-title.component';
import { ModalContentEbSingleEliminationComponent } from './components/modals/modal-content-eb-single-elimination/modal-content-eb-single-elimination.component';
import { ModalContentElectionsBoxComponent } from './components/modals/modal-content-elections-box/modal-content-elections-box.component';
import { ModalContentTeamsComponent } from './components/modals/modal-content-teams/modal-content-teams.component';
import { ModalContentOlimpiaCountdownBlockTitleComponent } from './components/modals/modal-content-olimpia-countdown-block-title/modal-content-olimpia-countdown-block-title.component';
import { ModalContentOlimpiaNewsBoxComponent } from './components/modals/modal-content-olimpia-news-box/modal-content-olimpia-news-box.component';
import { ModalContentOlimpiaHungarianCompetitionsComponent } from './components/modals/modal-content-olimpia-hungarian-competitions/modal-content-olimpia-hungarian-competitions.component';
import { ModalContentOlimpiaHungarianTeamComponent } from './components/modals/modal-content-olimpia-hungarian-team/modal-content-olimpia-hungarian-team.component';
import { ModalContentOlimpiaResultsBlockComponent } from './components/modals/modal-content-olimpia-results-block/modal-content-olimpia-results-block.component';
import { ModalContentOlimpiaArticlesWithPodcastContentComponent } from './components/modals/modal-content-olimpia-articles-with-podcast-content/modal-content-olimpia-articles-with-podcast-content.component';
import { ModalContentOlimpiaLargeNavigatorComponent } from './components/modals/modal-content-olimpia-large-navigator/modal-content-olimpia-large-navigator.component';
import { ModalContentGalleryArticleListComponent } from './components/modals/modal-content-gallery-article-list/modal-content-gallery-article-list.component';
import { ModalContentPublicAuthorsComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-public-authors/modal-content-public-authors.component';
import { ModalContentMultiVoteComponent } from './components/modals/modal-content-multi-vote/modal-content-multi-vote.component';
import { ModalContentServicesBoxComponent } from './components/modals/modal-content-services-box/modal-content-services-box.component';
import { ModalContentPodcastAppRecommenderComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-podcast-app-recommender/modal-content-podcast-app-recommender.component';
import { ModalContentExperienceGiftComponent } from './components/modals/modal-content-experience-gift/modal-content-experience-gift.component';
import { ModalContentGastroOccasionRecommenderComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-gastro-occasion-recommender/modal-content-gastro-occasion-recommender.component';
import { ModalContentGastroExperienceRecommendationComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-gastro-experience-recommendation/modal-content-gastro-experience-recommendation.component';
import { ModalContentGastroExperienceOccasionComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-gastro-experience-occasion/modal-content-gastro-experience-occasion.component';
import { ModalContentThematicRecommenderComponent } from './components/modals/modal-content-thematic-recommender/modal-content-thematic-recommender.component';
import { ModalContentGastroExperienceOccasionSwiperComponent } from './components/modals/modal-content-gastro-experience-occasion-swiper/modal-content-gastro-experience-occasion-swiper.component';
import { KoponyegDetectionCardsMasonryComponent } from '@modules/layout/components/layout-editor/components/portal-components';
import { ModalContentJobListingsComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-job-listings/modal-content-job-listings.component';
import { ModalContentTopTenTagsComponent } from './components/modals/modal-content-top-ten-tags/modal-content-top-ten-tags.component';
import { ModalContentSponsoredVoteComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-sponsored-vote/modal-content-sponsored-vote.component';
import { ModalContentSponsoredQuizComponent } from './components/modals/modal-content-sponsored-quiz/modal-content-sponsored-quiz.component';
import { ModalContentColumnBlockComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-column-block/modal-content-column-block.component';
import { ModalContentSubColumnsComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-sub-columns/modal-content-sub-columns.component';
import { ModalContentAstronetBrandingBoxComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-astronet-branding-box/modal-content-astronet-branding-box.component';
import { ModalContentAstronetColumnsComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-astronet-columns/modal-content-astronet-columns.component';
import { ModalContentTopCommentedArticlesComponent } from '@modules/layout/components/layout-editor/components/modals/modal-content-top-commented-articles/modal-content-top-commented-articles.component';
import { LoadWithIdleComponent } from '@modules/layout/components/layout-editor/components/load-with-idle/load-with-idle.component';
import { ModalContentShortVideosComponent } from './components/modals/modal-content-short-videos/modal-content-short-videos.component';

const MODAL_CONTENTS = [
  ModalContentArticleComponent,
  ModalContentAdComponent,
  ModalContentOpinionListComponent,
  ModalContentOpinionComponent,
  ModalContentVideoComponent,
  ModalContentDossierComponent,
  ModalContentNewsletterComponent,
  ModalContentGongNewsletterComponent,
  ModalContentStocksChartComponent,
  ModalContentVideoBlockComponent,
  ModalContentFreshNewsComponent,
  ModalContentLinkListComponent,
  ModalContentVoteComponent,
  ModalContentVisegradPostComponent,
  ModalContentCultureNationComponent,
  ModalContentHtmlEmbedComponent,
  ModalContentGalleryComponent,
  ModalContentBrandingBoxComponent,
  ModalContentNewspaperComponent,
  ModalContentSocialMediaComponent,
  ModalContentAstrologyComponent,
  ModalContentTabsComponent,
  ModalContentBreakingComponent,
  ModalContentOpinionPagesComponent,
  ModalContentArticlePagesComponent,
  ModalContentAgroKepComponent,
  ModalContentPrBlockComponent,
  ModalContentNoteComponent,
  ModalContentWysiwygComponent,
  ModalContentImageComponent,
  ModalContentBlogComponent,
  ModalContentIngatlanbazarComponent,
  ModalContentIngatlanbazarConfigurableComponent,
  ModalContentIngatlanbazarSearchComponent,
  ModalContentBrandingBoxExternalComponent,
  ModalContentTagBlockComponent,
  ModalContentTrendingTagsBlockComponent,
  ModalContentShortNewsComponent,
  ModalContentFastNewsComponent,
  ModalContentMinuteToMinuteComponent,
  ModalContentQuizComponent,
  ModalContentProgramComponent,
  ModalContentKoponyegComponent,
  ModalContentAstronetHoroszkopComponent,
  ModalContentAstronetJoslasComponent,
  ModalContentWazeComponent,
  ModalContentSzakikeresoComponent,
  ModalContentBestRecommenderComponent,
  ModalContentCategoryStepperComponent,
  ModalContentDossierListComponent,
  ModalContentBroadcastRecommenderComponent,
  ModalContentSongTopListComponent,
  ModalContentArticlesWithVideoComponent,
  ModalContentWeeklyNewspaperComponent,
  ModalContentDossierRepeaterComponent,
  ModalContentManualArticleComponent,
  ModalContentManualOpinionComponent,
  ModalContentRssBoxComponent,
  ModalContentFinalCountdownComponent,
  ModalContentHelloBudapestComponent,
  ModalContentAgroKepListComponent,
  ModalContentPodcastListComponent,
  ModalContentPdfBoxComponent,
  ModalContentKulturnemzetComponent,
  ModalContentArticlesWithPodcastContentComponent,
  ModalContentOlimpiaArticlesWithPodcastContentComponent,
  ModelContentMapRecommendationsComponent,
  ModalContentTagBlockComponent,
  ModalContentTrendingTagsBlockComponent,
  ModalContentTenyekBoxComponent,
  ModalContentHeroComponent,
  ModalContentMedicalMeteorologyComponent,
  ModalContentTwelveDaysForecastComponent,
  ModalContentDetectionsComponent,
  ModalContentImageMapListComponent,
  ModalContentDataBankComponent,
  ModalContentHeaderComponent,
  ModalContentSorozatvetoComponent,
  ModalContentDrawnMapListComponent,
  ModalContentTopStoriesComponent,
  ModalContentBaseComponent,
  ModalContentMoreArticlesComponent,
  ModalContentAuthorComponent,
  ModalContentRelatedArticlesComponent,
  ModalContentLiveBarComponent,
  ModalContentArticleBlockComponent,
  ModalContentDidYouKnowComponent,
  ModalContentSponsoredArticleBoxComponent,
  ModalContentTelekomVivicittaComponent,
  ModalContentConferenceComponent,
  ModalContentEbNewsBoxComponent,
  ModalContentCountdownBoxComponent,
  ModalContentElectionsBoxComponent,
  ModalContentEbCountdownBlockTitleComponent,
  ModalContentEbSingleEliminationComponent,
  ModalContentTeamsComponent,
  ModalContentOlimpiaCountdownBlockTitleComponent,
  ModalContentOlimpiaNewsBoxComponent,
  ModalContentOlimpiaHungarianCompetitionsComponent,
  ModalContentOlimpiaHungarianTeamComponent,
  ModalContentOlimpiaResultsBlockComponent,
  ModalContentOlimpiaLargeNavigatorComponent,
  ModalContentGalleryArticleListComponent,
  ModalContentPublicAuthorsComponent,
  ModalContentMultiVoteComponent,
  ModalContentPodcastAppRecommenderComponent,
  ModalContentExperienceGiftComponent,
  ModalContentGastroOccasionRecommenderComponent,
  ModalContentGastroExperienceRecommendationComponent,
  ModalContentGastroExperienceOccasionComponent,
  ModalContentJobListingsComponent,
  ModalContentTopTenTagsComponent,
  ModalContentColumnBlockComponent,
  ModalContentSponsoredQuizComponent,
  ModalContentSponsoredVoteComponent,
  ModalContentSubColumnsComponent,
  ModalContentAstronetBrandingBoxComponent,
  ModalContentAstronetColumnsComponent,
  ModalContentTopCommentedArticlesComponent,
  ModalContentShortVideosComponent,
];

@NgModule({
  declarations: [
    LayoutEditorComponent,
    ...MODAL_CONTENTS,
    ModalRowComponent,
    ModalColumnComponent,
    LayoutContentNamePipe,
    DragAndDropLayoutDirective,
    DragAndDropExplicitListDirective,
    ValidateRssDirective,
    ModalContentUpcomingMatchesComponent,
    ModalContentTripBoxComponent,
    ModalContentChampionshipTableComponent,
    ModalContentMostViewedComponent,
    ModalContentMediaPanelComponent,
    ModalContentLatestNewsComponent,
    ModalContentSpotlightComponent,
    ModalContentGpNewsBoxComponent,
    ModalContentSportBlockComponent,
    ModalContentRecipeCategoriesSelectComponent,
    ModalContentArticleSliderComponent,
    ModalContentTextBoxComponent,
    ModalContentTurpiBoxComponent,
    ModalContentTurpiCardComponent,
    ModalContentIngredientComponent,
    ModalContentRecipeCardComponent,
    ModalContentRecipeSwiperComponent,
    ModalContentGuaranteeBoxComponent,
    ModalContentNewsFeedComponent,
    ModalContentOfferBoxComponent,
    ModalContentMaestroBoxComponent,
    ModalContentHighlightedSelectionComponent,
    ModalContentBrandingBoxArticleComponent,
    ModalContentDailyMenuComponent,
    ModalContentOpinionNewsletterComponent,
    ModalContentSelectionComponent,
    ModalContentPodcastArticleListComponent,
    ModalContentServicesBoxComponent,
    ModalContentThematicRecommenderComponent,
    ModalContentGastroExperienceOccasionSwiperComponent,
    LoadWithIdleComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormModule,
    SharedModule,
    NgZorroModule,
    ToolboxModule,
    DragDropModule,
    FormControlsModule,
    KoponyegDetectionCardsMasonryComponent,
  ],
  exports: [LayoutEditorComponent, DragAndDropExplicitListDirective],
  providers: [],
})
export class LayoutEditorModule {}
