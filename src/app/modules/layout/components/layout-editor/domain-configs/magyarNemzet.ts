import { PortalConfigSetting } from '@shared/definitions/portal-config';
import { LayoutElementContentType, LayoutPageType } from '../../../layout-core.definitions';
import { IS_EB_COUNTDOWN_BLOCK_TITLE_ENABLED, LayoutEditorSettings } from '../../../layout-editor-configuration.definitions';

export const settings: LayoutEditorSettings[] = [
  {
    canAddColumn: true,
    canAddRow: true,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.OPINION,
      LayoutElementContentType.GALLERY,
      LayoutElementContentType.DOSSIER,
      LayoutElementContentType.AD,
      LayoutElementContentType.VOTE,
      LayoutElementContentType.HTML_EMBED,
      LayoutElementContentType.CULTURE_NATION,
      LayoutElementContentType.BRANDING_BOX,
      LayoutElementContentType.BRANDING_BOX_EX,
      LayoutElementContentType.PR_BLOCK,
      LayoutElementContentType.WYSIWYG,
      LayoutElementContentType.IMAGE,
      LayoutElementContentType.MOST_VIEWED,
      LayoutElementContentType.QUIZ,
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      {
        type: LayoutElementContentType.BAYER_BLOG,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_BAYER_ZSOLT_BLOG_COMPONENTS],
      },
      LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT,
      LayoutElementContentType.MINUTE_TO_MINUTE,
      LayoutElementContentType.NEWS_FEED,
      LayoutElementContentType.OPINION_NEWSLETTER_BOX,
      {
        type: LayoutElementContentType.SOROZATVETO,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_ARTICLE_REVIEWABLE_CHECKBOX],
      },
      {
        type: LayoutElementContentType.ARTICLE_BLOCK,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.DAILY_PROGRAM,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.EB_COUNTDOWN_BLOCK_TITLE,
        isEnabled: IS_EB_COUNTDOWN_BLOCK_TITLE_ENABLED,
      },
      {
        type: LayoutElementContentType.ARTICLES_WITH_PODCAST_CONTENT,
      },
      {
        type: LayoutElementContentType.EB_NEWS,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.DID_YOU_KNOW,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.TEAMS,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.EB_SINGLE_ELIMINATION,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.WHERE_THE_BALL_WILL_BE,
      },
      {
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },
    ],
    pageType: LayoutPageType.HOME,
    layoutWidth: 1200,
  },
  {
    canAddColumn: true,
    canAddRow: true,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.OPINION,
      LayoutElementContentType.GALLERY,
      LayoutElementContentType.DOSSIER,
      LayoutElementContentType.AD,
      LayoutElementContentType.VOTE,
      LayoutElementContentType.HTML_EMBED,
      LayoutElementContentType.BRANDING_BOX,
      LayoutElementContentType.PR_BLOCK,
      LayoutElementContentType.MOST_VIEWED,
      LayoutElementContentType.QUIZ,
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      {
        type: LayoutElementContentType.BAYER_BLOG,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_BAYER_ZSOLT_BLOG_COMPONENTS],
      },
      LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT,
      LayoutElementContentType.OPINION_NEWSLETTER_BOX,
      {
        type: LayoutElementContentType.SOROZATVETO,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_ARTICLE_REVIEWABLE_CHECKBOX],
      },
      {
        type: LayoutElementContentType.ARTICLE_BLOCK,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.DAILY_PROGRAM,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.EB_COUNTDOWN_BLOCK_TITLE,
        isEnabled: IS_EB_COUNTDOWN_BLOCK_TITLE_ENABLED,
      },
      {
        type: LayoutElementContentType.ARTICLES_WITH_PODCAST_CONTENT,
      },
      {
        type: LayoutElementContentType.EB_NEWS,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.DID_YOU_KNOW,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.TEAMS,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.EB_SINGLE_ELIMINATION,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },
    ],
    pageType: LayoutPageType.COLUMN,
    layoutWidth: 1200,
  },
  {
    canAddColumn: false,
    canAddRow: false,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.OPINION,
      LayoutElementContentType.AD,
      LayoutElementContentType.NEWSPAPER,
      LayoutElementContentType.SOCIAL_MEDIA,
      LayoutElementContentType.KULTUR_NEMZET,
      LayoutElementContentType.MOST_VIEWED,
      LayoutElementContentType.QUIZ,
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      {
        type: LayoutElementContentType.SPONSORED_VOTE,
        requiredPortalConfigs: [PortalConfigSetting.VOTING_EXTENDED_WITH_ADOCEAN],
      },
      {
        type: LayoutElementContentType.BAYER_BLOG,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_BAYER_ZSOLT_BLOG_COMPONENTS],
      },
      LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT,
      LayoutElementContentType.OPINION_NEWSLETTER_BOX,
      {
        type: LayoutElementContentType.ARTICLE_BLOCK,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.ARTICLES_WITH_PODCAST_CONTENT,
      },
      {
        type: LayoutElementContentType.EB_NEWS,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.DID_YOU_KNOW,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.DID_YOU_KNOW,
        requiredPortalConfigs: [PortalConfigSetting.VARIABLE_SPONSORED_CONTENT_FROM_DID_YOU_KNOW],
      },
      {
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },
    ],
    pageType: LayoutPageType.SIDEBAR,
    layoutWidth: 300,
  },
  {
    canAddColumn: false,
    canAddRow: false,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.OPINION,
      LayoutElementContentType.AD,
      LayoutElementContentType.NEWSPAPER,
      LayoutElementContentType.SOCIAL_MEDIA,
      LayoutElementContentType.KULTUR_NEMZET,
      LayoutElementContentType.MOST_VIEWED,
      LayoutElementContentType.QUIZ,
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      {
        type: LayoutElementContentType.SPONSORED_VOTE,
        requiredPortalConfigs: [PortalConfigSetting.VOTING_EXTENDED_WITH_ADOCEAN],
      },
      {
        type: LayoutElementContentType.BAYER_BLOG,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_BAYER_ZSOLT_BLOG_COMPONENTS],
      },
      LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT,
      LayoutElementContentType.OPINION_NEWSLETTER_BOX,
      {
        type: LayoutElementContentType.ARTICLE_BLOCK,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.ARTICLES_WITH_PODCAST_CONTENT,
      },
      {
        type: LayoutElementContentType.DID_YOU_KNOW,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.EB_NEWS,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.DID_YOU_KNOW,
        requiredPortalConfigs: [PortalConfigSetting.VARIABLE_SPONSORED_CONTENT_FROM_DID_YOU_KNOW],
      },
      {
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },
    ],
    pageType: LayoutPageType.COLUMNSIDEBAR,
    layoutWidth: 300,
  },
  {
    canAddColumn: true,
    canAddRow: true,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.OPINION,
      LayoutElementContentType.GALLERY,
      LayoutElementContentType.DOSSIER,
      LayoutElementContentType.AD,
      LayoutElementContentType.VOTE,
      LayoutElementContentType.HTML_EMBED,
      LayoutElementContentType.PR_BLOCK,
      LayoutElementContentType.WYSIWYG,
      LayoutElementContentType.IMAGE,
      LayoutElementContentType.MOST_VIEWED,
      LayoutElementContentType.QUIZ,
      {
        type: LayoutElementContentType.SPONSORED_VOTE,
        requiredPortalConfigs: [PortalConfigSetting.VOTING_EXTENDED_WITH_ADOCEAN],
      },
      {
        type: LayoutElementContentType.DID_YOU_KNOW,
        requiredPortalConfigs: [PortalConfigSetting.VARIABLE_SPONSORED_CONTENT_FROM_DID_YOU_KNOW],
      },
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      {
        type: LayoutElementContentType.BAYER_BLOG,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_BAYER_ZSOLT_BLOG_COMPONENTS],
      },
      LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT,
      LayoutElementContentType.OPINION_NEWSLETTER_BOX,
      {
        type: LayoutElementContentType.SOROZATVETO,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_ARTICLE_REVIEWABLE_CHECKBOX],
      },
      {
        type: LayoutElementContentType.ARTICLE_BLOCK,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.DAILY_PROGRAM,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.EB_COUNTDOWN_BLOCK_TITLE,
        isEnabled: IS_EB_COUNTDOWN_BLOCK_TITLE_ENABLED,
      },
      {
        type: LayoutElementContentType.ARTICLES_WITH_PODCAST_CONTENT,
      },
      {
        type: LayoutElementContentType.EB_NEWS,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.DID_YOU_KNOW,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.TEAMS,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.EB_SINGLE_ELIMINATION,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },
    ],
    pageType: LayoutPageType.CUSTOM_BUILT_PAGE,
    layoutWidth: 1200,
  },
];
