import { LayoutElementContentType, LayoutPageType } from '../../../layout-core.definitions';
import { LayoutEditorSettings } from '../../../layout-editor-configuration.definitions';
import { PortalConfigSetting } from '@shared/definitions/portal-config';

export const settings: LayoutEditorSettings[] = [
  {
    canAddColumn: true,
    canAddRow: true,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.NEWSLETTER_BLOCK,
      LayoutElementContentType.VOTE,
      LayoutElementContentType.BRANDING_BOX_EX,
      LayoutElementContentType.AD,
      {
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      {
        type: LayoutElementContentType.SECRET_DAYS_CALENDAR,
        requiredPortalConfigs: [PortalConfigSetting.MENU_TYPE_SECRET_DAYS_CALENDAR],
      },
    ],
    pageType: LayoutPageType.HOME,
    layoutWidth: 1200,
  },
  {
    canAddColumn: true,
    canAddRow: true,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.NEWSLETTER_BLOCK,
      LayoutElementContentType.AD,
      {
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      {
        type: LayoutElementContentType.SECRET_DAYS_CALENDAR,
        requiredPortalConfigs: [PortalConfigSetting.MENU_TYPE_SECRET_DAYS_CALENDAR],
      },
    ],
    pageType: LayoutPageType.COLUMN,
    layoutWidth: 1200,
  },
  {
    canAddColumn: false,
    canAddRow: false,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.NEWSLETTER_BLOCK,
      LayoutElementContentType.BLOCK_SEPARATOR,
      LayoutElementContentType.VOTE,
      LayoutElementContentType.AD,
      {
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      {
        type: LayoutElementContentType.SPONSORED_VOTE,
        requiredPortalConfigs: [PortalConfigSetting.VOTING_EXTENDED_WITH_ADOCEAN],
      },
      {
        type: LayoutElementContentType.SECRET_DAYS_CALENDAR,
        requiredPortalConfigs: [PortalConfigSetting.MENU_TYPE_SECRET_DAYS_CALENDAR],
      },
      {
        type: LayoutElementContentType.DID_YOU_KNOW,
        requiredPortalConfigs: [PortalConfigSetting.VARIABLE_SPONSORED_CONTENT_FROM_DID_YOU_KNOW],
      },
    ],
    pageType: LayoutPageType.SIDEBAR,
    layoutWidth: 300,
  },
  {
    canAddColumn: false,
    canAddRow: false,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.NEWSLETTER_BLOCK,
      LayoutElementContentType.BLOCK_SEPARATOR,
      LayoutElementContentType.AD,
      {
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      {
        type: LayoutElementContentType.SPONSORED_VOTE,
        requiredPortalConfigs: [PortalConfigSetting.VOTING_EXTENDED_WITH_ADOCEAN],
      },
      {
        type: LayoutElementContentType.SECRET_DAYS_CALENDAR,
        requiredPortalConfigs: [PortalConfigSetting.MENU_TYPE_SECRET_DAYS_CALENDAR],
      },
      {
        type: LayoutElementContentType.DID_YOU_KNOW,
        requiredPortalConfigs: [PortalConfigSetting.VARIABLE_SPONSORED_CONTENT_FROM_DID_YOU_KNOW],
      },
    ],
    pageType: LayoutPageType.COLUMNSIDEBAR,
    layoutWidth: 300,
  },
];
