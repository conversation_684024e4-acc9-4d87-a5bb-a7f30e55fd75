import { PortalConfigSetting } from '@shared/definitions/portal-config';
import { LayoutElementContentType, LayoutPageType } from '../../../layout-core.definitions';
import { LayoutEditorSettings } from '../../../layout-editor-configuration.definitions';

export const settings: LayoutEditorSettings[] = [
  {
    canAddColumn: true,
    canAddRow: true,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.OPINION,
      LayoutElementContentType.SPORT_RADIO_PLAYER,
      LayoutElementContentType.NEWSLETTER_BLOCK,
      LayoutElementContentType.VIDEO,
      LayoutElementContentType.GALLERY,
      LayoutElementContentType.VOTE,
      LayoutElementContentType.VISITOR_COUNTER,
      LayoutElementContentType.QUIZ,
      LayoutElementContentType.DATA_BANK,
      LayoutElementContentType.LEAD_EDITORS,
      LayoutElementContentType.VIDEO_BLOCK,
      LayoutElementContentType.BRANDING_BOX_EX,
      LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT,
      LayoutElementContentType.TEAMS,
      LayoutElementContentType.NEWSPAPER,
      LayoutElementContentType.TRIP_BOX,
      LayoutElementContentType.UPCOMING_MATCHES,
      LayoutElementContentType.CHAMPIONSHIP_TABLE,
      LayoutElementContentType.ARTICLE_BLOCK,
      LayoutElementContentType.BLOCK_SEPARATOR,
      LayoutElementContentType.LATEST_NEWS,
      LayoutElementContentType.BRANDING_BOX_ARTICLE,
      {
        type: LayoutElementContentType.TELEKOM_VIVICITTA,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_TELEKOM_VIVICITTA],
      },
      LayoutElementContentType.COUNTDOWN_BOX,
      {
        type: LayoutElementContentType.EB_SINGLE_ELIMINATION,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.DAILY_PROGRAM,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.MULTI_VOTE,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_MULTI_VOTE],
      },
    ],
    pageType: LayoutPageType.HOME,
    layoutWidth: 1200,
  },
  {
    canAddColumn: true,
    canAddRow: true,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.OPINION,
      LayoutElementContentType.VIDEO,
      LayoutElementContentType.GALLERY,
      LayoutElementContentType.SPORT_RADIO_PLAYER,
      LayoutElementContentType.NEWSLETTER_BLOCK,
      LayoutElementContentType.VOTE,
      LayoutElementContentType.VISITOR_COUNTER,
      LayoutElementContentType.QUIZ,
      LayoutElementContentType.DATA_BANK,
      LayoutElementContentType.VIDEO_BLOCK,
      LayoutElementContentType.LEAD_EDITORS,
      LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT,
      LayoutElementContentType.BRANDING_BOX_EX,
      LayoutElementContentType.TEAMS,
      LayoutElementContentType.NEWSPAPER,
      LayoutElementContentType.UPCOMING_MATCHES,
      LayoutElementContentType.CHAMPIONSHIP_TABLE,
      LayoutElementContentType.BLOCK_SEPARATOR,
      LayoutElementContentType.LATEST_NEWS,
      LayoutElementContentType.COUNTDOWN_BOX,
      {
        type: LayoutElementContentType.EB_SINGLE_ELIMINATION,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.DAILY_PROGRAM,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.MULTI_VOTE,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_MULTI_VOTE],
      },
    ],
    pageType: LayoutPageType.COLUMN,
    layoutWidth: 1200,
  },
  {
    canAddColumn: true,
    canAddRow: true,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.OPINION,
      LayoutElementContentType.VIDEO,
      LayoutElementContentType.GALLERY,
      LayoutElementContentType.SPORT_RADIO_PLAYER,
      LayoutElementContentType.NEWSLETTER_BLOCK,
      LayoutElementContentType.VOTE,
      LayoutElementContentType.VISITOR_COUNTER,
      LayoutElementContentType.QUIZ,
      LayoutElementContentType.DATA_BANK,
      LayoutElementContentType.VIDEO_BLOCK,
      LayoutElementContentType.LEAD_EDITORS,
      LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT,
      LayoutElementContentType.BRANDING_BOX_EX,
      LayoutElementContentType.TEAMS,
      LayoutElementContentType.NEWSPAPER,
      LayoutElementContentType.UPCOMING_MATCHES,
      LayoutElementContentType.CHAMPIONSHIP_TABLE,
      LayoutElementContentType.BLOCK_SEPARATOR,
      LayoutElementContentType.LATEST_NEWS,
      LayoutElementContentType.COUNTDOWN_BOX,
      {
        type: LayoutElementContentType.EB_SINGLE_ELIMINATION,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.DAILY_PROGRAM,
        requiredPortalConfigs: [PortalConfigSetting.ENABLE_FOOTBALL_EB_ELEMENTS],
      },
      {
        type: LayoutElementContentType.MULTI_VOTE,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_MULTI_VOTE],
      },
    ],
    pageType: LayoutPageType.CUSTOM_BUILT_PAGE,
    layoutWidth: 1200,
  },
  {
    canAddColumn: false,
    canAddRow: false,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.OPINION,
      LayoutElementContentType.VIDEO,
      LayoutElementContentType.GALLERY,
      LayoutElementContentType.NEWSLETTER_BLOCK,
      LayoutElementContentType.VOTE,
      LayoutElementContentType.VISITOR_COUNTER,
      LayoutElementContentType.QUIZ,
      LayoutElementContentType.DATA_BANK,
      LayoutElementContentType.LEAD_EDITORS,
      LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT,
      LayoutElementContentType.BRANDING_BOX_EX,
      LayoutElementContentType.TEAMS,
      LayoutElementContentType.NEWSPAPER,
      LayoutElementContentType.UPCOMING_MATCHES,
      LayoutElementContentType.CHAMPIONSHIP_TABLE,
      LayoutElementContentType.BLOCK_SEPARATOR,
      LayoutElementContentType.LATEST_NEWS,
      LayoutElementContentType.COUNTDOWN_BOX,
      {
        type: LayoutElementContentType.MULTI_VOTE,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_MULTI_VOTE],
      },
      {
        type: LayoutElementContentType.DID_YOU_KNOW,
        requiredPortalConfigs: [PortalConfigSetting.VARIABLE_SPONSORED_CONTENT_FROM_DID_YOU_KNOW],
      },
    ],
    pageType: LayoutPageType.SIDEBAR,
    layoutWidth: 300,
  },
  {
    canAddColumn: false,
    canAddRow: false,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.OPINION,
      LayoutElementContentType.VIDEO,
      LayoutElementContentType.GALLERY,
      LayoutElementContentType.NEWSLETTER_BLOCK,
      LayoutElementContentType.VOTE,
      LayoutElementContentType.VISITOR_COUNTER,
      LayoutElementContentType.QUIZ,
      LayoutElementContentType.DATA_BANK,
      LayoutElementContentType.LEAD_EDITORS,
      LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT,
      LayoutElementContentType.BRANDING_BOX_EX,
      LayoutElementContentType.TEAMS,
      LayoutElementContentType.NEWSPAPER,
      LayoutElementContentType.UPCOMING_MATCHES,
      LayoutElementContentType.CHAMPIONSHIP_TABLE,
      LayoutElementContentType.BLOCK_SEPARATOR,
      LayoutElementContentType.LATEST_NEWS,
      LayoutElementContentType.COUNTDOWN_BOX,
      {
        type: LayoutElementContentType.MULTI_VOTE,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_MULTI_VOTE],
      },
      {
        type: LayoutElementContentType.DID_YOU_KNOW,
        requiredPortalConfigs: [PortalConfigSetting.VARIABLE_SPONSORED_CONTENT_FROM_DID_YOU_KNOW],
      },
    ],
    pageType: LayoutPageType.COLUMNSIDEBAR,
    layoutWidth: 300,
  },
];
