import { LayoutElementContentType, LayoutPageType } from '../../../layout-core.definitions';
import { LayoutEditorSettings } from '../../../layout-editor-configuration.definitions';
import { PortalConfigSetting } from '@shared/definitions/portal-config';

export const settings: LayoutEditorSettings[] = [
  {
    canAddColumn: true,
    canAddRow: true,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.GALLERY,
      LayoutElementContentType.VOTE,
      LayoutElementContentType.AD,
      LayoutElementContentType.PR_BLOCK,
      LayoutElementContentType.OPINION,
      // LayoutElementContentType.FRESH_NEWS,
      LayoutElementContentType.HTML_EMBED,
      LayoutElementContentType.DOSSIER,
      LayoutElementContentType.ASTRONET_HOROSZKOP,
      LayoutElementContentType.ASTRONET_JOSLAS,
      LayoutElementContentType.INGATLANBAZAR,
      LayoutElementContentType.INGATLANBAZAR_SEARCH,
      LayoutElementContentType.INGATLANBAZAR_CONFIGURABLE,
      LayoutElementContentType.TOP_TEN_TAGS,
      LayoutElementContentType.NEWSLETTER_BLOCK,
      LayoutElementContentType.BRANDING_BOX_EX,
      LayoutElementContentType.MOST_VIEWED,
      /*{
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },*/
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT,
      LayoutElementContentType.STAR_BIRTHS,
      LayoutElementContentType.COLUMN_BLOCK,
      LayoutElementContentType.LATEST_AND_MOST_READ_ARTICLES,
      LayoutElementContentType.MINUTE_TO_MINUTE,
      LayoutElementContentType.SUB_COLUMNS,
      LayoutElementContentType.ASTRONET_BRANDING_BOX,
      LayoutElementContentType.ASTRONET_COLUMNS,
      LayoutElementContentType.TOPIC_SUGGESTION,
      LayoutElementContentType.TOP_COMMENTED_ARTICLES,
      LayoutElementContentType.SHORT_VIDEOS,
      {
        type: LayoutElementContentType.SECRET_DAYS_CALENDAR,
        requiredPortalConfigs: [PortalConfigSetting.MENU_TYPE_SECRET_DAYS_CALENDAR],
      },
    ],
    pageType: LayoutPageType.HOME,
    layoutWidth: 1200,
  },
  {
    canAddColumn: true,
    canAddRow: true,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.AD,
      LayoutElementContentType.PR_BLOCK,
      LayoutElementContentType.WYSIWYG,
      LayoutElementContentType.OPINION,
      // LayoutElementContentType.FRESH_NEWS,
      LayoutElementContentType.DOSSIER,
      LayoutElementContentType.GALLERY,
      LayoutElementContentType.HTML_EMBED,
      LayoutElementContentType.TOP_TEN_TAGS,
      LayoutElementContentType.NEWSLETTER_BLOCK,
      LayoutElementContentType.BRANDING_BOX_EX,
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      /*{
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },*/
      LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT,
      LayoutElementContentType.STAR_BIRTHS,
      LayoutElementContentType.COLUMN_BLOCK,
      LayoutElementContentType.LATEST_AND_MOST_READ_ARTICLES,
      LayoutElementContentType.MINUTE_TO_MINUTE,
      LayoutElementContentType.MOST_VIEWED,
      LayoutElementContentType.SUB_COLUMNS,
      LayoutElementContentType.ASTRONET_BRANDING_BOX,
      LayoutElementContentType.ASTRONET_COLUMNS,
      LayoutElementContentType.TOPIC_SUGGESTION,
      LayoutElementContentType.TOP_COMMENTED_ARTICLES,
      LayoutElementContentType.SHORT_VIDEOS,
      {
        type: LayoutElementContentType.SECRET_DAYS_CALENDAR,
        requiredPortalConfigs: [PortalConfigSetting.MENU_TYPE_SECRET_DAYS_CALENDAR],
      },
    ],
    pageType: LayoutPageType.COLUMN,
    layoutWidth: 1200,
  },
  {
    canAddColumn: true,
    canAddRow: true,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.AD,
      LayoutElementContentType.PR_BLOCK,
      LayoutElementContentType.WYSIWYG,
      LayoutElementContentType.HTML_EMBED,
      LayoutElementContentType.TOP_TEN_TAGS,
      LayoutElementContentType.NEWSLETTER_BLOCK,
      LayoutElementContentType.BRANDING_BOX_EX,
      LayoutElementContentType.GALLERY,
      LayoutElementContentType.MOST_VIEWED,
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      /*{
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },*/
      LayoutElementContentType.ARTICLES_WITH_VIDEO_CONTENT,
      LayoutElementContentType.STAR_BIRTHS,
      LayoutElementContentType.COLUMN_BLOCK,
      LayoutElementContentType.LATEST_AND_MOST_READ_ARTICLES,
      LayoutElementContentType.MINUTE_TO_MINUTE,
      LayoutElementContentType.SUB_COLUMNS,
      LayoutElementContentType.ASTRONET_BRANDING_BOX,
      LayoutElementContentType.ASTRONET_COLUMNS,
      LayoutElementContentType.TOPIC_SUGGESTION,
      LayoutElementContentType.SHORT_VIDEOS,
      LayoutElementContentType.TOP_COMMENTED_ARTICLES,
      {
        type: LayoutElementContentType.SECRET_DAYS_CALENDAR,
        requiredPortalConfigs: [PortalConfigSetting.MENU_TYPE_SECRET_DAYS_CALENDAR],
      },
    ],
    pageType: LayoutPageType.CUSTOM_BUILT_PAGE,
    layoutWidth: 1200,
  },
  {
    canAddColumn: false,
    canAddRow: false,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.OPINION,
      LayoutElementContentType.AD,
      // LayoutElementContentType.FRESH_NEWS,
      LayoutElementContentType.VOTE,
      LayoutElementContentType.HTML_EMBED,
      LayoutElementContentType.INGATLANBAZAR,
      LayoutElementContentType.INGATLANBAZAR_SEARCH,
      LayoutElementContentType.INGATLANBAZAR_CONFIGURABLE,
      LayoutElementContentType.BRANDING_BOX_EX,
      LayoutElementContentType.TOP_TEN_TAGS,
      LayoutElementContentType.NEWSLETTER_BLOCK,
      LayoutElementContentType.GALLERY,
      LayoutElementContentType.MOST_VIEWED,
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      {
        type: LayoutElementContentType.SPONSORED_VOTE,
        requiredPortalConfigs: [PortalConfigSetting.VOTING_EXTENDED_WITH_ADOCEAN],
      },
      /*{
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },*/
      LayoutElementContentType.STAR_BIRTHS,
      LayoutElementContentType.LATEST_AND_MOST_READ_ARTICLES,
      LayoutElementContentType.SUB_COLUMNS,
      LayoutElementContentType.TOPIC_SUGGESTION,
      LayoutElementContentType.TOP_COMMENTED_ARTICLES,
      {
        type: LayoutElementContentType.SECRET_DAYS_CALENDAR,
        requiredPortalConfigs: [PortalConfigSetting.MENU_TYPE_SECRET_DAYS_CALENDAR],
      },
      {
        type: LayoutElementContentType.DID_YOU_KNOW,
        requiredPortalConfigs: [PortalConfigSetting.VARIABLE_SPONSORED_CONTENT_FROM_DID_YOU_KNOW],
      },
    ],
    pageType: LayoutPageType.SIDEBAR,
    layoutWidth: 300,
  },
  {
    canAddColumn: false,
    canAddRow: false,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.OPINION,
      LayoutElementContentType.AD,
      // LayoutElementContentType.FRESH_NEWS,
      LayoutElementContentType.VOTE,
      LayoutElementContentType.HTML_EMBED,
      LayoutElementContentType.TOP_TEN_TAGS,
      LayoutElementContentType.NEWSLETTER_BLOCK,
      LayoutElementContentType.GALLERY,
      LayoutElementContentType.MOST_VIEWED,
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      {
        type: LayoutElementContentType.SPONSORED_VOTE,
        requiredPortalConfigs: [PortalConfigSetting.VOTING_EXTENDED_WITH_ADOCEAN],
      },
      /*{
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },*/
      LayoutElementContentType.STAR_BIRTHS,
      LayoutElementContentType.LATEST_AND_MOST_READ_ARTICLES,
      LayoutElementContentType.SUB_COLUMNS,
      LayoutElementContentType.TOPIC_SUGGESTION,
      LayoutElementContentType.TOP_COMMENTED_ARTICLES,
      {
        type: LayoutElementContentType.SECRET_DAYS_CALENDAR,
        requiredPortalConfigs: [PortalConfigSetting.MENU_TYPE_SECRET_DAYS_CALENDAR],
      },
      {
        type: LayoutElementContentType.DID_YOU_KNOW,
        requiredPortalConfigs: [PortalConfigSetting.VARIABLE_SPONSORED_CONTENT_FROM_DID_YOU_KNOW],
      },
    ],
    pageType: LayoutPageType.COLUMNSIDEBAR,
    layoutWidth: 300,
  },
  {
    canAddColumn: true,
    canAddRow: true,
    enabledContentTypes: [
      LayoutElementContentType.ARTICLE,
      LayoutElementContentType.OPINION,
      LayoutElementContentType.AD,
      LayoutElementContentType.HTML_EMBED,
      LayoutElementContentType.GALLERY,
      {
        type: LayoutElementContentType.CONFIGURABLE_SPONSORED_BOX,
        requiredPortalConfigs: [PortalConfigSetting.CONTENT_SPONSORSHIP_SPONSORED_BOX],
      },
      /*{
        type: LayoutElementContentType.SPONSORED_QUIZ,
        requiredPortalConfigs: [PortalConfigSetting.CMS_MENU_TYPE_AVAILABLE_QUIZZES],
      },*/
    ],
    pageType: LayoutPageType.OPINION,
    layoutWidth: 1200,
  },
];
