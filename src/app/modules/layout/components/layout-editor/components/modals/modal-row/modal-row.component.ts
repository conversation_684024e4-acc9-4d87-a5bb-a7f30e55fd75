import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { inject } from '@angular/core';
import { Component, Input, OnInit } from '@angular/core';
import { DomainKey } from 'src/app/core/modules/admin/admin.definitions';
import { DomainService } from '@core/modules/admin/services/domain.service';

@Component({
  selector: 'app-modal-row',
  templateUrl: './modal-row.component.html',
  styleUrls: ['./modal-row.component.scss'],
  standalone: false,
})
export class ModalRowComponent implements OnInit {
  private readonly modalData = inject<Record<string, any>>(NZ_MODAL_DATA, {});

  // TODO: review props
  @Input() selectedColumnLayout: number[];
  @Input() withBlockTitle: boolean;
  @Input() hasLine: boolean;
  @Input() hideMobile: boolean;
  @Input() canHaveBackground: boolean;
  @Input() backgroundColor = '';
  @Input() isMobileSideBySide = false;
  @Input() domain: DomainKey;
  selectedIndex = -1;
  displayHideMobile: boolean = true;

  domainService = inject(DomainService);

  public columnLayouts = [[], [12], [6, 6], [4, 8], [8, 4], [9, 3], [4, 4, 4], [4, 5, 3], [3, 3, 3, 3]];

  constructor() {}

  ngOnInit(): void {
    Object.keys(this.modalData).forEach((key) => (this[key] = this.modalData[key]));

    this.displayHideMobile = this.domain !== 'mandiner';
    if (this.selectedColumnLayout) {
      const columnLayout = this.columnLayouts.filter((element) => this.arraysEqual(element, this.selectedColumnLayout))[0];
      this.selectedIndex = this.columnLayouts.indexOf(columnLayout);
    }
  }

  get isMobileSideBySideLayout(): boolean {
    return this.selectedColumnLayout?.length && this.selectedColumnLayout.every((column) => [6, 3].includes(column));
  }

  selectLayout(index: number) {
    this.selectedIndex = index;
    this.selectedColumnLayout = this.columnLayouts[this.selectedIndex];
    if (!this.isMobileSideBySideLayout) {
      this.isMobileSideBySide = false;
    }
  }

  public get customPortalColorsToRow(): string[] {
    switch (this.domain) {
      case 'szabadfold':
        return ['#EBFCF3', '#F3F0EB', '#F8F4EE', '#F5FDF9', '#F1EDE6'];
      case 'mindmegette':
        return ['#262626', '#00371c', '#00964a', '#3861A1'];
      case 'vilaggazdasag':
        return ['#262626'];
      case 'pesti_sracok':
        return ['#FFE7CC'];
      case 'bors':
        return ['#FFFF85', '#4B3575', '#E2003B', '#7A5ACF', '#363D59', '#258383', '#507D54', '#278262', '#002234', '#6B5C4B'];
      default:
        return [];
    }
  }

  onDefaultBackground(): void {
    this.backgroundColor = null;
  }

  // TODO: refactor into pipe?, do some maths
  getColRate(col: number) {
    switch (col) {
      case 2:
        return `1 / 6`;
      case 3:
        return `1 / 4`;
      case 4:
        return `1 / 3`;
      case 6:
        return `1 / 2`;
      case 8:
        return `2 / 3`;
      case 9:
        return `3 / 4`;
      case 12:
        return `1 / 1`;
      default:
        return `${col} / 12`;
    }
  }

  arraysEqual(a, b) {
    if (a === b) {
      return true;
    }
    if (a == null || b == null) {
      return false;
    }
    if (a.length !== b.length) {
      return false;
    }
    for (let i = 0; i < a.length; ++i) {
      if (a[i] !== b[i]) {
        return false;
      }
    }
    return true;
  }
}
