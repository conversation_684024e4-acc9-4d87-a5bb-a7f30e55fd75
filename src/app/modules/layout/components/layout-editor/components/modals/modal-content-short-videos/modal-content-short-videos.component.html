<div class="article-config">
  <div class="row">
    <div class="col-12">
      <div class="checkbox">
        <label nz-checkbox [(ngModel)]="withBlockTitle">{{ 'CMS.block-has-title' | translate }}</label>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-6">
      <div class="form-control">
        <label for="contentLength">Short videók száma</label>
        <nz-form-control>
          <input
            type="number"
            [nzStepperless]="false"
            nz-input
            name="contentLength"
            [formControl]="lengthControl"
            [errors]="{ required: 'Mező kitöltése kötelező!' }"
          />
        </nz-form-control>
      </div>
    </div>
    <div class="col-6">
      <div class="checkbox">
        <label nz-checkbox [(ngModel)]="hideMobile">{{ 'CMS.hide-mobile' | translate }}</label>
      </div>
    </div>
  </div>
  <h2><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> formátuma</h2>
  <div class="row">
    <div class="col-6" *ngFor="let option of shortVideoOptions; let i = index">
      <button class="option" [ngClass]="{ selected: selectedIndex === i }" (click)="selectVideoType(i)">
        <img [src]="option.previewImage" class="preview-image" />
      </button>
    </div>
  </div>
</div>
