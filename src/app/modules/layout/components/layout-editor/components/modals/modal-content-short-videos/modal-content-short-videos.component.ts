import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { inject, Component, Input, OnInit, input } from '@angular/core';
import { UntypedFormControl, Validators } from '@angular/forms';
import { DomainKey } from '../../../../../../../core/modules/admin/admin.definitions';
import { DomainService } from '../../../../../../../core/modules/admin/services/domain.service';
import { LayoutElementVideoPreview } from '../../../../../layout-core.definitions';
import { NSOVideoCardType } from '@modules/layout/definitions';

@Component({
  selector: 'app-modal-content-short-videos',
  templateUrl: './modal-content-short-videos.component.html',
  styleUrls: ['./modal-content-short-videos.component.scss'],
  standalone: false,
})
export class ModalContentShortVideosComponent implements OnInit {
  private readonly modalData = inject<Record<string, any>>(NZ_MODAL_DATA, {});
  @Input() selectedShortVideoType: LayoutElementVideoPreview;
  @Input() contentLength: number;
  @Input() styleId: number;
  @Input() hideMobile: boolean;
  @Input() withBlockTitle: boolean;

  selectedIndex = -1;
  lengthControl: UntypedFormControl;

  domainConfigs: Partial<Record<DomainKey, LayoutElementVideoPreview[]>> = {
    bors: [
      {
        styleId: 1,
        previewImage: '/assets/images/layout-frames/bors/short_videos_01.png',
        hasTitle: true,
        hasLead: false,
        hasCoverImage: true,
        hasWatchTime: true,
      },
    ],
  };
  shortVideoOptions: LayoutElementVideoPreview[] = [];

  constructor(private readonly domainService: DomainService) {}

  ngOnInit(): void {
    Object.keys(this.modalData).forEach((key) => (this[key] = this.modalData[key]));

    const domainConfig = this.domainConfigs[this.domainService.currentDomain.key];
    if (domainConfig) {
      this.shortVideoOptions = domainConfig;
    }

    this.lengthControl = new UntypedFormControl(this.contentLength ? this.contentLength : 1, Validators.required);
    this.selectTypeIndex(this.styleId);
  }

  selectVideoType(index: number) {
    this.selectedIndex = index;
    this.selectedShortVideoType = this.shortVideoOptions[this.selectedIndex];
  }

  selectTypeIndex(styleId: number) {
    const type = this.shortVideoOptions.find((video) => video.styleId === styleId);
    this.selectedIndex = this.shortVideoOptions.indexOf(type);
    this.selectedShortVideoType = type;
  }
}
