import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { inject, Component, Input, OnInit } from '@angular/core';
import { UntypedFormControl, Validators } from '@angular/forms';
import { DomainKey } from 'src/app/core/modules/admin/admin.definitions';
import { DomainService } from 'src/app/core/modules/admin/services/domain.service';

@Component({
  selector: 'app-modal-content-program',
  templateUrl: './modal-content-program.component.html',
  styleUrls: ['./modal-content-program.component.scss'],
  standalone: false,
})
export class ModalContentProgramComponent implements OnInit {
  private readonly modalData = inject<Record<string, any>>(NZ_MODAL_DATA, {});
  @Input() contentLength: number;
  @Input() hideMobile: boolean;
  @Input() withBlockTitle: boolean;
  @Input() styleId: number;
  @Input() domain: DomainKey;
  @Input() selectedProgramType: {
    styleId: number;
    previewImage: string;
    hasTitle: boolean;
    hasImage: boolean;
  };

  lengthControl: UntypedFormControl;
  selectedIndex = -1;
  domainIndex = -1;

  programOptions: {
    domain: string;
    options: {
      styleId: number;
      previewImage: string;
      hasTitle: boolean;
      hasImage: boolean;
      hasDate: boolean;
      haslocation: boolean;
    }[];
  }[] = [
    {
      domain: 'megyeiLap',
      options: [
        {
          styleId: 1,
          previewImage: '/assets/images/layout-frames/megyeilapok/program01.png',
          hasDate: true,
          hasTitle: true,
          hasImage: true,
          haslocation: true,
        },
      ],
    },
  ];

  constructor(private domainService: DomainService) {}

  ngOnInit(): void {
    Object.keys(this.modalData).forEach((key) => (this[key] = this.modalData[key]));

    this.lengthControl = new UntypedFormControl(this.contentLength ? this.contentLength : 1, Validators.required);
    const isMegyeiLap = this.domainService.isMegyeiLap(this.domain);
    if (isMegyeiLap) {
      this.domainIndex = this.programOptions.findIndex((o) => o.domain === 'megyeiLap');
    } else {
      this.domainIndex = this.programOptions.findIndex((o) => o.domain === this.domain);
    }
    this.selectTypeIndex(this.styleId);
  }

  selectType(index: number) {
    this.selectedIndex = index;
    this.selectedProgramType = this.programOptions[this.domainIndex].options[this.selectedIndex];
  }

  selectTypeIndex(styleId: number) {
    const type = this.programOptions[this.domainIndex].options.find((program) => program.styleId === styleId);
    const selectedIndex = this.programOptions[this.domainIndex].options.indexOf(type);
    this.selectedIndex = selectedIndex;
    this.selectedProgramType = type;
  }
}
