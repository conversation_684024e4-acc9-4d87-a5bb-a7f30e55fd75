import { AfterViewInit, Component, inject, Input, OnDestroy, OnInit } from '@angular/core';
import { UntypedFormControl, Validators } from '@angular/forms';
import { FontSize } from '@shared/definitions/font-size.definitions';
import { FONT_SIZES } from '@shared/utils/font-sizes.utils';
import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { Subject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { DomainKey } from 'src/app/core/modules/admin/admin.definitions';
import { DomainService } from 'src/app/core/modules/admin/services/domain.service';
import { LayoutElementContentType, LayoutElementOpinionPreview, LayoutElementPreviewConfig } from '../../../../../layout-core.definitions';
import {
  BorsArticleCardType,
  OrigoSportOpinionCardType,
  PestiSracokArticleCardType,
  SzegedmaOpinionCardType,
  VGArticleCardType,
} from '@modules/layout/definitions';

@Component({
  selector: 'app-modal-content-opinion',
  templateUrl: './modal-content-opinion.component.html',
  styleUrls: ['./modal-content-opinion.component.scss'],
  standalone: false,
})
export class ModalContentOpinionComponent implements OnInit, AfterViewInit, OnDestroy {
  private readonly modalData = inject<Record<string, any>>(NZ_MODAL_DATA, {});
  @Input() selectedOpinionType: LayoutElementOpinionPreview;
  @Input() contentLength: number;
  @Input() styleId: number;
  @Input() hideMobile: boolean;
  @Input() withBlockTitle: boolean;
  @Input() domain: DomainKey;
  @Input() showHeader: boolean;
  @Input() mobileOrder: number;
  @Input() useMobileOrder: boolean;
  @Input() secondaryContentType: LayoutElementContentType = LayoutElementContentType.OPINION;
  @Input() fontSize = 20;
  @Input() withVerticalSeparator: boolean;

  readonly destroy$ = new Subject<void>();

  domainIndex = -1;
  selectedIndex: number | string = -1;
  lengthControl: UntypedFormControl;
  fontSizeControl: UntypedFormControl;
  public mobilOrderControl: UntypedFormControl;

  opinionOptions: LayoutElementPreviewConfig<LayoutElementOpinionPreview>[] = [
    {
      domain: 'vilaggazdasag',
      options: [
        {
          styleId: VGArticleCardType.FeaturedRightImgTagTitleLead,
          previewImage: '/assets/images/layout-frames/vg/opinion03.jpg',
          hasTitle: true,
          hasLead: true,
          hasImg: true,
          hasAuthorName: true,
          hasAuthorImg: true,
        },
        {
          styleId: VGArticleCardType.FeaturedTopImgTagTitle,
          previewImage: '/assets/images/layout-frames/vg/opinion04.jpg',
          hasTitle: true,
          hasLead: false,
          hasImg: true,
          hasAuthorName: true,
          hasAuthorImg: true,
        },
        {
          styleId: VGArticleCardType.FeaturedRightAuthorImgTagTitle,
          previewImage: '/assets/images/layout-frames/vg/opinion01.jpg',
          hasTitle: true,
          hasLead: false,
          hasImg: false,
          hasAuthorName: true,
          hasAuthorImg: true,
        },
        {
          styleId: VGArticleCardType.FeaturedTopAuthorImgTagTitle,
          previewImage: '/assets/images/layout-frames/vg/opinion02.jpg',
          hasTitle: true,
          hasLead: false,
          hasImg: false,
          hasAuthorName: true,
          hasAuthorImg: true,
        },
        {
          styleId: 101,
          previewImage: '/assets/images/layout-frames/vg/opinion-block01.png',
          hasTitle: true,
          hasLead: true,
          hasAuthorName: true,
          hasAuthorImg: true,
          hasImg: true,
          hasDate: false,
          secondaryContentType: LayoutElementContentType.OPINION_BLOCK,
        },
      ],
    },
    {
      domain: 'magyarNemzet',
      options: [
        {
          styleId: 1,
          previewImage: '/assets/images/layout-frames/mno/opinion01.png',
          hasTitle: true,
          hasLead: true,
          hasImg: false,
          hasAuthorName: true,
          hasAuthorImg: false,
        },
        {
          styleId: 2,
          previewImage: '/assets/images/layout-frames/mno/opinion02.png',
          hasTitle: true,
          hasLead: true,
          hasImg: false,
          hasAuthorName: true,
          hasAuthorImg: true,
        },
        {
          styleId: 3,
          previewImage: '/assets/images/layout-frames/mno/opinion03.png',
          hasTitle: true,
          hasLead: true,
          hasImg: false,
          hasAuthorName: true,
          hasAuthorImg: false,
        },
        {
          styleId: 4,
          previewImage: '/assets/images/layout-frames/mno/opinion04.png',
          hasTitle: true,
          hasLead: true,
          hasImg: false,
          hasAuthorName: true,
          hasAuthorImg: false,
        },
        {
          styleId: 5,
          previewImage: '/assets/images/layout-frames/mno/opinion05.png',
          hasTitle: true,
          hasLead: true,
          hasImg: false,
          hasAuthorName: true,
          hasAuthorImg: false,
        },
        {
          styleId: 6,
          previewImage: '/assets/images/layout-frames/mno/opinion06.png',
          hasTitle: true,
          hasLead: true,
          hasImg: false,
          hasAuthorName: true,
          hasAuthorImg: false,
        },
        {
          styleId: 7,
          previewImage: '/assets/images/layout-frames/mno/opinion07.png',
          hasTitle: true,
          hasLead: false,
          hasImg: false,
          hasAuthorName: false,
          hasAuthorImg: false,
        },
        {
          styleId: 8,
          previewImage: '/assets/images/layout-frames/mno/opinion08.png',
          hasTitle: true,
          hasLead: false,
          hasImg: true,
          hasAuthorName: true,
          hasAuthorImg: true,
        },
        {
          styleId: 9,
          previewImage: '/assets/images/layout-frames/mno/opinion09.png',
          hasTitle: true,
          hasLead: true,
          hasImg: true,
          hasAuthorName: true,
          hasAuthorImg: true,
        },

        {
          styleId: 1,
          previewImage: '/assets/images/layout-frames/mno/bayer_blog01.png',
          hasTitle: true,
          hasLead: true,
          hasImg: true,
          hasAuthorName: true,
          hasAuthorImg: false,
          secondaryContentType: LayoutElementContentType.BAYER_BLOG,
        },
        {
          styleId: 2,
          previewImage: '/assets/images/layout-frames/mno/bayer_blog02.png',
          hasTitle: true,
          hasLead: true,
          hasImg: true,
          hasAuthorName: true,
          hasAuthorImg: true,
          secondaryContentType: LayoutElementContentType.BAYER_BLOG,
        },
        {
          styleId: 3,
          previewImage: '/assets/images/layout-frames/mno/bayer_blog03.png',
          hasTitle: true,
          hasLead: true,
          hasImg: true,
          hasAuthorName: true,
          hasAuthorImg: false,
          secondaryContentType: LayoutElementContentType.BAYER_BLOG,
        },
        {
          styleId: 4,
          previewImage: '/assets/images/layout-frames/mno/bayer_blog04.png',
          hasTitle: true,
          hasLead: true,
          hasImg: true,
          hasAuthorName: true,
          hasAuthorImg: false,
          secondaryContentType: LayoutElementContentType.BAYER_BLOG,
        },

        {
          styleId: 1,
          previewImage: '/assets/images/layout-frames/mno/where-the-ball-will-be01.png',
          hasTitle: true,
          hasLead: true,
          hasImg: true,
          hasAuthorName: true,
          hasAuthorImg: false,
          secondaryContentType: LayoutElementContentType.WHERE_THE_BALL_WILL_BE,
        },
      ],
    },
    {
      domain: 'bors',
      options: [
        {
          styleId: BorsArticleCardType.MainArticle,
          previewImage: '/assets/images/layout-frames/bors/article07.webp',
          hasTitle: true,
          hasLead: false,
          hasImg: true,
          hasAuthorImg: false,
          hasAuthorName: false,
        },
        {
          styleId: BorsArticleCardType.TopSlantImgTagTitle,
          previewImage: '/assets/images/layout-frames/bors/article08.webp',
          hasTitle: true,
          hasLead: false,
          hasImg: true,
          hasAuthorImg: false,
          hasAuthorName: false,
        },
        {
          styleId: BorsArticleCardType.TopSlantImgTagTitlePadding,
          previewImage: '/assets/images/layout-frames/bors/article09.webp',
          hasTitle: true,
          hasLead: false,
          hasImg: true,
          hasAuthorImg: false,
          hasAuthorName: false,
        },
        {
          styleId: BorsArticleCardType.TopImgTagTitle,
          previewImage: '/assets/images/layout-frames/bors/article10.webp',
          hasTitle: true,
          hasLead: false,
          hasImg: true,
          hasAuthorImg: false,
          hasAuthorName: false,
        },
      ],
    },
    {
      domain: 'metropol',
      options: [
        {
          styleId: 1,
          previewImage: '/assets/images/layout-frames/metropol/opinion01.png',
          hasTitle: true,
          hasLead: true,
          hasAuthorName: true,
          hasAuthorImg: false,
          hasImg: false,
          hasDate: false,
          hasReadTime: false,
        },
      ],
    },
    {
      domain: 'mandiner',
      options: [
        {
          styleId: 1,
          previewImage: '/assets/images/layout-frames/mandiner/opinion01.png',
          hasTitle: true,
          hasLead: true,
          hasAuthorName: true,
          hasAuthorImg: true,
          hasImg: false,
          hasDate: true,
          availableFontSizes: [FONT_SIZES[20], FONT_SIZES[22], FONT_SIZES[24]],
        },
        {
          styleId: 2,
          previewImage: '/assets/images/layout-frames/mandiner/opinion02.png',
          hasTitle: true,
          hasLead: true,
          hasAuthorName: true,
          hasAuthorImg: true,
          hasImg: false,
          hasDate: true,
          availableFontSizes: [FONT_SIZES[20], FONT_SIZES[22], FONT_SIZES[24]],
        },
      ],
    },
    {
      domain: 'szabadfold',
      options: [
        {
          styleId: 1,
          previewImage: '/assets/images/layout-frames/szabadfold/opinion01.png',
          hasTitle: true,
          hasLead: true,
          hasAuthorName: false,
          hasAuthorImg: false,
          hasImg: false,
          hasDate: false,
        },
        {
          styleId: 101,
          previewImage: '/assets/images/layout-frames/szabadfold/opinion-block01.png',
          hasTitle: true,
          hasLead: true,
          hasAuthorName: false,
          hasAuthorImg: false,
          hasImg: false,
          hasDate: false,
          secondaryContentType: LayoutElementContentType.OPINION_BLOCK,
        },
      ],
    },
    {
      domain: 'nso',
      options: [
        {
          styleId: 0,
          previewImage: '/assets/images/layout-frames/nso/opinion0.png',
          hasTitle: true,
          hasLead: true,
          hasAuthorName: true,
          hasAuthorImg: false,
          hasImg: false,
          hasDate: false,
          hasReadTime: false,
        },
        {
          styleId: 1,
          previewImage: '/assets/images/layout-frames/nso/opinion1.png',
          hasTitle: true,
          hasLead: true,
          hasAuthorName: true,
          hasAuthorImg: true,
          hasImg: false,
          hasDate: false,
          hasReadTime: false,
        },
      ],
    },
    {
      domain: 'szegedma',
      options: [
        {
          styleId: SzegedmaOpinionCardType.LARGE,
          previewImage: '/assets/images/layout-frames/szegedma/opinion0.png',
          hasTitle: true,
          hasLead: true,
          hasAuthorName: true,
          hasAuthorImg: false,
          hasImg: true,
          hasDate: false,
          hasReadTime: false,
        },
        {
          styleId: SzegedmaOpinionCardType.MEDIUM_WITH_IMAGE,
          previewImage: '/assets/images/layout-frames/szegedma/opinion1.png',
          hasTitle: true,
          hasLead: true,
          hasAuthorName: true,
          hasAuthorImg: false,
          hasImg: true,
          hasDate: false,
          hasReadTime: false,
          secondaryContentType: LayoutElementContentType.OPINION_BLOCK,
        },
        {
          styleId: SzegedmaOpinionCardType.MEDIUM_WITHOUT_IMAGE,
          previewImage: '/assets/images/layout-frames/szegedma/opinion2.png',
          hasTitle: true,
          hasLead: true,
          hasAuthorName: true,
          hasAuthorImg: false,
          hasImg: false,
          hasDate: false,
          hasReadTime: false,
        },
      ],
    },
    {
      domain: 'origo',
      options: [
        {
          styleId: 1,
          previewImage: '/assets/images/layout-frames/origo/opinion-block01.png',
          hasTitle: true,
          hasLead: true,
          hasAuthorName: true,
          hasAuthorImg: true,
          hasImg: true,
          hasDate: false,
          secondaryContentType: LayoutElementContentType.OPINION_BLOCK,
        },
        {
          styleId: OrigoSportOpinionCardType.FeaturedBigImgTagDateTitle,
          previewImage: '/assets/images/layout-frames/origo/origosport-opinion-card01.png',
          hasTitle: true,
          hasLead: true,
          hasAuthorName: true,
          hasAuthorImg: false,
          hasImg: false,
          hasDate: false,
          hasReadTime: false,
        },
      ],
    },
    {
      domain: 'pesti_sracok',
      options: [
        {
          styleId: PestiSracokArticleCardType.Opinion,
          previewImage: '/assets/images/layout-frames/pestisracok/article10.png',
          hasTitle: true,
          hasLead: true,
          hasImg: false,
          hasAuthorImg: false,
          hasAuthorName: false,
        },
      ],
    }
  ];

  constructor(private domainService: DomainService) {}

  ngOnInit(): void {
    Object.keys(this.modalData).forEach((key) => (this[key] = this.modalData[key]));

    this.mobilOrderControl = new UntypedFormControl(this.mobileOrder);
    const defaultContentLength = this.domain === 'vilaggazdasag' && this.secondaryContentType === LayoutElementContentType.OPINION_BLOCK ? 3 : 1;
    this.lengthControl = new UntypedFormControl(this.contentLength ? this.contentLength : defaultContentLength, Validators.required);
    if (
      (this.domain === 'vilaggazdasag' && this.secondaryContentType === LayoutElementContentType.OPINION_BLOCK) ||
      (this.domain === 'magyarNemzet' && this.secondaryContentType === LayoutElementContentType.WHERE_THE_BALL_WILL_BE)
    ) {
      this.lengthControl.disable();
    }
    this.fontSizeControl = new UntypedFormControl(this.fontSize || 20);
    this.fontSizeControl.valueChanges
      .pipe(
        takeUntil(this.destroy$),
        filter((value) => value !== this.selectedOpinionType?.lastSelectedFontSize)
      )
      .subscribe((value) => (this.selectedOpinionType.lastSelectedFontSize = value));
    const isMegyeiLap = this.domainService.isMegyeiLap(this.domain);
    if (isMegyeiLap) {
      this.domainIndex = this.opinionOptions.findIndex((o) => o.domain === 'megyeiLap');
    } else {
      this.domainIndex = this.opinionOptions.findIndex((o) => o.domain === this.domain);
    }
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      // Without timeout this will trigger CD and will cause `ExpressionChangedAfterItHasBeenCheckedError`
      if (this.styleId) {
        const styleId = this.styleOptions.find(({ styleId }) => styleId === this.styleId)?.styleId;
        this.selectTypeIndex(styleId);
      } else if (this.secondaryContentType !== LayoutElementContentType.OPINION) {
        this.selectTypeIndex(this.styleOptions[0].styleId);
      }
    });
  }

  get styleOptions() {
    return this.opinionOptions[this.domainIndex].options.filter(({ secondaryContentType }) =>
      this.secondaryContentType && this.secondaryContentType !== LayoutElementContentType.OPINION
        ? secondaryContentType === this.secondaryContentType
        : !secondaryContentType
    );
  }

  selectArticleType(index: number | string) {
    this.selectedIndex = index;
    this.selectedOpinionType = this.styleOptions[this.selectedIndex];
    this.fontSizeControl.setValue(this.selectedOpinionType?.lastSelectedFontSize || this.selectedOpinionType?.availableFontSizes?.[0].value);
  }

  get fontSizeOptions(): FontSize[] {
    return this.selectedOpinionType?.availableFontSizes || [];
  }

  selectTypeIndex(styleId: number | string) {
    const type = this.styleOptions.find((opinion) => opinion.styleId === styleId);
    this.selectedIndex = this.styleOptions.indexOf(type);
    this.selectedOpinionType = type;
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
