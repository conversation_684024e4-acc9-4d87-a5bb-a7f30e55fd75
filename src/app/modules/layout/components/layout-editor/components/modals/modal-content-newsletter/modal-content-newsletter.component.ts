import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { Component, inject, Input, OnInit } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { DomainKey } from '../../../../../../../core/modules/admin/admin.definitions';
import { BorsNewsletterCardType, MindmegetteNewsletterCardType, OrigoNewsletterType, VGNewsletterBoxType } from '@modules/layout/definitions';

@Component({
  selector: 'app-modal-content-newsletter',
  templateUrl: './modal-content-newsletter.component.html',
  styleUrls: ['./modal-content-newsletter.component.scss'],
  standalone: false,
})
export class ModalContentNewsletterComponent implements OnInit {
  private readonly modalData = inject<Record<string, any>>(NZ_MODAL_DATA, {});
  @Input() domain: DomainKey;
  @Input() hideMobile: boolean;
  @Input() withBlockTitle: boolean;
  @Input() styleId: number;
  @Input() selectedNewsletterType: {
    styleId: number | string;
    previewImage: string;
  };
  @Input() mobileOrder: number;
  @Input() useMobileOrder: boolean;

  selectedIndex = -1;
  domainIndex = -1;
  public mobilOrderControl: UntypedFormControl;
  newsletterOptions: {
    domain: DomainKey;
    options: {
      styleId: number | string;
      previewImage: string;
    }[];
  }[] = [
    {
      domain: 'magyarNemzet',
      options: [
        {
          styleId: 1,
          previewImage: '/assets/images/layout-frames/mno/newsletter-block01.png',
        },
        {
          styleId: 2,
          previewImage: '/assets/images/layout-frames/mno/newsletter-block02.png',
        },
      ],
    },
    {
      domain: 'vilaggazdasag',
      options: [
        {
          styleId: VGNewsletterBoxType.Basic,
          previewImage: '/assets/images/layout-frames/vg/newsletter-block01.jpg',
        },
        {
          styleId: VGNewsletterBoxType.SocialSorted,
          previewImage: '/assets/images/layout-frames/vg/newsletter-block02.jpg',
        },
        {
          styleId: VGNewsletterBoxType.SocialRight,
          previewImage: '/assets/images/layout-frames/vg/newsletter-block03.jpg',
        },
      ],
    },
    {
      domain: 'mandiner',
      options: [
        {
          styleId: 1,
          previewImage: '/assets/images/layout-frames/mandiner/newsletter-block01.png',
        },
      ],
    },
    {
      domain: 'szabadfold',
      options: [
        {
          styleId: 1,
          previewImage: '/assets/images/layout-frames/szabadfold/newsletter-block01.png',
        },
      ],
    },
    {
      domain: 'nso',
      options: [
        {
          styleId: 1,
          previewImage: '/assets/images/layout-frames/nso/newsletter-block01.png',
        },
      ],
    },
    {
      domain: 'life',
      options: [
        {
          styleId: 1,
          previewImage: '/assets/images/layout-frames/life/newsletter-block01.jpg',
        },
      ],
    },
    {
      domain: 'she',
      options: [
        {
          styleId: 1,
          previewImage: '/assets/images/layout-frames/she/newsletter-block01.jpg',
        },
      ],
    },
    {
      domain: 'origo',
      options: [
        {
          styleId: OrigoNewsletterType.OrigoBlue,
          previewImage: '/assets/images/layout-frames/origo/newsletter-block01.png',
        },
        {
          styleId: OrigoNewsletterType.OrigoTransparent,
          previewImage: '/assets/images/layout-frames/origo/newsletter-block04.png',
        },
        {
          styleId: OrigoNewsletterType.Sport,
          previewImage: '/assets/images/layout-frames/origo/newsletter-block03.png',
        },
        {
          styleId: OrigoNewsletterType.Origo,
          previewImage: '/assets/images/layout-frames/origo/newsletter-block02.png',
        },
      ],
    },
    {
      domain: 'mindmegette',
      options: [
        {
          styleId: MindmegetteNewsletterCardType.TopImageCard,
          previewImage: '/assets/images/layout-frames/mme/newsletter-top-img.png',
        },
        {
          styleId: MindmegetteNewsletterCardType.LeftImageCard,
          previewImage: '/assets/images/layout-frames/mme/newsletter-left-img.png',
        },
        {
          styleId: MindmegetteNewsletterCardType.Gastro,
          previewImage: '/assets/images/layout-frames/mme/gasztro/newsletter.jpg',
        },
        {
          styleId: MindmegetteNewsletterCardType.GastroMobile,
          previewImage: '/assets/images/layout-frames/mme/gasztro/newsletter-mobile.jpg',
        },
      ],
    },
    {
      domain: 'bors',
      options: [
        {
          styleId: BorsNewsletterCardType.Default,
          previewImage: '/assets/images/layout-frames/bors/newsletter-block01.png',
        },
      ],
    },
    {
      domain: 'pesti_sracok',
      options: [
        {
          styleId: BorsNewsletterCardType.Default,
          previewImage: '/assets/images/layout-frames/pestisracok/newsletter-block01.png',
        },
      ],
    },
  ];

  constructor() {}

  ngOnInit(): void {
    Object.keys(this.modalData).forEach((key) => (this[key] = this.modalData[key]));
    this.mobilOrderControl = new UntypedFormControl(this.mobileOrder);
    this.domainIndex = this.newsletterOptions.findIndex((o) => o.domain === this.domain);
    this.selectTypeIndex(this.styleId);
  }

  selectType(index: number) {
    this.selectedIndex = index;
    this.selectedNewsletterType = this.newsletterOptions[this.domainIndex].options[this.selectedIndex];
  }

  selectTypeIndex(styleId: number | string) {
    const type = this.newsletterOptions[this.domainIndex].options.find((article) => article.styleId === styleId);
    this.selectedIndex = this.newsletterOptions[this.domainIndex].options.indexOf(type);
    this.selectedNewsletterType = type;
  }
}
