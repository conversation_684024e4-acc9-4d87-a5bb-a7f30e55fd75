import { ContentElementItemType } from '../../layout-core.definitions';
import { mapLayoutVideos } from './selected-mappers/videos';
import { mapLayoutArticles } from './selected-mappers/articles';
import { mapLayoutVote } from './selected-mappers/vote';
import { mapLayoutQuiz } from './selected-mappers/quiz';
import { mapLayoutGalleries } from './selected-mappers/gallery';
import { mapLayoutCompetitions } from './selected-mappers/competitions';
import { mapLayoutDossiers } from './selected-mappers/dossier';
import { mapLayoutTabs } from './selected-mappers/tabs';
import { mapLayoutPrograms } from './selected-mappers/programs';
import { mapLayoutImage } from './selected-mappers/image';
import { mapLayoutWysiwyg } from './selected-mappers/wysiwyg';
import { mapLayoutHtmlContent } from './selected-mappers/html-embed';
import { mapLayoutBrandingBoxes } from './selected-mappers/branding-boxes';
import { mapLayoutTags } from './selected-mappers/tags';
import { mapLayoutTextBox } from './selected-mappers/text-box';
import { mapLayoutDetections } from '@modules/layout/components/layout-iframe/selected-mappers/detections';
import { DomainKey } from '@core/modules/admin/admin.definitions';
import { mapLayoutIngredient } from './selected-mappers/ingredient';
import { mapLayoutRecipes } from './selected-mappers/recipes';
import { mapLayoutArticlesAndRecipes } from './selected-mappers/articles-and-recipes';
import { mapLayoutSelectionItems } from './selected-mappers/selection-item';
import { mapLayoutTurpi } from './selected-mappers/turpi';
import { mapLayoutExperienceOccasions } from '@modules/layout/components/layout-iframe/selected-mappers/experience-occasions';
import { mapLayoutMultiVote } from '@modules/layout/components/layout-iframe/selected-mappers/multi-vote';
import { LayoutElementDynamicConfig } from '@modules/layout/components/layout-configurator/@v2/layout-configurator-definitions';
import { mapLayoutRecipeCategories } from './selected-mappers/recipe-categories';

export const selectedItemPortalMapping = {
  [ContentElementItemType.VIDEOS]: mapLayoutVideos,
  [ContentElementItemType.SHORT_VIDEOS]: mapLayoutVideos,
  [ContentElementItemType.ARTICLES]: mapLayoutArticles,
  [ContentElementItemType.OPINIONS]: mapLayoutArticles,
  [ContentElementItemType.MINUTE_TO_MINUTE]: mapLayoutArticles,
  [ContentElementItemType.VOTE]: mapLayoutVote,
  [ContentElementItemType.MULTI_VOTE]: mapLayoutMultiVote,
  [ContentElementItemType.QUIZ]: mapLayoutQuiz,
  [ContentElementItemType.GALLERIES]: mapLayoutGalleries,
  [ContentElementItemType.COMPETITIONS]: mapLayoutCompetitions,
  [ContentElementItemType.DOSSIERS]: mapLayoutDossiers,
  [ContentElementItemType.NOTES]: mapLayoutArticles,
  [ContentElementItemType.TABS]: mapLayoutTabs,
  [ContentElementItemType.BREAKING]: mapLayoutArticles,
  [ContentElementItemType.PROGRAM]: mapLayoutPrograms,
  [ContentElementItemType.IMAGE]: mapLayoutImage,
  [ContentElementItemType.WYSIWYG]: mapLayoutWysiwyg,
  [ContentElementItemType.HTML_EMBED]: mapLayoutHtmlContent,
  [ContentElementItemType.PODCASTS]: mapLayoutArticles,
  [ContentElementItemType.BRANDING]: mapLayoutBrandingBoxes,
  [ContentElementItemType.TAGS]: mapLayoutTags,
  [ContentElementItemType.RECIPE_CATEGORIES]: mapLayoutRecipeCategories,
  [ContentElementItemType.FAST_NEWS]: mapLayoutArticles,
  [ContentElementItemType.BLOGS]: mapLayoutArticles,
  [ContentElementItemType.TEXT]: mapLayoutTextBox,
  [ContentElementItemType.DETECTIONS]: mapLayoutDetections,
  [ContentElementItemType.INGREDIENT]: mapLayoutIngredient,
  [ContentElementItemType.RECIPE]: mapLayoutRecipes,
  [ContentElementItemType.ARTICLES_AND_RECIPES]: mapLayoutArticlesAndRecipes,
  [ContentElementItemType.SELECTION_SORTING]: mapLayoutSelectionItems,
  [ContentElementItemType.TURPI]: mapLayoutTurpi,
  [ContentElementItemType.EXPERIENCE_OCCASION]: mapLayoutExperienceOccasions,
};

/**
 * Maps the selected items for the portal postMessage layout payload.
 * This is needed in order to serve usable information about items that are only accessible in the CMS.
 * For example with articles we only have access to the original property initially, as these are not filled from BE
 * automatically. Other mappings, like default placeholder values can be also added through these mapper functions.
 * Make sure to set the proper element item type in the selectedItemPortalMapping variable and add your own mapping functions.
 * @param layoutItem
 * @param domain
 */
export const mapSelectedItemsForPortal = (layoutItem: LayoutElementDynamicConfig, domain: DomainKey) => {
  let elementItemTypes = ContentElementItemType;
  let data = {
    ...layoutItem,
  };
  Object.values(elementItemTypes).forEach((type: string) => {
    if (!layoutItem || !(type in layoutItem)) return data;
    if (Array.isArray(layoutItem[type]) && layoutItem[type]?.length > 0) {
      const selectedList = layoutItem[type]?.map((item) => {
        if (type in selectedItemPortalMapping) {
          const mappedValue = selectedItemPortalMapping[type](item, domain);
          return mappedValue;
        }
      });
      data = { ...data, [type]: selectedList };
    } else {
      if (type in selectedItemPortalMapping) {
        data[type] = selectedItemPortalMapping[type](layoutItem[type], domain);
      }
    }
  });
  return data;
};
