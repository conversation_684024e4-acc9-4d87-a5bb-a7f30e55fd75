import {
  ArticleCard,
  AutoFill,
  BlockTitle,
  DossierArticleShort,
  LayoutConfigurationRecipe,
  Maestro,
  RecipeCard,
  HighlightedSelection,
  TapeDataType,
  OlimpiaHungarianTeam,
  FakeBool,
  ExtendedTag,
  SorozatvetoOpinionCard,
  Tag,
} from '@trendency/kesma-ui';
import { ArticleReviews, Column } from 'src/app/core/definitions/article.definitions';
import { MediaStoreResultImage } from 'src/app/media-store/media-store.definitions';
import { GeneratedBodyFormControl } from 'src/app/shared/modules/form-generator2/definitions/form-generator-body-adapters.definitions';
import { ColumnWithIcon } from '@core/api.definitons';
import { Championship, Team } from '@shared/definitions/championship.definitions';
import { GastroExperienceCard } from '@trendency/kesma-ui/lib/definitions/gastro-experience-card.definitions';
import { JournalData } from './definitions/journal.definitions';
import {
  ChampionshipSchedule,
  Competition,
  MindmegetteDailyTurpi,
  MindmegetteIngredient,
  MindmegetteLabelSelectWithRecipesType,
  MindmegetteSortingBox,
} from '@modules/layout/definitions';

// TODO: replace any types with types written based on BE responses

export type AutoFillOptions = Readonly<{
  filterColumns: SourceSelectValue[];
  filterTags: SourceSelectValue[];
  filterPriorities: SourceSelectValue[];
  filterSponsorships: SourceSelectValue[];
  filterArticleNetworkSlots: SourceSelectValue;
  filterDossierNetworkSlots: SourceSelectValue[];
  filterRegions: SourceSelectValue[];
  filterSports: SourceSelectValue[];
  filterPrTags?: SourceSelectValue[];
  filterExperienceCategories?: SourceSelectValue[];
  filterExperience?: SourceSelectValue;
  filterRecipeCategories?: SourceSelectValue[];
  orderPriority: 'date' | 'popularity';
  newest: boolean;
  filterNotOpinion?: boolean;
  filterNotPodcast?: boolean;
  filterNotSponsorship?: boolean;
  filterPodcast?: boolean;
  hasVideo?: boolean;
}>;

export type RecipeAutoFillOptions = Readonly<{
  filterAuthors?: SourceSelectValue[];
  newest: boolean;
  hasImage?: boolean;
  hasSecondaryImage?: boolean;
  filterOnlyMmeWarranty?: boolean;
  hasVideo?: boolean;
  filterTags?: SourceSelectValue[];
  filterRecipeCategories?: SourceSelectValue[];
}>;

export type SelectionAutoFillOptions = Readonly<{
  filterSelections: { id: string }[];
  newest: boolean;
}>;

export interface SourceSelectValue {
  id: string;
  title: string;
  original?: any;
  slug?: any;
  logoThumbnailUrl?: string;
}

export interface ArticleOverwrite {
  title: string;
  image: MediaStoreResultImage;
  lead: string;
  customTag?: string;
  url?: string;
  openInNewTab?: boolean;
}

export type LayoutElementContentConfiguration =
  | LayoutElementContentConfigurationArticle
  | LayoutElementContentConfigurationBlog
  | LayoutElementContentConfigurationBreaking
  | LayoutElementContentConfigurationOpinion
  | LayoutElementContentConfigurationVideo
  | LayoutElementContentConfigurationDossier
  | LayoutElementContentConfigurationPodcastBlock
  | LayoutElementContentConfigurationVideoBlock
  | LayoutElementContentConfigurationGallery
  | LayoutElementContentConfigurationVote
  | LayoutElementContentConfigurationLinkList
  | LayoutElementContentConfigurationHtmlEmbed
  | LayoutElementContentConfigurationBrandingBox
  | LayoutElementContentConfigurationBrandingBoxEx
  | LayoutElementContentConfigurationVisegradPost
  | LayoutElementContentConfigurationFreshBlock
  | LayoutElementContentConfigurationFreshNews
  | LayoutElementContentConfigurationTabsBlock
  | LayoutElementContentConfigurationPrBlock
  | LayoutElementContentConfigurationNote
  | LayoutElementContentConfigurationWysiwyg
  | LayoutElementContentConfigurationImage
  | LayoutElementContentConfigurationShortNews
  | LayoutElementContentConfigurationFastNews
  | LayoutElementContentConfigurationMinuteToMinutes
  | LayoutElementContentConfigurationTrendingTags
  | LayoutElementContentConfigurationTagStrip
  | LayoutElementContentConfigurationQuiz
  | LayoutElementContentConfigurationProgram
  | LayoutElementContentConfigurationBestRecommender
  | LayoutElementContentConfigurationFinalCountdown
  | LayoutElementContentConfigurationHelloBudapest
  | LayoutElementContentConfigurationWithId
  | LayoutElementContentConfigurationIngatlanbazarSearch
  | LayoutElementContentConfigurationDossierList
  | LayoutElementContentConfigurationPodcastList
  | LayoutElementContentConfigurationPdfBox
  | LayoutElementContentConfigurationManualArticle
  | LayoutElementContentConfigurationNewspaper
  | LayoutElementContentConfigurationDataBank
  | LayoutElementContentConfigurationSponsoredArticleBox
  | LayoutElementContentConfigurationMultiVote;

export type LayoutElementContentConfigurationWithId = Readonly<{
  layoutElementId: string;
}>;

export type LayoutElementContentConfigurationWithAutofill<AutofillDefinition, AllowedAutoFillOptions extends keyof AutofillDefinition> = Readonly<{
  autoFill: Pick<AutofillDefinition, AllowedAutoFillOptions>;
  doAutoFill: boolean;
}>;

export interface ColorCombination {
  foreground: string;
  background: string;
}

export type LayoutElementContentConfigurationBestRecommender = LayoutElementContentConfigurationWithId &
  Readonly<{
    selectedColumns: LayoutConfigurationColumn[];
    tabs: {
      autoFill: AutoFill;
      doAutoFill: boolean;
      selectedArticles: LayoutConfigurationArticle[];
    }[];
  }>;

export type LayoutElementContentConfigurationCategoryStepper = LayoutElementContentConfigurationBestRecommender & { interval: number }; // Note: For now this behaves like BestRecommender

export type LayoutElementContentConfigurationArticleWithCount = LayoutElementContentConfigurationArticle & { articleCount?: number };

export type LayoutElementContentConfigurationExperience = LayoutElementContentConfigurationWithId & {
  selectedOccasions: GastroExperienceCard[];
  autoFill: {
    filterExperienceCategories: [];
  };
  doAutoFill: boolean;
};

export type LayoutElementContentConfigurationArticle = LayoutElementContentConfigurationWithId &
  LayoutElementContentConfigurationWithAutofill<
    AutoFillOptions,
    | 'filterColumns'
    | 'filterRegions'
    | 'filterSports'
    | 'filterTags'
    | 'filterPriorities'
    | 'filterSponsorships'
    | 'filterArticleNetworkSlots'
    | 'orderPriority'
    | 'newest'
    | 'hasVideo'
    | 'filterPodcast'
  > &
  Readonly<{
    selectedArticles: LayoutConfigurationArticle[];
    // TODO: remove this prop when BE processes layouts properly
    hasImage: boolean;
    leadingArticle: boolean;
    blockHeaderTitle?: string;
    foregroundColor?: string;
    backgroundColor?: string;
    canModifyContentLength?: boolean;
    filterPodcast?: boolean;
    filterVideo?: boolean;
    isSidebar?: boolean;
    isPublishDate?: boolean;
  }>;

export type LayoutElementContentConfigurationManualArticle = LayoutElementContentConfigurationWithId &
  Readonly<{
    fontSize?: number;
    selectedArticles: LayoutConfigurationArticle[];
    // TODO: remove this prop when BE processes layouts properly
    hasImage: boolean;
    leadingArticle: boolean;
    foregroundColor?: string;
    backgroundColor?: string;
    canModifyContentLength?: boolean;
  }>;

export interface LayoutElementContentConfigurationManualOpinion extends LayoutElementContentConfigurationWithId {
  selectedOpinions: {
    id: string;
    title: string;
    overwrite: {
      title: string;
      image: MediaStoreResultImage;
      lead: string;
    };
    original?: any;
  }[];
  hasLenia?: boolean;
}

export interface LayoutConfigurationArticle {
  id: string;
  overwrite: ArticleOverwrite;
  original?: ArticleCard & {
    primaryColumn?: Column;
    authorName?: string;
    authorRank?: string;
    authorAvatarThumbnailUrl?: string;
    readingLength?: string;
    secondaryThumbnailUrl?: string;
    'editedVersion.dataPrimary.preTitle'?: string;
    thumbnailUrl43?: string;
    originalReviews?: SorozatvetoOpinionCard[];
    videoType?: boolean;
  };
  data?: ArticleCard;
  title?: string;
  visibleFrom?: string;
  visibleUntil?: string;
}

export type LayoutElementContentConfigurationRecipeBase = LayoutElementContentConfigurationWithId &
  Readonly<{
    selectedRecipes: LayoutConfigurationRecipe[];
  }>;

export type LayoutElementContentConfigurationRecipe = LayoutElementContentConfigurationRecipeBase &
  LayoutElementContentConfigurationWithAutofill<
    RecipeAutoFillOptions,
    'newest' | 'filterOnlyMmeWarranty' | 'filterAuthors' | 'hasImage' | 'hasSecondaryImage' | 'hasVideo' | 'filterTags' | 'filterRecipeCategories'
  > &
  Readonly<{
    canModifyContentLength?: boolean;
  }>;

export interface LayoutConfigurationColumn extends ColumnWithIcon {
  slug?: string;
}

export type LayoutElementContentConfigurationOlimpiaArticleWithPodcast = LayoutElementContentConfigurationWithId &
  LayoutElementContentConfigurationWithAutofill<AutoFillOptions, 'filterColumns' | 'filterTags' | 'orderPriority' | 'newest' | 'hasVideo' | 'filterPodcast'> &
  Readonly<{
    selectedArticles: LayoutConfigurationArticle[];
    // TODO: remove this prop when BE processes layouts properly
    hasImage: boolean;
    blockHeaderTitle?: string;
    foregroundColor?: string;
    backgroundColor?: string;
    canModifyContentLength?: boolean;
    filterPodcast?: boolean;
    filterVideo?: boolean;
    isSidebar?: boolean;
    isPublishDate?: boolean;
  }>;

export type LayoutElementContentConfigurationShortNews = LayoutElementContentConfigurationWithId &
  LayoutElementContentConfigurationWithAutofill<
    AutoFillOptions,
    | 'filterColumns'
    | 'filterTags'
    | 'filterRegions'
    | 'filterSports'
    | 'filterPriorities'
    | 'filterSponsorships'
    | 'filterArticleNetworkSlots'
    | 'orderPriority'
    | 'newest'
  > &
  Readonly<{
    selectedShortNews: {
      id: string;
      overwrite: {
        title: string;
        image: MediaStoreResultImage;
        lead: string;
        customTag?: string;
      };
      original?: any;
      title?: string;
      visibleFrom?: string;
      visibleUntil?: string;
    }[];
    // TODO: remove this prop when BE processes layouts properly
    hasImage: boolean;
    leadingArticle: boolean;
    foregroundColor?: string;
    backgroundColor?: string;
  }>;

export type LayoutElementContentConfigurationFastNews = LayoutElementContentConfigurationWithId &
  LayoutElementContentConfigurationWithAutofill<
    AutoFillOptions,
    | 'filterColumns'
    | 'filterTags'
    | 'filterRegions'
    | 'filterSports'
    | 'filterPriorities'
    | 'filterSponsorships'
    | 'filterArticleNetworkSlots'
    | 'orderPriority'
    | 'newest'
  > &
  Readonly<{
    selectedFastNews: {
      //TODO: Check API response
      id: string;
      overwrite: {
        title: string;
        image: MediaStoreResultImage;
        lead: string;
        customTag?: string;
      };
      original?: any;
      title?: string;
      visibleFrom?: string;
      visibleUntil?: string;
    }[];
    // TODO: remove this prop when BE processes layouts properly
    hasImage: boolean;
    leadingArticle: boolean;
    foregroundColor?: string;
    backgroundColor?: string;
  }>;

export type LayoutElementContentConfigurationMinuteToMinutes = LayoutElementContentConfigurationWithId &
  Readonly<{
    selectedArticles: {
      //TODO: Check API response
      id: string;
      overwrite: {
        title: string;
        image: MediaStoreResultImage;
        lead: string;
        customTag?: string;
      };
      original?: any;
      title?: string;
      visibleFrom?: string;
      visibleUntil?: string;
    }[];
    // TODO: remove this prop when BE processes layouts properly
    hasImage: boolean;
    leadingArticle: boolean;
  }>;

export interface LayoutElementContentConfigurationBreaking extends LayoutElementContentConfigurationWithId {
  selectedBreakings: {
    id: string;
    recommended_title?: string;
    overwrite: {
      title: string;
      image: MediaStoreResultImage;
      lead: string;
      customTag?: string;
    };
    original?: any;
  }[];
  // TODO: remove this prop when BE processes layouts properly
  hasImage: boolean;
  hasImg?: boolean; // FIXME: added for type compatibility -> mapped to hasImage because dialogs use this
  doAutoFill: boolean;
  autoFill: {
    filterColumns: SourceSelectValue[];
    filterTags: SourceSelectValue[];
    filterPriorities: SourceSelectValue[];
    filterSponsorships: SourceSelectValue[];
    orderPriority: 'date' | 'popularity';
    newest: boolean;
  };
}

export interface LayoutElementContentConfigurationHero extends LayoutElementContentConfigurationImage {
  selectedDetections?: ({
    image?: {
      thumbnailUrl?: string;
    };
  } & SourceSelectValue)[];
}

export interface LayoutElementContentConfigurationImage extends LayoutElementContentConfigurationWithId {
  cancelMargin: boolean;
  url: string;
  selectedImage: MediaStoreResultImage;
  title?: string;
  logo?: MediaStoreResultImage;
}

export interface LayoutElementContentConfigurationAuthor extends LayoutElementContentConfigurationWithId {
  selectedAuthors: <AUTHORS>
  contentLength?: number;
}

export interface LayoutElementContentConfigurationQuiz extends LayoutElementContentConfigurationWithId {
  selectedQuiz: any;
}

export interface LayoutElementContentConfigurationBlog extends LayoutElementContentConfigurationWithId {
  selectedBlogs: {
    id: string;
    overwrite: {
      title: string;
      image: MediaStoreResultImage;
      lead: string;
    };
    original?: any;
  }[];
  // TODO: remove when BE handles publicapi properly
  autoFill: {
    filterColumns: SourceSelectValue[];
    filterTags: SourceSelectValue[];
    filterPriorities: SourceSelectValue[];
    filterSponsorships: SourceSelectValue[];
    filterArticleNetworkSlots: SourceSelectValue;
    orderPriority: 'date' | 'popularity';
  };
}

export interface LayoutElementContentConfigurationGPNewsBox extends LayoutElementContentConfigurationWithId {
  selectedArticles: {
    id: string;
    overwrite: {
      title: string;
      image: MediaStoreResultImage;
      lead: string;
    };
    original?: any;
  }[];
  // TODO: remove when BE handles publicapi properly
  autoFill: {
    filterColumns: SourceSelectValue[];
    filterTags: SourceSelectValue[];
    filterPriorities: SourceSelectValue[];
    filterSponsorships: SourceSelectValue[];
    filterArticleNetworkSlots: SourceSelectValue;
    orderPriority: 'date' | 'popularity';
  };
  canModifyContentLength: boolean;
}

export interface LayoutElementContentConfigurationEBNews extends LayoutElementContentConfigurationWithId {
  selectedArticles: {
    id: string;
    overwrite: {
      title: string;
      image: MediaStoreResultImage;
      lead: string;
    };
    original?: any;
  }[];
  autoFill: {
    filterColumns: SourceSelectValue[];
    filterTags: SourceSelectValue[];
    filterPriorities: SourceSelectValue[];
    filterSponsorships: SourceSelectValue[];
    filterArticleNetworkSlots: SourceSelectValue;
    orderPriority: 'date' | 'popularity';
  };
  canModifyContentLength: boolean;
}

export interface LayoutElementContentConfigurationBrandingBoxArticle extends LayoutElementContentConfigurationWithId {
  selectedArticles: {
    id: string;
    overwrite: {
      title: string;
      image: MediaStoreResultImage;
      lead: string;
    };
    original?: any;
  }[];
  // TODO: remove when BE handles publicapi properly
  autoFill: AutoFillOptions;
}

export type LayoutElementContentConfigurationRelatedArticles = LayoutElementContentConfigurationWithId &
  LayoutElementContentConfigurationWithAutofill<
    AutoFillOptions,
    | 'filterColumns'
    | 'filterRegions'
    | 'filterSports'
    | 'filterTags'
    | 'filterPriorities'
    | 'filterSponsorships'
    | 'filterArticleNetworkSlots'
    | 'orderPriority'
    | 'newest'
    | 'hasVideo'
  > & {
    selectedArticles: {
      id: string;
      overwrite: {
        title: string;
        image: MediaStoreResultImage;
        lead: string;
      };
      original?: any;
    }[];
    canModifyContentLength: boolean;
    doAutoFill: boolean;
  };

export interface LayoutElementContentConfigurationArticleBlock extends LayoutElementContentConfigurationWithId {
  selectedArticles: {
    id: string;
    overwrite: {
      title: string;
      image: MediaStoreResultImage;
      lead: string;
    };
    original?: any;
  }[];
  canModifyContentLength: boolean;
  doAutoFill: boolean;
}

export type LayoutElementContentConfigurationGastroOccasionRecommender = LayoutElementContentConfigurationWithId &
  LayoutElementContentConfigurationWithAutofill<AutoFillOptions, 'filterExperienceCategories' | 'filterExperience'> & {
    description: string;
    buttonText: string;
    buttonUrl: string;
    backgroundImage: MediaStoreResultImage;
    occasionsCount: number;
    selectedOccasions: {
      id: string;
      title: string;
      experienceSlug: string;
      startOfExperienceEvent: string;
      isComingSoon: FakeBool;
      occasionSug: string;
      lead: string;
      featuredImage: string;
      publicAuthor: {
        id: string;
        fullName: string;
        avatarFullSizeUrl: string;
        isMaestroAuthor: boolean;
        slug: string;
      };
      original: any;
    }[];
    autoFill?: {
      filterExperienceCategories?: SourceSelectValue[];
      filterExperience?: SourceSelectValue;
      skipDisplayFilterExperience?: boolean;
      newest?: boolean;
    };
    doAutoFill: boolean;
    remainingNumberOfSeatsOrder?: boolean;
  };
export type LayoutElementContentConfigurationGastroExperienceRecommendation = LayoutElementContentConfigurationWithId &
  LayoutElementContentConfigurationWithAutofill<AutoFillOptions, 'filterExperienceCategories'> & {
    selectedOccasions: {
      id: string;
      title: string;
      experienceSlug: string;
      startOfExperienceEvent: string;
      isComingSoon: FakeBool;
      occasionSug: string;
      lead: string;
      featuredImage: string;
      publicAuthor: {
        id: string;
        fullName: string;
        avatarFullSizeUrl: string;
        isMaestroAuthor: boolean;
        slug: string;
      };
    }[];
    autoFill?: {
      filterExperienceCategories?: SourceSelectValue[];
    };
    doAutoFill: boolean;
    remainingNumberOfSeatsOrder?: boolean;
  };

export type LayoutElementContentConfigurationGastroThematicRecommender = LayoutElementContentConfigurationWithId & {
  selectedOccasions: {
    id: string;
    title: string;
    experienceSlug: string;
    startOfExperienceEvent: string;
    isComingSoon: FakeBool;
    occasionSug: string;
    lead: string;
    featuredImage: string;
    publicAuthor: {
      id: string;
      fullName: string;
      avatarFullSizeUrl: string;
      isMaestroAuthor: boolean;
      slug: string;
    };
    original: any;
  }[];
  blockTitle?: string;
  buttonUrl?: string;
  autoFill?: {
    filterExperienceCategories?: SourceSelectValue[];
    newest: boolean;
  };
  doAutoFill: boolean;
};

export type LayoutElementContentConfigurationGastroOccasionExperienceSwiper = LayoutElementContentConfigurationWithId & {
  selectedOccasions: {
    id: string;
    title: string;
    experienceSlug: string;
    startOfExperienceEvent: string;
    isComingSoon: FakeBool;
    occasionSug: string;
    lead: string;
    featuredImage: string;
    publicAuthor: {
      id: string;
      fullName: string;
      avatarFullSizeUrl: string;
      isMaestroAuthor: boolean;
      slug: string;
    };
    original: any;
  }[];
  autoFill?: {
    filterExperienceCategories?: SourceSelectValue[];
    newest: boolean;
  };
  doAutoFill: boolean;
};

export interface LayoutElementContentConfigurationSportBlock extends LayoutElementContentConfigurationWithId {
  selectedArticles: {
    id: string;
    overwrite: {
      title: string;
      image: MediaStoreResultImage;
      lead: string;
    };
    original?: any;
  }[];
  // TODO: remove when BE handles publicapi properly
  autoFill: {
    filterColumns: SourceSelectValue[];
    filterTags: SourceSelectValue[];
    filterPriorities: SourceSelectValue[];
    filterSponsorships: SourceSelectValue[];
    filterArticleNetworkSlots: SourceSelectValue;
    orderPriority: 'date' | 'popularity';
  };
}

export interface LayoutElementContentConfigurationArticleSlider extends LayoutElementContentConfigurationWithId {
  selectedArticles: {
    id: string;
    overwrite: {
      title: string;
      image: MediaStoreResultImage;
      lead: string;
    };
    original?: any;
  }[];
  // TODO: remove when BE handles publicapi properly
  autoFill: {
    filterColumns: SourceSelectValue[];
    filterTags: SourceSelectValue[];
    filterPriorities: SourceSelectValue[];
    filterSponsorships: SourceSelectValue[];
    filterArticleNetworkSlots: SourceSelectValue;
    orderPriority: 'date' | 'popularity';
  };
}

export interface LayoutElementContentConfigurationOpinion extends LayoutElementContentConfigurationWithId {
  selectedOpinions: {
    id: string;
    overwrite: {
      title: string;
      image: MediaStoreResultImage;
      lead: string;
    };
    original?: any;
  }[];
  // TODO: remove when BE handles publicapi properly
  hasImage: boolean;
  hasLenia?: boolean;
  canModifyContentLength?: boolean;
  autoFill: {
    filterColumns: SourceSelectValue[];
    filterRegions: SourceSelectValue[];
    filterSports: SourceSelectValue[];
    filterTags: SourceSelectValue[];
    filterPriorities: SourceSelectValue[];
    filterSponsorships: SourceSelectValue[];
    filterArticleNetworkSlots: SourceSelectValue;
    orderPriority: 'date' | 'popularity';
  };
}

export interface LayoutElementContentConfigurationNote extends LayoutElementContentConfigurationWithId {
  selectedNotebooks: {
    id: string;
    overwrite: {
      title: string;
      image: MediaStoreResultImage;
      lead: string;
    };
    original?: any;
  }[];
  // TODO: remove when BE handles publicapi properly
  hasImage: boolean;
  hasLenia?: boolean;
  autoFill: {
    filterColumns: SourceSelectValue[];
    filterTags: SourceSelectValue[];
    filterPriorities: SourceSelectValue[];
    filterSponsorships: SourceSelectValue[];
    filterArticleNetworkSlots: SourceSelectValue | null;
    orderPriority: 'date' | 'popularity';
  };
}

export interface LayoutElementContentConfigurationVideo extends LayoutElementContentConfigurationWithId {
  selectedVideos: {
    id: string;
    overwrite: {
      title: string;
      image: MediaStoreResultImage;
      lead: string;
    };
    original?: any;
  }[];
  autoFill: {
    filterColumns: SourceSelectValue[];
    filterTags: SourceSelectValue[];
  };
}

export interface LayoutElementContentConfigurationShortVideos extends LayoutElementContentConfigurationWithId {
  selectedShortVideos: SourceSelectValue[];
  autoFill: {
    filterColumns: SourceSelectValue[];
    filterTags: SourceSelectValue[];
    orderPriority: string;
    newest: boolean;
  };
}

export interface LayoutElementContentConfigurationDossierList extends LayoutElementContentConfigurationWithId {
  doAutoFill: boolean;
  selectedDossiers: {
    id: string;
    overwriteTitle: string;
    original?: any;
    data?: any;
  }[];
}

export interface LayoutElementContentConfigurationJournal extends LayoutElementContentConfigurationArticle {
  selectedJournal: JournalData;
}

export interface LayoutElementContentConfigurationSelectedDossier {
  id: string;
  overwriteTitle?: string;
  original?: any;
  sponsor?: LayoutElementContentConfigurationSponsorShip;
  mainArticle?: {
    id: string;
    original?: DossierArticleShort;
  };
  secondaryArticles: {
    id: string;
    original?: DossierArticleShort;
  }[];
}

export interface LayoutElementContentConfigurationSponsorShip {
  id: string;
  title?: string;
  thumbnailUrl?: string;
  highlightedColor?: string;
  fontColor?: string;
  url?: string;
}

export interface LayoutElementContentConfigurationDossierRepeater extends LayoutElementContentConfigurationWithId {
  selectedDossiers: {
    id: string;
    original?: any;
    overwriteTitle: string;
    secondaryArticles: {
      id: string;
      original?: any;
    }[];
  }[];
  autoFill: AutoFillOptions;
  doAutoFill: boolean;
}

export interface LayoutElementContentConfigurationDossier extends LayoutElementContentConfigurationWithId {
  selectedDossiers: LayoutElementContentConfigurationSelectedDossier[];
  autoFill: {
    filterDossierNetworkSlots: SourceSelectValue[];
    newest?: boolean;
  };
}
export interface LayoutElementContentConfigurationSorozatveto extends LayoutElementContentConfigurationWithId {
  selectedArticles: LayoutConfigurationArticle[];
  selectedReview?: ArticleReviews;
  hasImage: boolean;
  hasTitle: boolean;
  hasLead: boolean;
}

export interface LayoutElementContentConfigurationDetections extends LayoutElementContentConfigurationWithId {
  selectedDetections: ({
    image?: {
      thumbnailUrl?: string;
    };
  } & SourceSelectValue)[];
}

export interface LayoutElementContentConfigurationPodcastBlock extends LayoutElementContentConfigurationWithId {
  selectedPodcasts: { id: string; title: string; original?: any }[];
  autoFill: {
    filterColumns: SourceSelectValue[];
    filterTags: SourceSelectValue[];
  };
}

export interface LayoutElementContentConfigurationPodcastList extends LayoutElementContentConfigurationWithId {
  selectedPodcasts: {
    id: string;
    overwrite: {
      title: string;
      lead: string;
      image: MediaStoreResultImage;
      time?: string;
      columnTitle?: string;
    };
    original?: any;
  }[];
  autoFill: {
    newest: boolean;
  };
  doAutoFill: boolean;
}

export type LayoutElementContentConfigurationTurpiBox = LayoutElementContentConfigurationWithId &
  LayoutElementContentConfigurationWithAutofill<AutoFillOptions, 'newest'> & {
    selectedBestPractices: {
      id: string;
      description?: string;
      original?: any;
      data?: any;
      title?: string;
      owner?: string;
      savingTitle?: string;
    }[];
    title: string;
    description: string;
    selectedImage: MediaStoreResultImage;
  };

export interface LayoutElementContentConfigurationMoreArticles extends LayoutElementContentConfigurationWithId {
  selectedUrl: {
    urlName: string;
    url: string;
    color: string;
  };
}

export interface LayoutElementContentConfigurationVideoBlock extends LayoutElementContentConfigurationWithId {
  selectedVideos: SourceSelectValue[];
  autoFill: {
    filterColumns: SourceSelectValue[];
    filterTags: SourceSelectValue[];
  };
}

export interface LayoutElementContentConfigurationGallery extends LayoutElementContentConfigurationWithId {
  selectedGalleries: SourceSelectValue[];
  doAutoFill: boolean;
  autoFill?: object;
}

export interface LayoutElementContentConfigurationMultiVote extends LayoutElementContentConfigurationWithId {
  selectedMultiVote: {
    id: string;
    title: string;
    original?: any;
    article?: {
      id: string;
      title: string;
    };
  };
  showCount: boolean;
  showPercent: boolean;
}

export interface LayoutElementContentConfigurationVote extends LayoutElementContentConfigurationWithId {
  selectedVote: {
    id: string;
    title: string;
    original?: any;
    article?: {
      id: string;
      title: string;
    };
  };
  autoFill: {
    filterVotingNetworkSlots: SourceSelectValue;
  };
  showCount: boolean;
  showPercent: boolean;
}

export type LayoutElementContentConfigurationDataBank = LayoutElementContentConfigurationWithId &
  Readonly<{
    selectedCompetitions: any[];
    canModifyContentLength?: boolean;
  }>;

export interface LayoutElementContentConfigurationLinkList extends LayoutElementContentConfigurationWithId {
  linkList: { text: string; url: string; styleId: number }[];
}

export interface LayoutElementContentConfigurationHtmlEmbed extends LayoutElementContentConfigurationWithId {
  htmlContent: { desktop: string; mobile: string };
}

export interface LayoutElementContentConfigurationselectedDidYouKnowBox extends LayoutElementContentConfigurationWithId {
  selectedDidYouKnowBox: [{ id: string; title: string }];
}

export interface LayoutElementContentConfigurationBrandingBox extends LayoutElementContentConfigurationWithId {
  tagLinkList: { text: string; url: string }[];
  selectedBrandingBoxes: SourceSelectValue[];
  // TODO: hogy a régi branding boxok ne törjenek, egyébként törölhető
  selectedBrandArticles?: SourceSelectValue[];
  autoFill: {
    orderPriority: 'date';
    filterBrandingBoxTypes: {
      id: string;
    }[];
  };
  doAutoFill: boolean;
}

export interface LayoutElementContentConfigurationBrandingBoxEx extends LayoutElementContentConfigurationWithId {
  articleLimit: number;
  disabledColumns: SourceSelectValue[];
}

export interface LayoutElementContentConfigurationVisegradPost extends LayoutElementContentConfigurationWithId {
  tagLinkList: { text: string; url: string }[];
  selectedArticles?: SourceSelectValue[];
  autoFill: {
    orderPriority: 'date';
    filterArticleSources: {
      id: string;
    }[];
  };
  doAutoFill: boolean;
}

export interface LayoutElementContentConfigurationProgram extends LayoutElementContentConfigurationWithId {
  selectedPrograms: {
    id: string;
    original?: any;
  }[];
  doAutoFill: boolean;
  autoFill: {
    filterTypes: SourceSelectValue[];
    filterLocations: SourceSelectValue[];
    filterTags: SourceSelectValue[];
  };
}

export interface LayoutElementContentConfigurationTrendingTags extends LayoutElementContentConfigurationWithId {
  trendingTagsMain: {
    publishedAt: string;
    title: string;
    id: string;
    thumbnail?: string;
  }[];
  trendingTagsSecondary: {
    publishedAt: string;
    title: string;
    id: string;
    thumbnail?: string;
  }[];
}

export interface LayoutElementContentConfigurationTeams extends LayoutElementContentConfigurationWithId {
  selectedChampionship: Championship;
  teams: Team[];
}

export interface LayoutElementContentConfigurationUpcomingMatches extends LayoutElementContentConfigurationWithId {
  selectedCompetition: {
    id: string;
    title: string;
    slug: string;
  };
}

export interface LayoutElementContentConfigurationChampionshipTable extends LayoutElementContentConfigurationWithId {
  selectedChampionship: Championship;
}

export interface LayoutElementContentConfigurationTagStrip extends LayoutElementContentConfigurationWithId {
  tags: ExtendedTag[];
}

export interface SourceFilterValue {
  id: string;
  title: string;
  slug: string;
  original?: any;
}

export type LayoutElementContentConfigurationFreshBlock = LayoutElementContentConfigurationWithId &
  LayoutElementContentConfigurationWithAutofill<
    AutoFillOptions,
    | 'filterColumns'
    | 'filterTags'
    | 'filterRegions'
    | 'filterSports'
    | 'filterPriorities'
    | 'filterSponsorships'
    | 'filterArticleNetworkSlots'
    | 'orderPriority'
    | 'newest'
  > &
  Readonly<{
    selectedArticles: {
      id: string;
      overwrite: {
        title: string;
        image: MediaStoreResultImage;
        lead: string;
        customTag?: string;
      };
      original?: any;
      title?: string;
      visibleFrom?: string;
      visibleUntil?: string;
    }[];
  }>;

export interface LayoutElementContentConfigurationLatestNews extends LayoutElementContentConfigurationWithId {
  selectedArticles: {
    id: string;
    overwrite: {
      title: string;
      image: MediaStoreResultImage;
      lead: string;
      customTag?: string;
    };
    original?: any;
    title?: string;
    visibleFrom?: string;
    visibleUntil?: string;
  }[];
  autoFill: {
    filterPriorities: SourceSelectValue[];
    filterColumns: SourceFilterValue[];
    orderPriority: 'date' | 'popularity';
    newest: boolean;
    filterNotOpinion: boolean;
    filterNotPodcast: boolean;
    filterNotSponsorship: boolean;
  };
  doAutoFill?: boolean;
}

export interface LayoutElementContentConfigurationFreshNews extends LayoutElementContentConfigurationWithId {
  autoFill: {
    filterPriorities: SourceSelectValue[];
    filterColumns: SourceFilterValue[];
    filterTags: SourceFilterValue[];
  };
  doAutoFill?: boolean;
}

export interface LayoutElementContentConfigurationLeadEditors extends LayoutElementContentConfigurationWithId {
  editors: string[];
}

export interface LayoutElementContentConfigurationFinalCountdown extends LayoutElementContentConfigurationWithId {
  deadLine: string | Date;
  endText: string;
  backgroundColor: string;
  color: string;
  size: number;
}

export interface LayoutElementContentConfigurationIngatlanbazarSearch extends LayoutElementContentConfigurationWithId {
  showCountyLocationsWithBudapest: boolean;
  showBudapestLocations: boolean;
  showCountyLocations: boolean;
  showOtherLocations: boolean;
  showAdvertiseButton: boolean;
  showNewBuildButton: boolean;
  defaultLocation: number | undefined;
  defaultType: string | undefined;
  utmSource: string | undefined;
}

export type LayoutElementContentConfigurationTabsBlock = LayoutElementContentConfigurationWithId &
  Readonly<{
    tabs: TabsBlockTab[];
  }>;

export interface LayoutElementContentConfigurationHelloBudapest extends LayoutElementContentConfigurationWithId {
  selectedArticles: SourceSelectValue[];
  autoFill: {
    filterColumns: SourceSelectValue[];
  };
  doAutoFill: boolean;
  hasImage: true;
}

type TabsBlockTab = LayoutElementContentConfigurationWithAutofill<
  AutoFillOptions,
  | 'filterColumns'
  | 'filterTags'
  | 'filterRegions'
  | 'filterSports'
  | 'filterPriorities'
  | 'filterSponsorships'
  | 'filterArticleNetworkSlots'
  | 'orderPriority'
  | 'newest'
> & {
  id: string;
  title: string;
  slug: string;
  tabIndex: number;
  selectedArticles: {
    id: string;
    overwrite: {
      title: string;
      image: MediaStoreResultImage;
      lead: string;
    };
    original?: any;
    title?: string;
  }[];
};

export type LayoutElementContentConfigurationPrBlock = LayoutElementContentConfigurationWithId &
  LayoutElementContentConfigurationWithAutofill<
    AutoFillOptions,
    | 'filterColumns'
    | 'filterTags'
    | 'filterRegions'
    | 'filterSports'
    | 'filterPriorities'
    | 'filterSponsorships'
    | 'filterArticleNetworkSlots'
    | 'filterPrTags'
    | 'orderPriority'
    | 'newest'
  > &
  Readonly<{
    title: string;
    selectedArticles: {
      id: string;
      overwrite: {
        title: string;
        image: MediaStoreResultImage;
        lead: string;
        customTag?: string;
      };
      title?: string;
      visibleFrom?: string;
      visibleUntil?: string;
      original?: any;
      blockTitle?: BlockTitle;
    }[];
    // TODO: remove this prop when BE processes layouts properly
    hasImage: boolean;
    leadingArticle: boolean;
    foregroundColor?: string;
    backgroundColor?: string;
  }>;

export type LayoutElementContentConfigurationWysiwyg = LayoutElementContentConfigurationWithId &
  Readonly<{
    formControls: GeneratedBodyFormControl[];
  }>;

export interface LayoutElementContentConfigurationPdfBox extends LayoutElementContentConfigurationWithId {
  boxTitle: string;
  title: string;
  lead?: string;
  btnUrl: string;
  target: '_blank' | '_self';
  isOpenNewTab?: boolean;
}

export interface LayoutElementContentConfigurationArticlesWithPodcast extends LayoutElementContentConfigurationArticle {
  btnUrl: string;
}

export type LayoutElementContentConfigurationNewspaper = LayoutElementContentConfigurationWithId &
  Readonly<{
    printUrl?: string;
    onlineUrl?: string;
  }>;

export interface LayoutElementContentConfigurationConference extends LayoutElementContentConfigurationWithId {
  title: string;
  lead: string;
  date: string;
  location: string;
  url: string;
  indexImage1: MediaStoreResultImage;
  indexImage2: MediaStoreResultImage;
}

export interface LayoutElementContentConfigurationConference extends LayoutElementContentConfigurationWithId {
  title: string;
  lead: string;
  date: string;
  location: string;
  url: string;
  indexImage1: MediaStoreResultImage;
  indexImage2: MediaStoreResultImage;
}

export interface LayoutElementContentConfigurationTripBox extends LayoutElementContentConfigurationWithId {
  tripName: string;
  allResult?: string;
  matchesNumber?: number;
  matches: ChampionshipSchedule[];
  selectedChampion?: Competition;
}

export interface LayoutElementContentConfigurationRecipeCategorySelect extends LayoutElementContentConfigurationWithId {
  blockTitle?: string;
  recipeCategories: (MindmegetteLabelSelectWithRecipesType & {
    id: string;
    recipes: (RecipeCard & { id: string })[];
  })[];
}

export interface LayoutElementContentConfigurationTextBox extends LayoutElementContentConfigurationWithId {
  text: string;
}

export interface LayoutElementContentConfigurationIngredient extends LayoutElementContentConfigurationWithId {
  selectedIngredients: (MindmegetteIngredient & { id: string })[];
}

export interface LayoutElementContentConfigurationSponsoredArticleBox extends LayoutElementContentConfigurationWithId {
  isHideSponsorName: boolean;
  selectedArticles: LayoutConfigurationArticle[];
  autoFill: {
    filterColumns: SourceSelectValue[];
    filterTags: SourceSelectValue[];
    filterSponsorships: SourceSelectValue[];
  };
}

export type LayoutElementContentConfigurationGuaranteeBox = LayoutElementContentConfigurationWithId &
  Readonly<{
    selectedArticlesAndRecipes: (LayoutConfigurationRecipe | LayoutConfigurationArticle)[];
    blockTitle: {
      url: string;
      name?: string;
    };
  }>;

export type LayoutElementContentConfigurationTurpiCard = LayoutElementContentConfigurationWithId &
  LayoutElementContentConfigurationWithAutofill<AutoFillOptions, 'newest'> & {
    selectedBestPractices: (MindmegetteDailyTurpi & { id: string })[];
  };

export type LayoutElementContentConfigurationOfferBox = LayoutElementContentConfigurationWithId &
  LayoutElementContentConfigurationWithAutofill<
    AutoFillOptions,
    | 'filterColumns'
    | 'filterTags'
    | 'filterRegions'
    | 'filterSports'
    | 'filterPriorities'
    | 'filterSponsorships'
    | 'filterArticleNetworkSlots'
    | 'orderPriority'
    | 'newest'
  > & {
    selectedArticles: LayoutConfigurationArticle[];
    blockTitle?: string;
  };

export interface LayoutElementContentConfigurationMaestroBox extends LayoutElementContentConfigurationWithId {
  title: string;
  description: string;
  authors: (Maestro & { id: string; slug: string; avatarThumbnailUrl?: string })[];
}

export interface LayoutElementContentConfigurationPublicAuthors extends LayoutElementContentConfigurationWithId {
  authors: Record<string, string>[];
}

export interface LayoutElementContentConfigurationHighlightedSelection extends LayoutElementContentConfigurationWithId {
  selection?: HighlightedSelection & { id: string };
}

export type LayoutElementContentConfigurationSelection = LayoutElementContentConfigurationWithId &
  LayoutElementContentConfigurationWithAutofill<SelectionAutoFillOptions, 'newest' | 'filterSelections'> & {
    sortingBoxData?: Omit<MindmegetteSortingBox, 'data' | 'highlightedImageUrl'> & {
      highlightedImage: MediaStoreResultImage;
    };
    selectedSelectionItems: SelectedSelectionItemType[];
    selection?: {
      id: string;
      slug: string;
      title: string;
    };
  };

export type SelectedSelectionItemType = ((LayoutConfigurationRecipe | LayoutConfigurationArticle) & { contentType: TapeDataType }) | null;

export interface LayoutElementContentConfigurationCountdownBox extends LayoutElementContentConfigurationWithId {
  endDate: Date;
  exactTime: boolean;
  endText: string;
  slug: string;
  mainText?: string;
  subText?: string;
  valueSuffixText?: string;
  logoImage?: MediaStoreResultImage;
  sponsorImage?: MediaStoreResultImage;
  sponsorUrl?: string;
}

export interface LayoutElementContentConfigurationServicesBox extends LayoutElementContentConfigurationWithId {
  services: {
    title: string;
    logoImage: MediaStoreResultImage;
    url: string;
  }[];
}

export interface LayoutElementContentConfigurationEbCountdownBlockTitle extends LayoutElementContentConfigurationWithId {
  title: string;
  slug: string;
}

export interface LayoutElementContentConfigurationOlimpiaCountdownBlockTitle extends LayoutElementContentConfigurationWithId {
  title: string;
  slug: string;
}

export interface LayoutElementContentConfigurationOlimpiaHungarianTeam extends LayoutElementContentConfigurationWithId {
  selectedAthletes: OlimpiaHungarianTeam[];
}

export interface LayoutElementContentConfigurationOlimpiaNews extends LayoutElementContentConfigurationWithId {
  selectedArticles: {
    id: string;
    overwrite: {
      title: string;
      image: MediaStoreResultImage;
      lead: string;
    };
    original?: any;
  }[];
  autoFill: {
    filterColumns: SourceSelectValue[];
    filterTags: SourceSelectValue[];
    filterPriorities: SourceSelectValue[];
    filterSponsorships: SourceSelectValue[];
    filterArticleNetworkSlots: SourceSelectValue;
    orderPriority: 'date' | 'popularity';
  };
  canModifyContentLength: boolean;
}

export interface LayoutElementContentConfigurationTopTenTags extends LayoutElementContentConfigurationWithId {
  tags: Tag[];
}

export interface LayoutElementContentConfigurationSecretDaysCalendar extends LayoutElementContentConfigurationWithId {
  selectedCalendar: any;
}
