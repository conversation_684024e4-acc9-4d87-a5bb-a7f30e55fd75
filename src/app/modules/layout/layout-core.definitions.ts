import { DomainKey } from '../../core/modules/admin/admin.definitions';
import { FontSize } from '@shared/definitions/font-size.definitions';
import { Image } from '../../media-store/media-store.definitions';
import { ChampionshipSchedule, Competition, CustomMapType, StaticWeatherMapType } from '@modules/layout/definitions';

export enum LayoutPageType {
  HOME = 'HomePage',
  COLUMN = 'Column',
  OPINION = 'Opinion',
  SIDEBAR = 'Sidebar',
  COLUMNSIDEBAR = 'ColumnSidebar',
  CUSTOM_BUILT_PAGE = 'CustomBuiltPage',
}

export enum ContentElementItemType {
  QUIZ = 'selectedQuiz',
  SHORT_NEWS = 'selectedShortNews',
  FAST_NEWS = 'selectedFastNews',
  ARTICLES = 'selectedArticles',
  COMPETITIONS = 'selectedCompetitions',
  NOTES = 'selectedNotebooks',
  OPINIONS = 'selectedOpinions',
  VIDEOS = 'selectedVideos',
  SHORT_VIDEOS = 'selectedShortVideos',
  VOTE = 'selectedVote',
  MULTI_VOTE = 'selectedMultiVote',
  DOSSIERS = 'selectedDossiers',
  PODCASTS = 'selectedPodcasts',
  GALLERIES = 'selectedGalleries',
  BREAKING = 'selectedBreakings',
  PROGRAM = 'selectedPrograms',
  BRANDING = 'selectedBrandingBoxes',
  BLOGS = 'selectedBlogs',
  TABS = 'tabs',
  IMAGE = 'selectedImage',
  BLOG = 'blog',
  DETECTIONS = 'selectedDetections',
  DATA_BANK = 'selectedCompetitions',
  RECIPE = 'selectedRecipes',
  INGREDIENT = 'selectedIngredients',
  SELECTION = 'selectedSortingBoxData',
  MINUTE_TO_MINUTE = 'minute-to-minute',
  WYSIWYG = 'formControls',
  HTML_EMBED = 'htmlContent',
  TAGS = 'tags',
  RECIPE_CATEGORIES = 'recipeCategories',
  TEXT = 'text',
  ARTICLES_AND_RECIPES = 'selectedArticlesAndRecipes',
  SELECTION_SORTING = 'selectedSelectionItems',
  TURPI = 'selectedBestPractices',
  EXPERIENCE_OCCASION = 'selectedOccasions',
  PODCAST_LIST = 'selectedPodcasts',
}

export enum LayoutElementType {
  ROW = 'row',
  COLUMN = 'column',
  CONTENT = 'content',
}

export enum LayoutElementContentType {
  WEEKLY_NEWSPAPER_BOX = 'weekly-newspaper-box',
  COLUMN_BOX = 'column-box',
  ARTICLE = 'article',
  AD = 'ad',
  STOCK = 'stock',
  NOTE = 'note',
  OPINION = 'opinion',
  OPINION_LIST = 'opinion-list',
  OPINION_BLOCK = 'opinion-block',
  VIDEO = 'video',
  DOSSIER = 'dossier',
  PODCAST_BLOCK = 'podcast-block',
  PODCAST_LIST = 'podcast-list',
  VIDEO_BLOCK = 'video-block',
  NEWSLETTER_BLOCK = 'newsletter-block',
  NEWSLETTER_BLOCK_GONG = 'newsletter-block-gong',
  FRESH_BLOCK = 'fresh-block',
  FRESH_NEWS = 'fresh-news',
  LINK_LIST = 'link-list',
  VOTE = 'vote',
  GALLERY = 'gallery',
  BRANDING_BOX = 'branding-box',
  BRANDING_BOX_EX = 'branding-box-ex',
  BRANDING_BOX_ARTICLE = 'branding-box-article',
  VISEGRAD_POST = 'visegrad-post',
  CULTURE_NATION = 'culture-nation',
  HTML_EMBED = 'html-embed',
  NEWSPAPER = 'newspaper',
  SOCIAL_MEDIA = 'social-media',
  ASTROLOGY = 'astrology',
  TABS = 'tabs',
  BREAKING = 'breaking',
  AGROKEP = 'agrokep',
  AGROKEP_LIST = 'agrokep-list',
  PR_BLOCK = 'pr-block',
  WYSIWYG = 'wysiwyg',
  IMAGE = 'image',
  BLOG = 'blog',
  INGATLANBAZAR = 'ingatlanbazar',
  INGATLANBAZAR_CONFIGURABLE = 'ingatlanbazar-configurable',
  INGATLANBAZAR_SEARCH = 'ingatlanbazar-search',
  TAG_BLOCK = 'tag-block',
  TRENDING_TAGS_BLOCK = 'trending-tags-block',
  SHORT_NEWS = 'shortNews',
  FAST_NEWS = 'fast-news',
  QUIZ = 'quiz',
  SPONSORED_QUIZ = 'sponsored-quiz',
  YESSFACTOR_BLOCK = 'yessfactor-block',
  KOMPOST_BLOCK = 'kompost-block',
  RIPOST7_BLOCK = 'ripost7-block',
  PROGRAM = 'program',
  KOPONYEG = 'koponyeg',
  ASTRONET_CIKKEK = 'astronet-cikkek',
  ASTRONET_HOROSZKOP = 'astronet-horoszkop',
  ASTRONET_JOSLAS = 'astronet-joslas',
  ASTRONET_BRANDING_BOX = 'astronet-branding-box',
  ASTRONET_COLUMNS = 'astronet-columns',
  BEST_RECOMMENDER = 'best-recommender',
  CATEGORY_STEPPER = 'category-stepper',
  BROWN_RECOMMENDER = 'brown-recommender',
  ALLASKERESO = 'allaskereso',
  WAZE = 'waze',
  SZAKIKERESO = 'szakikereso',
  HELLO_BUDAPEST = 'hello-budapest',
  FINAL_COUNTDOWN = 'final-countdown',
  DOSSIER_LIST = 'dossier-list',
  BROADCAST_RECOMMENDER = 'broadcast-recommender',
  SONG_TOP_LIST = 'song-top-list',
  ARTICLES_WITH_VIDEO_CONTENT = 'articles-with-video-content',
  PDF_BOX = 'pdf-box',
  DOSSIER_REPEATER = 'dossier-repeater',
  MANUAL_OPINION = 'manual-opinion',
  MANUAL_ARTICLE = 'manual-article',
  KULTUR_NEMZET = 'kulturnemzet',
  ARTICLES_WITH_PODCAST_CONTENT = 'articles-with-podcast-content',
  PODCAST_ARTICLE_LIST = 'podcast-article-list',
  GALLERY_ARTICLE_LIST = 'gallery-article-list',
  RSS_BOX = 'rss-box',
  MAP_RECOMMENDATIONS = 'map-recommendations',
  TENYEK_BOX = 'tenyek-box',
  DETECTIONS = 'detections',
  HERO = 'hero',
  TWELVE_DAYS_FORECAST = 'twelve-days-forecast',
  MEDICAL_METEOROLOGY = 'medical-meteorology',
  IMAGE_MAP_LIST = 'image-map-list',
  DRAWN_MAP_LIST = 'drawn-map-list',
  SPORT_RADIO_PLAYER = 'sport-radio-player',
  NEWS_FEED = 'news-feed',
  VISITOR_COUNTER = 'visitor-counter',
  DATA_BANK = 'data-bank',
  LEAD_EDITORS = 'lead-editors',
  TEAMS = 'teams',
  UPCOMING_MATCHES = 'upcoming-matches',
  TRIP_BOX = 'trip-box',
  CHAMPIONSHIP_TABLE = 'championship-table',
  SOROZATVETO = 'sorozatveto',
  WRITE_TO_US = 'write-to-us',
  MOST_VIEWED = 'most-viewed',
  TOP_STORIES = 'top-stories',
  MEDIA_PANEL = 'media-panel',
  LATEST_NEWS = 'latest-news',
  SPOTLIGHT = 'spotlight',
  GP_NEWS_BOX = 'gp-news-box',
  SPORT_BLOCK = 'sport-block',
  RECIPE_CATEGORY_SELECT = 'recipe-category-select',
  MORE_ARTICLES = 'more-articles',
  ARTICLE_SLIDER = 'article-slider',
  TEXT_BOX = 'text-box',
  TURPI_BOX = 'turpi-box',
  AUTHOR = 'author',
  TURPI_CARD = 'turpi-card',
  INGREDIENT = 'ingredient',
  RECIPE = 'recipe',
  RECIPE_SWIPER = 'recipe-swiper',
  RELATED_ARTICLES = 'related-articles',
  LIVE_BAR = 'live-bar',
  GUARANTEE_BOX = 'guarantee-box',
  MINUTE_TO_MINUTE = 'minute-to-minute',
  OFFER_BOX = 'offer-box',
  MAESTRO_BOX = 'maestro-box',
  ARTICLE_BLOCK = 'article-block',
  DID_YOU_KNOW = 'did-you-know',
  BLOCK_SEPARATOR = 'block-separator',
  APP_DOWNLOAD = 'app-download',
  SPONSORED_ARTICLE_BOX = 'sponsored-article-box',
  HIGHLIGHTED_SELECTION = 'highlighted-selection',
  SELECTION = 'selection',
  DAILY_MENU = 'daily-menu',
  OPINION_NEWSLETTER_BOX = 'opinion-newsletter-box',
  TELEKOM_VIVICITTA = 'telekom-vivicitta',
  CONFERENCE = 'conference',
  DAILY_PROGRAM = 'daily-program',
  EB_NEWS = 'eb-news',
  COUNTDOWN_BOX = 'countdown-box',
  ELECTIONS_BOX = 'elections-box',
  EB_COUNTDOWN_BLOCK_TITLE = 'eb-countdown-block-title',
  EB_SINGLE_ELIMINATION = 'eb-single-elimination',
  OLIMPIA_COUNTDOWN_BLOCK_TITLE = 'olimpia-countdown-block-title',
  OLIMPIA_NEWS = 'olimpia-news',
  OLIMPIA_HUNGARIAN_COMPETITIONS = 'olimpia-hungarian-competitions',
  OLIMPIA_HUNGARIAN_TEAM = 'olimpia-hungarian-team',
  OLIMPIA_RESULTS_BLOCK = 'olimpia-results-block',
  OLIMPIA_ARTICLES_WITH_PODCAST_CONTENT = 'olimpia-articles-with-podcast-content',
  OLIMPIA_LARGE_NAVIGATOR = 'olimpia-large-navigator',
  BAYER_BLOG = 'bayer-blog',
  WEEKLY_MENU = 'weekly-menu',
  PUBLIC_AUTHORS = 'public-authors',
  MULTI_VOTE = 'multi-vote',
  SERVICES_BOX = 'services-box',
  PODCAST_APP_RECOMMENDER = 'podcast-app-recommender',
  EXPERIENCE_GIFT = 'experience-gift',
  EVENT_CALENDAR = 'event-calendar',
  GASTRO_OCCASION_RECOMMENDER = 'gastro-occasion-recommender',
  GASTRO_EXPERIENCE_RECOMMENDATION = 'gastro-experience-recommendation',
  GASTRO_EXPERIENCE_OCCASION = 'gastro-experience-occasion',
  GASTRO_THEMATIC_RECOMMENDER = 'gastro-thematic-recommender',
  GASTRO_EXPERIENCE_OCCASION_SWIPER = 'gastro-experience-occasion-swiper',
  TOP_RANKING_GLOSSARY = 'top-ranking-glossary',
  JOB_LISTINGS = 'job-listings',
  WHERE_THE_BALL_WILL_BE = 'where-the-ball-will-be',
  TOP_TEN_TAGS = 'top-ten-tags',
  CONFIGURABLE_SPONSORED_BOX = 'configurable-sponsored-box',
  SPONSORED_VOTE = 'sponsored-vote',
  STAR_BIRTHS = 'star-births',
  COLUMN_BLOCK = 'column-block',
  LATEST_AND_MOST_READ_ARTICLES = 'latest-and-most-read-articles',
  SUB_COLUMNS = 'sub-columns',
  TOPIC_SUGGESTION = 'topic-suggestion',
  SECRET_DAYS_CALENDAR = 'secret-days-calendar',
  TOP_COMMENTED_ARTICLES = 'top-commented-articles',
  SHORT_VIDEOS = 'short-videos',
}

export interface LayoutStructureChangedData {
  id: string;
  isCreate: boolean;
  contentType: LayoutElementContentType;
  itemType: ContentElementItemType;
}

export interface BlockTitle {
  text: string;
  url: string;
  urlName?: string;
  sponsored?: boolean;
  overrideColor?: string;
}

export interface LayoutElement {
  readonly id: string;
  readonly type: LayoutElementType;
  hideMobile: boolean;
  withBlockTitle: boolean;
  blockTitle?: BlockTitle;
  selectedImage?: Image;
}

export interface LayoutElementRow extends LayoutElement {
  readonly type: LayoutElementType.ROW;
  widthDesktop: number;
  widthDesktopRecursive: number;
  backgroundColor: string;
  hasLine?: boolean;
  elements: (LayoutElementColumn | LayoutElementContent)[];
  isMobileSideBySide?: boolean;
}

export interface LayoutElementColumn extends LayoutElement {
  readonly type: LayoutElementType.COLUMN;
  widthDesktop: number;
  widthDesktopRecursive: number;
  elements: (LayoutElementRow | LayoutElementContent)[];
  hasLeftMargin?: boolean;
  hasRightMargin?: boolean;
  marginBorderColor?: 'green' | 'gray';
  fontSize?: number;
  mobileOrder?: number;
}

export interface LayoutElementContent extends LayoutElement {
  readonly type: LayoutElementType.CONTENT;
  readonly contentType: LayoutElementContentType;
  readonly secondaryContentType?: LayoutElementContentType;
  readonly previewImage: string;
  readonly configurable: true | false;
  contentLength: number;
  mobileOrder?: number;
}

export interface BasicLayoutElementPreview {
  styleId: number;
  previewImage: string;
}

export type DetectionType = Partial<BasicLayoutElementPreview> & {
  useComponent?: boolean;
  minParentWidth?: number;
  firstPreviewImage?: string; // Use this if you don't want to show multiple times the previewImage on layout tab.
  isList?: boolean;
  allowedPageTypes?: LayoutPageType[];
  hasWithImageFilter?: boolean;
  automataFill?: boolean;
};

export interface LayoutElementSelection extends BasicLayoutElementPreview {
  maxContentLength: number;
  hasBackground: boolean;
}

export interface LayoutElementDrawnMapList extends BasicLayoutElementPreview {
  mapType: CustomMapType;
}

export interface LayoutElementImageMapList extends BasicLayoutElementPreview {
  mapType: StaticWeatherMapType;
}

export interface LayoutElementVideoPreview extends BasicLayoutElementPreview {
  hasTitle: boolean;
  hasLead: boolean;
  hasCoverImage: boolean;
  hasWatchTime: boolean;
}

export interface LayoutElementArticlePreview extends BasicLayoutElementPreview {
  hasTitle: boolean;
  hasImage: boolean;
  hasImg?: boolean; // FIXME added for type compatibilty as this is used in the background and `hasImage` is mapped
  hasLead: boolean;
  canHaveCustomTag?: boolean;
  hasDate?: boolean;
  hasReadTime?: boolean;
}

export interface LayoutElementGalleryPreview extends BasicLayoutElementPreview {
  hasTitle: boolean;
  hasImage: boolean;
  galleryLength: number;
  galleryOverwriteEnabled?: boolean;
  galleryLengthOverwrite?: number;
  isDisabledOverwriteGalleryLength?: boolean;
  maxGalleryLength?: number;
}

export interface LayoutElementOpinionListPreview extends BasicLayoutElementPreview {
  hasTitle: boolean;
}

export interface LayoutElementOpinionPreview {
  hasTitle: boolean;
  hasLead: boolean;
  hasImg: boolean;
  hasDate?: boolean;
  hasReadTime?: boolean;
  hasAuthorName: boolean;
  hasAuthorImg: boolean;
  secondaryContentType?: LayoutElementContentType;
  availableFontSizes?: FontSize[];
  lastSelectedFontSize?: number;
  styleId: number | string;
  previewImage: string;
}

export interface LayoutElementVideoArticlesPreview extends BasicLayoutElementPreview {
  articleCount: number;
}

export interface LayoutElementPreviewConfig<T = BasicLayoutElementPreview> {
  domain: DomainKey;
  options: T[];
}

export type LayoutEditorModalResult<T = {}> = {
  hideMobile: boolean;
  withBlockTitle: boolean;
} & T;

export type LayoutEditorModalResultMulti<T = {}> = LayoutEditorModalResult & {
  contentLength: number;
  mobileOrder?: number;
  styleId?: number;
  previewImage?: string;
} & T;

export type LayoutEditorModalResultItems<T = {}> = LayoutEditorModalResult & {
  itemCount: number;
} & T;

export interface PdfBox {
  boxTitle?: string;
  title: string;
  lead?: string;
  btnUrl: string;
}

export interface LayoutElementContentTripBox extends LayoutElementContent {
  selectedChampion?: Competition;
  matchesNumber: number;
  tripName: string;
  matches: ChampionshipSchedule[];
}
