import { Routes } from '@angular/router';
import { DoctorAnswerComponent } from '@modules/doctor-answer/doctor-answer.component';
import { doctorAnswerResolver } from '@modules/doctor-answer/doctor-answer.resolver';

export const doctorAnswerRouting: Routes = [
  {
    path: '',
    component: DoctorAnswerComponent,
    resolve: { list: doctorAnswerResolver },
    pathMatch: 'full',
  },
  {
    path: 'editor',
    loadChildren: () => import('src/app/item-editor-page/item-editor-page.module').then((m) => m.ItemEditorPageModule),
  },
];
