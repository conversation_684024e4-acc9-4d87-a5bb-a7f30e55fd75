import { Component, OnInit } from '@angular/core';
import { BaseContentComponent } from '@modules/base-content.component';
import { IBaseContentConfig } from '@core/core.definitions';
import { DataTableModule } from '@shared/modules/data-table/data-table.module';
import { IHttpOptions } from '@external/http';
import { CustomTemplateType, DataTableEventType, IDataTableActionInfo, IDataTableColumnInfo } from '@shared/definitions/data-table.definitions';
import { ContentData } from '@shared/modules/form-generator/form-generator.definitions';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-doctor-answer',
  templateUrl: './doctor-answer.component.html',
  styleUrl: './doctor-answer.component.scss',
  imports: [DataTableModule],
})
export class DoctorAnswerComponent extends BaseContentComponent implements OnInit {
  ngOnInit(): void {
    this.initTableDataAndConfig(this.route.snapshot.data.list);
  }

  get config(): IBaseContentConfig {
    this.formApiCall = this.getFormInfo.bind(this);
    return {
      contentType: 'doctor-answer',
      contentListType: 'doctor-answer',
      previewPageRoute: null,
      tableTitle: 'Orvos válaszol',
      editorType: 'modal',
      contentGroup: 'doctor-answer',
      dataColumns: this.dataColumns,
      formApiCall: this.getFormInfo.bind(this),
    };
  }

  list(params?: IHttpOptions): Observable<object> {
    return this.apiService.doctorAnswer.list({ params } as IHttpOptions);
  }

  create(data: ContentData): Observable<object> {
    return this.apiService.doctorAnswer.create(data);
  }

  update(id: string, data: ContentData): Observable<object> {
    return this.apiService.doctorAnswer.update(id, data);
  }

  delete(id: string): Observable<object> {
    return this.apiService.doctorAnswer.delete(id);
  }

  restore(id: string): Observable<object> {
    return this.apiService.doctorAnswer.restore(id);
  }

  publish(id: string): Observable<object> {
    return this.apiService.doctorAnswer.toActive(id);
  }

  unpublish(id: string): Observable<object> {
    return this.apiService.doctorAnswer.toInactive(id);
  }

  getFormInfo(id?: string): Observable<object> {
    return this.apiService.doctorAnswer.getFormInfo(id);
  }

  private get dataColumns(): IDataTableColumnInfo[] {
    return [
      {
        key: 'subject',
        property: 'subject',
        title: 'subject',
      },
      {
        key: 'title',
        property: 'title',
        title: 'title',
      }
    ];
  }

  getUniqueRowActionModuleConfigs(): IDataTableActionInfo[] {
    return [
      {
        key: DataTableEventType.UPDATE,
        eventHandler: this.edit.bind(this),
        label: 'edit',
        buttonType: 'primary',
        iconName: 'edit',
        newTabOption: false,
      },
      {
        key: DataTableEventType.DELETE,
        eventHandler: this.onDelete.bind(this),
        buttonType: 'danger',
        actionType: 'confirm',
        iconName: 'delete',
        label: 'delete',
      },
    ];
  }
}
