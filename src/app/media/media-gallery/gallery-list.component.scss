@use 'shared' as *;

:host {
  .deleted {
    width: 100%;
    font-weight: bold;
    text-align: center;
  }

  .cover-image {
    width: 80px;
    object-fit: contain;
  }

  .advanced-search-view-button {
    margin-bottom: 10px;
  }

  app-data-table ::ng-deep {
    // Make highlightedImageUrlThumbnail and isActive columns narrower.
    th[data-columnKey='highlightedImageUrlThumbnail'],
    th[data-columnKey='isActive'] {
      width: 110px;
    }

    .table-title {
      font-size: 22px;
      margin-right: auto;
    }

    .global-action {
      margin-right: 0;
    }

    .global-actions {
      width: 100%;
      border-bottom: 1px solid $gray-medium;
      margin-bottom: 20px;
      display: flex;
      gap: 10px;
    }
  }
}
