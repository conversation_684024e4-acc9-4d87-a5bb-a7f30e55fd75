import { ChangeDetectionStrategy, Component, DestroyRef, ElementRef, inject, OnInit, signal, TemplateRef, ViewChild } from '@angular/core';
import { BaseContentComponent } from '@modules/base-content.component';
import { IBaseContentConfig } from '@core/core.definitions';
import {
  CustomTemplateType,
  DataTableEventType,
  DataTableInputType,
  IDataTableActionInfo,
  IDataTableColumnInfo,
  IDataTableMeta,
} from '@shared/definitions/data-table.definitions';
import { Observable, of } from 'rxjs';
import { ApiResult } from '@trendency/kesma-ui';
import { DataTableModule } from '@shared/modules/data-table/data-table.module';
import { CommonModule } from '@angular/common';
import { IHttpOptions } from '@external/http';
import { catchError, switchMap, take, tap } from 'rxjs/operators';
import { NgZorroModule } from '@shared/modules/ng-zorro/ng-zorro.module';
import { GalleryList, GalleryQueryParams } from '@media/media-gallery/definitions';
import { GalleryFiltersComponent } from '@media/media-gallery/components';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { GalleryApiManagerService } from '@media/media-gallery/services/gallery-api-manager.service';
import { starNamesInUse } from '@shared/utils/star.utils';
import { HttpResponse } from '@angular/common/http';

@Component({
  selector: 'app-gallery-list',
  templateUrl: 'gallery-list.component.html',
  styleUrl: 'gallery-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [DataTableModule, NgZorroModule, CommonModule, GalleryFiltersComponent],
})
export class GalleryListComponent extends BaseContentComponent implements OnInit {
  @ViewChild('coverImageTemplate', { static: true }) coverImageTemplate: TemplateRef<ElementRef>;

  isLoading = signal<boolean>(true);
  queryParams = signal<object>({});
  filteredQueryParams = signal<object>({});

  private readonly imageType = this.route.snapshot.data?.imageType;

  private readonly galleryService = inject(GalleryApiManagerService).getInstance(this.imageType);
  private readonly destroyRef = inject(DestroyRef);

  ngOnInit(): void {
    this.route.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((params) => {
      this.queryParams.set(params);
    });
    this.initTableDataAndConfig(this.route.snapshot.data.galleries);
    this.isLoading.set(false);
  }

  list(params?: IHttpOptions): Observable<ApiResult<GalleryList, IDataTableMeta>> {
    this.isLoading.set(true);
    return this.galleryService
      .galleries$({
        params: {
          ...params,
          ...this.filteredQueryParams(),
        },
      } as IHttpOptions)
      .pipe(tap(() => this.isLoading.set(false)));
  }

  delete(id: string): Observable<void> {
    if (this.imageType === 'starDictionary') {
      return this.galleryService.getStarGalleriesPlacesOfUseList$(id).pipe(
        switchMap(({ data }) => {
          if (data?.length) {
            this.sharedService.showNotification('error', starNamesInUse('galéria', data));
            return;
          }
          return this.galleryService.delete$(id);
        }),
        catchError((err) => {
          this.sharedService.showNotification('error', err.error?.data?.message);
          return of(null);
        })
      );
    }

    return this.galleryService.delete$(id);
  }

  restore(id: string): Observable<void> {
    return this.galleryService.restore$(id);
  }

  publish(id: string): Observable<object> {
    return this.galleryService.public$(id);
  }

  unpublish(id: string): Observable<object> {
    if (this.imageType === 'starDictionary') {
      return this.galleryService.getStarGalleriesPlacesOfUseList$(id).pipe(
        switchMap(({ data }) => {
          if (data?.length) {
            this.sharedService.showNotification('error', starNamesInUse('galéria', data));
            return;
          }
          return this.galleryService.nonPublic$(id);
        }),
        catchError((err) => {
          this.sharedService.showNotification('error', err.error?.data?.message);
          return of(null);
        })
      );
    }

    return this.galleryService.nonPublic$(id);
  }

  handleGlobalAction(): void {
    this.router
      .navigate(['create'], {
        relativeTo: this.route,
      })
      .then();
  }

    public exportArticles(): void {
      this.apiService
        .exportGalleries(this.filteredQueryParams)
        .pipe(
          tap((res: HttpResponse<Blob>) => {
            const blob = res.body as Blob;
            const contentDisposition = res.headers.get('Content-Disposition');
            const match = contentDisposition.match(/filename[^;=\n]*=(['"]?)([^'";\n]+)\1/);
            const fileName = match ? match[2] : 'file.xlsx';
            const objectUrl = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = objectUrl;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(objectUrl);
          })
        )
        .pipe(take(1))
        .subscribe();
    }

  openEditorAsReadonlyMode(id: string): void {
    this.router
      .navigate(['show', id], {
        relativeTo: this.route,
      })
      .then();
  }

  onFilterChange(queryParams: GalleryQueryParams): void {
    // Convert the queryParams object to a new object with only the properties that have a truthy value.
    const filteredParams = Object.fromEntries(Object.entries(queryParams).filter(([_, value]) => !!value));
    const params = {
      ...this.queryParams(),
      ...filteredParams,
    };
    this.filteredQueryParams.set(filteredParams);
    this.list(params as IHttpOptions).subscribe((galleries) => {
      this.initTableDataAndConfig(galleries);
      this.forceUpdateTableParams(galleries.meta);
    });
  }

  get isDeletedList(): boolean {
    return this.queryParams()?.['status'] === 'deleted';
  }

  get config(): IBaseContentConfig {
    return {
      contentType: 'gallery',
      contentListType: 'gallery',
      previewPageRoute: null,
      tableTitle: 'Galériák',
      editorType: 'editor',
      contentGroup: 'content-group',
      dataColumns: this.dataColumns,
    };
  }

  get dataColumns(): IDataTableColumnInfo[] {
    const { status } = this.route.snapshot.queryParams;
    return [
      {
        key: 'highlightedImageUrlThumbnail',
        property: 'highlightedImageUrlThumbnail',
        title: 'coverImage',
        customTemplate: this.coverImageTemplate,
      },
      {
        key: 'title',
        property: 'title',
        title: 'title',
        orderable: {
          queryKey: 'title_order',
        },
      },
      {
        key: 'photographer',
        property: 'photographer',
        title: 'photographer',
        orderable: {
          queryKey: 'photographer_order',
        },
      },
      {
        key: 'author',
        property: 'author',
        title: 'author',
        orderable: {
          queryKey: 'user_order',
        },
      },
      {
        key: 'isActive',
        property: 'isActive',
        title: 'isActive',
        customTemplateType: CustomTemplateType.IS_ACTIVE,
        orderable: {
          queryKey: 'isActive_order',
        },
      },
      {
        key: 'createDate',
        property: 'createDate',
        title: 'createdAt',
        customTemplateType: CustomTemplateType.DATE_TIME,
        orderable: {
          queryKey: 'createDate_order',
        },
      },
      {
        key: 'publicDate',
        property: 'publicDate',
        title: 'publicDate',
        customTemplateType: CustomTemplateType.DATE_TIME,
        orderable: {
          queryKey: 'publicDate_order',
        },
      },
      {
        key: 'deletedAt',
        property: 'deletedAt',
        title: 'deletedAt',
        customTemplateType: CustomTemplateType.DATE_TIME,
      },
      {
        key: 'deletedBy',
        property: 'deletedBy',
        title: 'deletedBy',
      },
    ].filter(({ key }) => {
      if (status === 'deleted') {
        return !['isActive', 'publicDate'].includes(key);
      }
      return !['deletedAt', 'deletedBy'].includes(key);
    });
  }

  getUniqueGlobalActionModuleConfigs(): IDataTableActionInfo[] {
    return [
      {
        key: DataTableEventType.LIST_DELETED,
        inputType: DataTableInputType.SWITCH,
        buttonType: 'primary',
        label: 'labelDeleted',
        onLabel: 'labelList',
        queryParamKey: 'status',
        queryParamValue: 'deleted',
      },
      {
        key: DataTableEventType.CREATE,
        inputType: DataTableInputType.BUTTON,
        label: 'create',
        buttonType: 'primary',
        eventHandler: this.edit.bind(this),
      },
    ];
  }

  getUniqueRowActionModuleConfigs(): IDataTableActionInfo[] {
    return [
      {
        key: DataTableEventType.UPDATE,
        eventHandler: this.edit.bind(this),
        label: 'edit',
        buttonType: 'primary',
        iconName: 'edit',
        newTabOption: true,
      },
      {
        key: DataTableEventType.DELETE,
        eventHandler: this.onDelete.bind(this),
        buttonType: 'danger',
        actionType: 'confirm',
        iconName: 'delete',
        label: 'delete',
      },
    ];
  }
}
