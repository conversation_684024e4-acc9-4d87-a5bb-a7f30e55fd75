<nz-card *ngIf="dataTableConfig">
  <app-data-table
    [loading]="isLoading()"
    [enableSorting]="true"
    [enableSearch]="false"
    [tableData]="dataTableData"
    [tableConfig]="dataTableConfig"
    [customActionTemplate]="customActionTemplate"
    (rowActionTriggered)="handleAction($event, 'rowActions')"
    (globalActionTriggered)="handleGlobalAction()"
    (queryParamsChanged)="refreshTableData($event)"
  >
    <app-gallery-filters data-table-header-field (filterChange)="onFilterChange($event)" [isDeletedList]="isDeletedList"></app-gallery-filters>
    <div data-table-custom-global-actions>
      <button class="advanced-search-view-button" nz-button nzType="primary" (click)="exportArticles()">Exportálás</button>
    </div>
  </app-data-table>
</nz-card>

<ng-template #coverImageTemplate let-coverImage="data">
  @if (!!coverImage) {
    <img class="cover-image" [src]="coverImage" alt="" />
  } @else {
    <nz-tag class="deleted" nzColor="darkred">Törölve</nz-tag>
  }
</ng-template>

<ng-template #customActionTemplate let-data="data" let-isDeletedList="isDeletedList">
  <nz-button-group class="row-action" *ngIf="isDeletedList">
    <button nz-button nzType="primary" (click)="openEditorAsReadonlyMode(data.id)">
      <span nz-icon nzType="eye"></span>
    </button>
  </nz-button-group>
</ng-template>
