export type MediaObject = {
  altText?: string;
  caption?: string;
  date: Date;
  fileName: string;
  fileSize: number;
  focusPointX?: number;
  focusPointY?: number;
  id: number;
  //Meta props
  mediaDetailsMeta?: {
    description?: string;
    source?: string;
    photographer?: string;
    headline?: string;
  };
  photographer?: string;
  source?: string;
  title?: string;
  userId?: string;
  userName?: string;
  variantIds: number[];
};

export type MediaQueryParams = {
  only_mine?: boolean | string;
  text?: string;
  from_date?: string;
  to_date?: string;
  limit?: number;
  offset?: number;
};
