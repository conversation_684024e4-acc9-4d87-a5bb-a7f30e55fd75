import { ChangeDetectionStrategy, Component, input, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NgZorroModule } from '@shared/modules/ng-zorro/ng-zorro.module';
import { FilterComponent } from '@media/shared/components/filter.component';
import { format, sub } from 'date-fns';
import { MediaQueryParams } from '@media/shared/definitions/media.definitions';

@Component({
  selector: 'app-media-filters',
  templateUrl: 'media-filters.component.html',
  styleUrl: 'media-filters.component.scss',
  imports: [FormsModule, NgZorroModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MediaFiltersComponent extends FilterComponent<MediaQueryParams> implements OnInit {
  readonly buttonText = input<string>('Általam feltöltött képek');

  override dates: Date[] = [sub(new Date(), { months: 6 }), new Date()];

  ngOnInit(): void {
    this.searchTerm$.subscribe();
  }

  onDateChange(): void {
    const [from, to] = this.dates;
    this.queryParams = {
      ...this.queryParams,
      from_date: from ? format(from, "yyyy-MM-dd'T00:00:01'") : null,
      to_date: to ? format(to, "yyyy-MM-dd'T23:59:59'") : null,
    };
    this.search();
  }
}
