import { ChangeDetectorRef, Component, DestroyRef, inject, Input, signal } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { MEDIA_CONSTANTS } from '@media/shared/constans';
import { Selection } from '@media/shared/selection';
import { MediaQueryParams } from '@media/shared/definitions/media.definitions';
import { InitModelService } from '@core/services/model/init-model/init-model.service';

@Component({
  template: ``,
})
export class ListComponent<DataType extends { id: number }> {
  @Input() selection = new Selection<DataType>();

  protected canLoadMore = true;
  protected data = new Map<number, DataType>([]);

  private readonly _loadMore$ = new BehaviorSubject({ limit: MEDIA_CONSTANTS.itemsPerPage, offset: 0 });
  readonly loadMore$ = this._loadMore$.asObservable();

  private readonly _isLoading$ = new BehaviorSubject(true);
  readonly isLoading$ = this._isLoading$.asObservable();

  protected readonly cdr = inject(ChangeDetectorRef);
  protected readonly initModelService = inject(InitModelService);
  protected readonly destroyRef = inject(DestroyRef);
  protected readonly nzData = inject(NZ_MODAL_DATA);
  /**
   * Provides access to the current NzModal reference.
   * @optional
   */
  protected readonly modalRef = inject(NzModalRef, { optional: true });

  readonly mediaConstants = MEDIA_CONSTANTS;

  readonly count = signal<number>(0);
  readonly pageIndex = signal<number>(1);

  get currentFilters(): object {
    return this._loadMore$.value;
  }

  setIsLoading(isLoading: boolean): void {
    this._isLoading$.next(isLoading);
  }

  pushOrUpdate(data: DataType | DataType[]): void {
    const iterator = Array.isArray(data) ? data : [data];
    iterator.forEach((item) => {
      this.data.set(item.id, item);
      this.selection.isPicked(item) && this.selection.set(item);
    });
    this.cdr.markForCheck();
  }

  unshift(data: DataType): void {
    this.data = new Map([[data.id, data], ...this.data.entries()]);
    this.cdr.markForCheck();
  }

  remove(data: DataType): void {
    this.selection.removeIfPicked(data);
    this.data.delete(data.id);
    this.cdr.markForCheck();
  }

  /**
   * Use this if you need to load results by scrolling instead of pagination.
   */
  scrollEvent(event: Event): void {
    if (!this.canLoadMore) {
      return;
    }
    const target = event.target as HTMLElement;
    const bottomPosition = target.scrollHeight - target.clientHeight;
    const isBottom = Math.abs(target.scrollTop - bottomPosition) < 1;

    // If the user is not at the bottom of the element or the loading is already in progress,
    // return early to prevent unnecessary requests.
    if (!isBottom || this._isLoading$.value) {
      return;
    }
    this.setIsLoading(true);
    this.triggerLoadMore();
  }

  handlePaginationChange(index: number): void {
    this.pageIndex.set(index);
    this.onFilterChange({
      ...this.currentFilters,
      offset: MEDIA_CONSTANTS.itemsPerPage * (index - 1),
    });
  }

  onFilterChange(queryParams: MediaQueryParams): void {
    if (queryParams?.only_mine) {
      queryParams.only_mine = this.initModelService.data.userId;
    }
    // Convert the queryParams object to a new object with only the properties that have a truthy value.
    queryParams = Object.fromEntries(Object.entries(queryParams).filter(([_, value]) => !!value));
    this.data.clear();
    this.selection.clear();
    this.setIsLoading(true);
    this.resetWithFilters(queryParams);
  }

  resetWithFilters(queryParams: MediaQueryParams): void {
    this._loadMore$.next({
      offset: 0,
      limit: MEDIA_CONSTANTS.itemsPerPage,
      ...queryParams,
    });
  }

  private triggerLoadMore(): void {
    const offset = this._loadMore$.value?.offset + this._loadMore$.value?.limit || 0; // Default to 0 if no offset is set (i.e., first page)

    this._loadMore$.next({
      ...this._loadMore$.value,
      limit: MEDIA_CONSTANTS.itemsPerPage,
      offset,
    });
  }
}
