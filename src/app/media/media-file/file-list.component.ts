import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { ListComponent } from '@media/shared/components/list.component';
import { FileMimeType, MediaFile } from '@media/media-file/definitions/file.definitions';
import { switchMap } from 'rxjs';
import { tap } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FileService } from '@media/media-file/services/file.service';
import { AsyncPipe, NgIf, NgTemplateOutlet } from '@angular/common';
import { NzEmptyComponent } from 'ng-zorro-antd/empty';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import { NzPaginationComponent } from 'ng-zorro-antd/pagination';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzCardComponent } from 'ng-zorro-antd/card';
import { NzListComponent, NzListItemComponent } from 'ng-zorro-antd/list';
import { NzTagComponent } from 'ng-zorro-antd/tag';
import { StorageIECFormatPipe } from '@media/shared/pipes/storage-iec-format.pipe';
import { MediaFiltersComponent } from '@media/shared/components/media-filters/media-filters.component';
import { FileUploaderComponent } from '@media/media-file/components';
import { DropZoneDirective } from '@media/shared/directives/drop-zone.directive';

@Component({
  selector: 'app-file-list',
  templateUrl: './file-list.component.html',
  styleUrl: './file-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    AsyncPipe,
    NgIf,
    NzEmptyComponent,
    NzSpinComponent,
    NzPaginationComponent,
    NzIconDirective,
    NgTemplateOutlet,
    NzCardComponent,
    NzListComponent,
    NzListItemComponent,
    NzTagComponent,
    StorageIECFormatPipe,
    MediaFiltersComponent,
    FileUploaderComponent,
    DropZoneDirective,
  ],
})
export class FileListComponent extends ListComponent<MediaFile> implements OnInit {
  private readonly fileService = inject(FileService);

  readonly FileMimeType = FileMimeType;

  ngOnInit(): void {
    this.loadMore$
      .pipe(
        switchMap((queryParams) => this.fileService.files$(queryParams)),
        tap(() => this.setIsLoading(false)),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ count, files }) => {
        this.count.set(count);
        this.pushOrUpdate(files);
      });
  }
}
