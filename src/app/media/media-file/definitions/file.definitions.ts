import { MediaObject } from '@media/shared/definitions/media.definitions';

export type MediaFile = MediaObject & {
  type: 'File';
  url: {
    fullSize: string;
  };
  selectedVariant: {
    mimeType: string;
    fileSize: number;
    id: number;
  };
};

export enum FileMimeType {
  XLSX = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  PDF = 'application/pdf',
}

export type MediaFileFormObj = {
  variantId: number;
  fileName: string;
  fileUrl: string;
  source: string;
  description: string;
  mimeType: FileMimeType;
  fileSize: number;
  createdAt: Date;
};
