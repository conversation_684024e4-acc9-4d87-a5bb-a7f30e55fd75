export const CANVAS_WIDTH = 1280;
export const CANVAS_HEIGHT = 720;

/**
 * Aspect Ratio Calculator.
 * https://www.digitalrebellion.com/webapps/aspectcalc
 */
export const MOSAIC_EDITOR_CONFIG = {
  template_01: {
    key: 'template_01',
    numberOfCrops: 3,
    thumbnail: 'assets/images/mosaic/template_01.png',
    originBase64: null,
    ratios: [0.59, 0.59, 0.59],
    drawAction: (ctx: CanvasRenderingContext2D, images: ImageBitmap[]): void => {
      const columnWidth = CANVAS_WIDTH / 3;
      images.forEach((image, index) => drawImage(ctx, image, columnWidth * index, 0, columnWidth, CANVAS_HEIGHT));
    },
  },
  template_02: {
    key: 'template_02',
    numberOfCrops: 2,
    thumbnail: 'assets/images/mosaic/template_02.png',
    originBase64: null,
    ratios: [0.59, 1.18],
    drawAction: (ctx: CanvasRenderingContext2D, images: ImageBitmap[]): void => {
      const columnWidth = CANVAS_WIDTH / 3;
      drawImage(ctx, images[0], 0, 0, columnWidth, CANVAS_HEIGHT);
      drawImage(ctx, images[1], columnWidth, 0, columnWidth * 2, CANVAS_HEIGHT);
    },
  },
  template_03: {
    key: 'template_03',
    numberOfCrops: 3,
    thumbnail: 'assets/images/mosaic/template_03.png',
    originBase64: null,
    ratios: [0.59, 1.18, 1],
    drawAction: (ctx: CanvasRenderingContext2D, images: ImageBitmap[]): void => {
      MOSAIC_EDITOR_CONFIG.template_02.drawAction(ctx, images);
      const columnWidth = CANVAS_WIDTH / 3;
      const dx = columnWidth * 2 + columnWidth / 2;
      const dy = CANVAS_HEIGHT - CANVAS_HEIGHT * 0.3;
      const radius = 210;
      ctx.save();
      ctx.beginPath();
      ctx.arc(dx, dy, radius, 0, Math.PI * 2, true);
      ctx.clip();
      drawImage(ctx, images[2], dx - radius, dy - radius, radius * 2, radius * 2);
      ctx.beginPath();
      ctx.arc(dx, dy, radius, 0, Math.PI * 2);
      ctx.lineWidth = 4;
      ctx.stroke();
      ctx.restore();
    },
    roundCropper: [2],
  },
  template_04: {
    key: 'template_04',
    numberOfCrops: 2,
    thumbnail: 'assets/images/mosaic/template_04.png',
    originBase64: null,
    ratios: [16 / 9, 0.59],
    drawAction: (ctx: CanvasRenderingContext2D, images: ImageBitmap[]): void => {
      const columnWidth = CANVAS_WIDTH / 4;
      drawImage(ctx, images[0], 0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
      drawImage(ctx, images[1], CANVAS_WIDTH - columnWidth, 0, columnWidth, CANVAS_HEIGHT * 0.75);
    },
  },
  template_05: {
    key: 'template_05',
    numberOfCrops: 3,
    thumbnail: 'assets/images/mosaic/template_05.png',
    originBase64: null,
    ratios: [0.89, 0.89, 1.18],
    drawAction: (ctx: CanvasRenderingContext2D, images: ImageBitmap[]): void => {
      const halfCanvasWidth = CANVAS_WIDTH / 2;
      drawImage(ctx, images[0], 0, 0, halfCanvasWidth, CANVAS_HEIGHT);
      drawImage(ctx, images[1], halfCanvasWidth, 0, halfCanvasWidth, CANVAS_HEIGHT);
      drawImage(ctx, images[2], CANVAS_WIDTH / 3 - 70, CANVAS_HEIGHT / 2, CANVAS_WIDTH / 3, CANVAS_HEIGHT / 2);
    },
  },
  template_06: {
    key: 'template_06',
    numberOfCrops: 2,
    thumbnail: 'assets/images/mosaic/template_06.png',
    originBase64: null,
    ratios: [16 / 9, 4.26],
    drawAction: (ctx: CanvasRenderingContext2D, images: ImageBitmap[]): void => {
      const columnWidth = CANVAS_WIDTH / 3;
      drawImage(ctx, images[0], 0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
      drawImage(ctx, images[1], CANVAS_WIDTH - columnWidth, 40, columnWidth, 100);
    },
  },
  template_07: {
    key: 'template_07',
    numberOfCrops: 2,
    thumbnail: 'assets/images/mosaic/template_07.png',
    originBase64: null,
    ratios: [16 / 9, 1],
    drawAction: (ctx: CanvasRenderingContext2D, images: ImageBitmap[]): void => {
      const columnWidth = CANVAS_WIDTH / 5;
      drawImage(ctx, images[0], 0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
      drawImage(ctx, images[1], CANVAS_WIDTH - columnWidth, 40, columnWidth, CANVAS_HEIGHT / 3);
    },
  },
  template_08: {
    key: 'template_08',
    numberOfCrops: 4,
    thumbnail: 'assets/images/mosaic/template_08.png',
    originBase64: null,
    ratios: [2.24, 1.31, 1.31, 2.24],
    drawAction: (ctx: CanvasRenderingContext2D, images: ImageBitmap[]): void => {
      const columnWidthShort = CANVAS_WIDTH * 0.37;
      const columnWidthLong = CANVAS_WIDTH - columnWidthShort;
      const columnHeight = CANVAS_HEIGHT / 2;
      drawImage(ctx, images[0], 0, 0, columnWidthLong, columnHeight);
      drawImage(ctx, images[1], columnWidthLong, 0, columnWidthShort, columnHeight);
      drawImage(ctx, images[2], 0, columnHeight, columnWidthShort, columnHeight);
      drawImage(ctx, images[3], columnWidthShort, columnHeight, columnWidthLong, columnHeight);
    },
  },
  template_09: {
    key: 'template_09',
    numberOfCrops: 4,
    thumbnail: 'assets/images/mosaic/template_09.png',
    originBase64: null,
    ratios: [0.59, 1.18, 1.18, 0.59],
    drawAction: (ctx: CanvasRenderingContext2D, images: ImageBitmap[]): void => {
      const columnWidth = CANVAS_WIDTH / 3;
      const columnHeight = CANVAS_HEIGHT / 2;
      drawImage(ctx, images[0], 0, 0, columnWidth, CANVAS_HEIGHT);
      drawImage(ctx, images[1], columnWidth, 0, columnWidth, columnHeight);
      drawImage(ctx, images[2], columnWidth, columnHeight, columnWidth, columnHeight);
      drawImage(ctx, images[3], columnWidth * 2, 0, columnWidth, CANVAS_HEIGHT);
    },
  },
  template_10: {
    key: 'template_10',
    numberOfCrops: 5,
    thumbnail: 'assets/images/mosaic/template_10.png',
    originBase64: null,
    ratios: [16 / 9, 16 / 9, 16 / 9, 16 / 9, 1],
    drawAction: (ctx: CanvasRenderingContext2D, images: ImageBitmap[]): void => {
      const radius = 250;
      const columnWidth = CANVAS_WIDTH / 2;
      const columnHeight = CANVAS_HEIGHT / 2;

      drawImage(ctx, images[0], 0, 0, columnWidth, columnHeight);
      drawImage(ctx, images[1], columnWidth, 0, columnWidth, columnHeight);
      drawImage(ctx, images[2], 0, columnHeight, columnWidth, columnHeight);
      drawImage(ctx, images[3], columnWidth, columnHeight, columnWidth, columnHeight);

      ctx.save();
      ctx.beginPath();
      ctx.arc(CANVAS_WIDTH / 2, CANVAS_HEIGHT / 2, radius, 0, Math.PI * 2, false);
      ctx.closePath();
      ctx.clip();

      drawImage(ctx, images[4], CANVAS_WIDTH / 2 - radius, CANVAS_HEIGHT / 2 - radius, radius * 2, radius * 2);

      ctx.beginPath();
      ctx.arc(CANVAS_WIDTH / 2, CANVAS_HEIGHT / 2, radius, 0, Math.PI * 2);
      ctx.lineWidth = 4;
      ctx.stroke();
      ctx.restore();
    },
    roundCropper: [4],
  },
  template_11: {
    key: 'template_11',
    numberOfCrops: 5,
    thumbnail: 'assets/images/mosaic/template_11.png',
    originBase64: null,
    ratios: [1.18, 1.18, 1.18, 16 / 9, 16 / 9],
    drawAction: (ctx: CanvasRenderingContext2D, images: ImageBitmap[]): void => {
      const columnWidthTop = CANVAS_WIDTH / 3;
      const columnWidthBottom = CANVAS_WIDTH / 2;
      const columnHeight = CANVAS_HEIGHT / 2;
      drawImage(ctx, images[0], 0, 0, columnWidthTop, columnHeight);
      drawImage(ctx, images[1], columnWidthTop, 0, columnWidthTop, columnHeight);
      drawImage(ctx, images[2], columnWidthTop * 2, 0, columnWidthTop, columnHeight);
      drawImage(ctx, images[3], 0, columnHeight, columnWidthBottom, columnHeight);
      drawImage(ctx, images[4], columnWidthBottom, columnHeight, columnWidthBottom, columnHeight);
    },
  },
};

const drawImage = (ctx: CanvasRenderingContext2D, image: ImageBitmap, dx: number, dy: number, dw: number, dh: number): void => {
  ctx.beginPath();
  ctx.strokeStyle = '#FFF';
  ctx.lineWidth = 4;
  ctx.strokeRect(dx, dy, dw, dh);
  ctx.drawImage(image, dx, dy, dw, dh);
};
