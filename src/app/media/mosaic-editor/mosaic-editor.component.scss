@use "shared" as *;

:host {
  --cropper-overlay-color: #{$grey-4};
  --cropper-color: #{$primary-color};
  --cropper-resize-square-bg: #{$primary-color};
  display: flex;
  flex-direction: column;
  margin-bottom: 32px;
  gap: 12px;
}
.template {
  width: 140px;
  &-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  &-box {
    cursor: pointer;
    height: fit-content;
    flex-shrink: 0;
    position: relative;
  }
}
.current:after {
  content: "";
  background-color: $primary-color;
  opacity: 0.5;
  position: absolute;
  inset: 0;
}
.block-title {
  font-size: 14px;
}
.cropper {
  border-radius: 4px;
  max-height: 360px;
  &-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  &-box {
    width: 400px;
  }
}
.select {
  width: 100%;
  margin-bottom: 4px;
  font-size: 12px;
}
.empty {
  background-color: $grey-1;
  padding: 80px;
  border: 1px dashed $gray-light;
  font-size: 14px;
  font-weight: bold;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 12px;
  nz-icon {
    font-size: 24px;
  }
}
.canvas {
  height: 430px;
  width: 780px;
  margin-bottom: 32px;
  &-box {
    display: flex;
    justify-content: center;
    background-color: $grey-2;
    position: relative;
  }
  &-message {
    position: absolute;
    font-size: 18px;
    font-weight: bold;
    text-transform: uppercase;
    top: 45%;
    letter-spacing: .5px;
  }
}
nz-page-header {
  background-color: $grey-2;
}
