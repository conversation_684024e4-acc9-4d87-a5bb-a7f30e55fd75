import { UntypedFormGroup } from '@angular/forms';
import { IBaseContentConfig } from 'src/app/core/core.definitions';
import { DataTableComponent } from '../modules/data-table/data-table.component';
import { Observable } from 'rxjs';
import { TemplateRef } from '@angular/core';

export type DataTableEditorType = 'modal' | 'content-page-editor' | 'editor' | 'templates';

export enum DataTableEventType {
  CREATE = 'create',
  EDIT = 'edit',
  UPDATE = 'update',
  DELETE = 'delete',
  LIST_DELETED = 'delete',
  COPY = 'copy',
  COPY_URL = 'copyUrl',
  COPY_ARTICLE = 'copy.to_clipboard',
  COPY_ARTICLE_AND_SET_PRINT_STATUS = 'copy.article_and_set_print_status',
  COPY_AS_PRINT = 'copy.print',
  COPY_AS_ONLINE = 'copy.online',
  PREVIEW = 'preview',
  MODERATE = 'moderate',
  INACTIVATE = 'to_inactive',
  ACTIVATE = 'to_active',
  INACTIVE = 'inactivate',
  TO_ACTIVATE = 'to_activate',
  TO_INACTIVATE = 'to_inactivate',
  ACTIVE = 'activate',
  ACCEPT = 'accept',
  DECLINE = 'decline',
  RESTORE = 'restore',
  HIGHLIGHT = 'highlight',
  UNHIGHLIGHT = 'unhighlight',
  ADD_TO_HEADER_BAR = 'headerbar.add',
  REMOVE_TO_HEADER_BAR = 'headerbar.remove',
  TAGS_ON_HEADER_BAR = 'headerbar.list',
  EXPORT = 'print_export',
  SORT = 'sort',
  SHOW = 'show',
  CUSTOM_ACTION = 'row-custom',
  MOVE_UP = 'moveUp',
  MOVE_DOWN = 'moveDown',
  FORCE_UNLOCK = 'force_unlock',
  USER_PERMISSION = 'user_permission',
  USER_PERMISSION_WRONG_PORTAL = 'user_permission_wrong_portal',
  PUBLIC = 'public',
  NONPUBLIC = 'nonpublic',
  COMMENT_TO_LEADER = 'comment_to_leader',
  COMMENT_TO_FOLLOWER = 'comment_to_follower',
  COMMENT_TO_SHOW = 'comment_to_show',
  COMMENT_TO_HIDE = 'comment_to_hide',
  COMMENT_TO_APPROVED = 'comment_to_approved',
  COMMENT_TO_DENIED = 'comment_to_denied',
  FAST_USER_ASSIGMENT = 'user_assignment',
  USER_MERGE = 'merge',
  DOWNLOAD = 'download',
  TRANSACTION_POPUP = 'transaction-popup',
  DATA_VIEW = 'data-view',
  MUTE = 'mute',
  UNMUTE = 'unmute',
  WAITING = 'waiting',
  REVIEW_STATUS_ALLOW = 'reviewStatusAllow',
  REVIEW_STATUS_DENY = 'reviewStatusDeny',
  ADD_REVIEW = 'addReview',
  UPDATE_REVIEW = 'reviewUpdate',
  COVER_IMAGE_APPROVE = 'coverimage_approve',
  COVER_IMAGE_REJECT = 'coverimage_reject',
}

export enum CustomTemplateType {
  IS_ACTIVE = 'isActiveTemplate',
  DATE = 'dateTemplate',
  DATE_TIME = 'dateTimeTemplate',
  SLUG = 'slugTemplate',
  URL = 'urlTemplate',
  TAGS = 'thematicTagsTemplate',
  IMAGE = 'imageTemplate',
}

export enum DataTableInputType {
  BUTTON = 'button',
  SWITCH = 'switch',
}

export enum DataTableFilterType {
  TEXT = 'text',
  SELECT = 'select',
}

export type SortDirection = 'ascend' | 'descend' | null;
export type BackendSortDirection = 'asc' | 'desc';

export interface IDataTableComponentConfig {
  config: IBaseContentConfig;
  meta: IDataTableMeta;
}

export interface IDataTableSort {
  key: string;
  priority: number;
  direction: SortDirection;
}

export interface IDataTableMeta {
  meta: {};
  // limit: IDataTableMetaLimitInfo;
  dataCount?: number;
  limitable?: IDataTableMetaLimitableInfo;
  table?: IDataTableMetaTableInfo;
  globalActions?: Array<IDataTableActionInfo>;
  rowActions?: Array<IDataTableActionInfo>;
  message?: IDataTableMetaOriginalMessage;
  filterOptions?: {
    [name: string]: {
      [id: string]: string;
    };
  };
}

export interface IDataTableMetaLimitInfo {
  allRowCount: number;
  currentPage: number;
  fromRow: number;
  maxPage: number;
  rowsOnPage: number;
  toRow: number;
  visibleRowCount: number;
}

export interface IDataTableMetaLimitableInfo {
  pageCurrent: number;
  pageMax: number;
  fromRow: number;
  rowAllCount: number;
  rowFrom: number;
  rowOnPageCount: number;
}

export interface IDataTableMetaTableInfo {
  tableLayoutKey: string;
  contentType: string;
  columns: IDataTableColumnInfo[];
  globalActions: IDataTableActionInfo[];
  globalFilterKey: string;
  limitable: boolean;
  rowActions: IDataTableActionInfo[];
}

export interface IDataTableMetaOriginalMessage {
  category: {
    label: string;
    id: string;
  };
  date: string;
  moderateDate: string;
  moderateStatus: string;
  portalUser: {
    fullName: string;
    id: string;
  };
  text: string;
  id: string;
}

export interface IDataTableColumnInfo {
  key: string;
  property: string;
  title: string;
  customTemplateType?: CustomTemplateType;
  customTemplate?: any;
  filterable?: {
    queryKey: string;
    type: DataTableFilterType;
  };
  orderable?: {
    queryKey: string;
  };
  filters?: {
    filterable: boolean;
    filterOptions: Array<{
      text: string;
      value: any;
    }>;
  };
  sort?: {
    direction: SortDirection;
    priority: number;
  };
}

export interface IDataTableParams {
  filter: Array<{
    key: string;
    value: Array<string>;
  }>;
  pageIndex: number;
  page_limit: number;
  pageSize: number;
  sort: Array<{
    key: string;
    value: SortDirection;
  }>;
}

export interface IDataTableActionInfo {
  // granted: boolean;
  key: DataTableEventType;
  inputType?: DataTableInputType;
  label: string;
  onLabel?: string;
  newTabOption?: boolean;
  disabled?: boolean;
  copyOption?: boolean;
  isOption?: boolean;
  isPaired?: boolean;
  options?: DataTableEventType[];
  usesData?: boolean;
  queryParamKey?: string;
  queryParamValue?: string;
  cssClass?: string;

  buttonType?: 'primary' | 'default' | 'dashed' | 'link' | 'danger';
  actionType?: 'instant' | 'confirm';
  generateDynamicTitle?(data: any): string;
  nzCancelText?: string;
  nzOkText?: string;
  iconName?: string;
  customTemplate?: TemplateRef<any>;
  CustomTemplateType?: CustomTemplateType;
  eventHandler?(data: any): Observable<any>;
}

export interface IDataTableEventResult {
  eventType: DataTableEventType;
  success: boolean;
  errors?: string[];
  data?: any;
  successMessage?: string;
}

export interface IDataTableChangeThumbnailEvent {
  pageid?: string;
  contentType?: string;
  thumbnailid?: string;
}

export interface IDataTableEvent {
  dataTable: DataTableComponent;
  id: string;
}

export interface IDataTableSaveEvent extends IDataTableEvent {
  form: UntypedFormGroup;
}

export interface IDataTablePublishEvent extends IDataTableEvent {
  isActive: boolean;
}

export interface IDataTableAcceptDeclineEvent extends IDataTableEvent {
  type: 'message' | 'comment';
}

export interface IDataTableSortEvent extends IDataTableEvent {
  order: number;
}

export interface IDataTableGenerateDefaultEvent extends IDataTableEvent {
  languages: string[];
}

export interface IDataTablePaginationEvent extends IDataTableEvent {
  pageEvent: any;
}

export interface IDataTableSearchEvent extends IDataTableEvent {
  value: string;
}

export type BrandingBoxSearchParams = Partial<{
  global_filter: string;
  brand_filter: any;
  pubState: string;
}>;
