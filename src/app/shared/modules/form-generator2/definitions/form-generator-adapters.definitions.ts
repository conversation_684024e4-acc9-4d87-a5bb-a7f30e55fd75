import { ComponentFieldDataAsserts, ComponentFieldInputInfo, ContentInputType, FormControlImage, IFormInfoDataMeta } from 'src/app/core/api.definitons';
import { SelectSourceValueType, RadioGroupSourceValueType, CheckboxGroupSourceValueType } from '../../form-controls/definitions/form-controls.definitions';
import { FormControlListener } from './form-control-interaction.definitions';
import { DeepPartial } from 'src/app/shared/definitions/general.definitions';
import { SelectObjectItem } from '../../form-controls/components/selects/select.definitions';
import { List } from 'src/app/shared/definitions/shared.definitions';
import { GalleryImage } from '../../../../media-store/media-store.definitions';

export type FormControlErrorType = 'required' | 'minLength' | 'maxLength' | 'minValue' | 'maxValue' | 'pattern';

export type FormControlErrorBody = Readonly<{
  message: string;
}>;

export type FormControlError = Readonly<{
  [key in FormControlErrorType]: FormControlErrorBody;
}>;

export type GeneratedFormControl<T = any> =
  | TextFormControl<T>
  | NumberFormControl
  | SelectFormControl
  | CheckboxFormControl
  | ContributorsSelectFormControl
  | DatetimeFormControl
  | DatetimeRangeFormControl
  | RadioButtonFormControl
  | RadioGroupFormControl
  | CheckboxGroupFormControl
  | ImageFormControl
  | TableFormControl
  | OpeningHoursFormControl
  | AddressFormControl
  | SelectHighlightedImageFormControl;

export type GeneratedFormControlBase = Readonly<{
  key: string;
  label: string;
  inputType: ContentInputType;
  inputInfo: Partial<ComponentFieldInputInfo>;
  isDisabled: boolean;
  isHidden: boolean;
  asserts: ComponentFieldDataAsserts;
  errors: FormControlError;
  interactionListeners: FormControlListener[];
}>;

export type GeneratedFormControlWithValue<ValueType> = Readonly<{
  value: ValueType;
  initialValue: ValueType;
}>;

export type GeneratedFormControlWithSourceData = Readonly<{
  sourceData: {
    url: string;
    displayProperty: string;
    valueProperty: string;
  };
}>;

export type CounterInfo = Readonly<{
  wordCount: number;
  characterCount: number;
  rowCount: number;
  flekkCount?: number;
}>;

export type TextFormControl<T = string> = GeneratedFormControlBase & GeneratedFormControlWithValue<T> & CounterInfo;

export type PasswordFormControl = GeneratedFormControlBase & GeneratedFormControlWithValue<string>;

export type NumberFormControl = GeneratedFormControlBase & GeneratedFormControlWithValue<number>;

export type BackendTreeSelectSourceItem = SelectObjectItem &
  Readonly<{
    children: List<BackendTreeSelectSourceItem>;
  }>;

export type CheckboxFormControl = GeneratedFormControlBase & GeneratedFormControlWithValue<boolean>;

export type DatetimeFormControl = GeneratedFormControlBase & GeneratedFormControlWithValue<string>;

export type DatetimeRangeFormControl = GeneratedFormControlBase & GeneratedFormControlWithValue<{ lte: string; gte: string }>;

export type RadioButtonFormControl = GeneratedFormControlBase & GeneratedFormControlWithValue<boolean>;

export type RadioGroupFormControl = GeneratedFormControlBase & GeneratedFormControlWithValue<RadioGroupSourceValueType> & GeneratedFormControlWithSourceData;

export type CheckboxGroupFormControl = GeneratedFormControlBase &
  GeneratedFormControlWithValue<CheckboxGroupSourceValueType> &
  GeneratedFormControlWithSourceData &
  Readonly<{
    sourceData: {
      url: string;
      displayProperty: string;
      valueProperty: string;
      checked: string;
    };
  }>;

export type ImageFormControl = GeneratedFormControlBase &
  GeneratedFormControlWithValue<DeepPartial<FormControlImage>> &
  Readonly<{
    isAvatar: boolean;
    isStarDictionaryStar: boolean;
  }>;

export type Contributor = Readonly<{
  id: string;
  name: string;
  percentage: number;
  fullName?: string;
  isActive?: boolean;
}>;

export type ContributorUserSourceValueApi = Readonly<{
  email: string;
  firstName: string;
  fullName: string;
  id: string;
  isActive: string;
  isDeleted: string;
  lastName: string;
  publicAuthor: string;
  publicAuthorDescription: string;
  publicAuthorName: string;
  slug: string;
  title: string;
}>;

export type ContributorUserSourceValue = Readonly<{
  id: string;
  fullName?: string;
  name: string;
  percentage?: number;
}>;

export type SelectFormControl = GeneratedFormControlBase &
  GeneratedFormControlWithValue<SelectSourceValueType | SelectSourceValueType[]> &
  GeneratedFormControlWithSourceData &
  Readonly<{
    isMultiple: boolean;
    shouldTranslateOptionsLabels: boolean;
    createOptionUrl: string;
    allowClear: boolean;
    choices?: SelectSourceValueType[]; // If this is defined, the option of select is set based on this values
  }>;

export type ContributorsSelectFormControl = GeneratedFormControlBase &
  GeneratedFormControlWithValue<Contributor[]> &
  Readonly<{
    isMultiple: boolean;
    sourceData: {
      url: string;
      displayProperty: string;
      valueProperty: string;
    };
    createOptionUrl: string;
    contributorUsers: string;
    newContributorFeatureEnabled: string;
  }>;

export type ContributorsSelectFormControlApi = Readonly<{
  data: NamesFormControl[];
  meta: IFormInfoDataMeta &
    Readonly<{
      requestUrl: string;
    }>;
}>;

type NamesFormControl = GeneratedFormControlBase &
  GeneratedFormControlWithValue<string> &
  Readonly<{
    multiple: boolean;
  }>;

export type TableFormControl = GeneratedFormControlBase &
  GeneratedFormControlWithValue<
    {
      [key: string]: any;
    }[]
  >;

/**
 * Type which the TimePicker FormControl uses. It has a list of days with the corresponding opening and closing times.
 *
 */
export type OpeningHoursFormControl = GeneratedFormControlBase & GeneratedFormControlWithValue<OpeningHoursValue>;

/**
 * Value
 */
export type OpeningHoursValue = Readonly<{
  [day in OpeningHoursDaysOfWeek | string]?: OpeningHoursValueItem;
}>;

export type OpeningHoursValueItem = OpeningHoursValueDay[] | { closed: true } | { openAllDay: true };

/**
 * List of days a week consists of. This is used for listing the days in the TimePicker FormControl.
 */
export type OpeningHoursDaysOfWeek = 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';

/**
 * The opening and closing times for a given day in the TimePicker FormControl.
 * For example: friday: {open: '09:00', close: '18:00'}
 */
export type OpeningHoursValueDay = Readonly<{
  open: string;
  close: string;
}>;

/**
 * Type which the OtherTimesFormControl uses. It has a list of time description with the corresponding time.
 *
 */
export type OtherTimesFormControl = GeneratedFormControlBase & GeneratedFormControlWithValue<OtherTimesValue[]>;

export type OtherTimesValue = Readonly<{
  timeName: string;
  time: number;
}>;

/**
 * Type which the AddressFormControl uses. It has an object which contains the fields for the address value.
 */
export type AddressFormControl = GeneratedFormControlBase & GeneratedFormControlWithValue<AddressFormControlValue>;

/**
 * Type which the AddressFormControl uses. It has an object which contains the fields for the address value.
 */
export type SelectHighlightedImageFormControl = GeneratedFormControlBase &
  GeneratedFormControlWithSourceData &
  GeneratedFormControlWithValue<GalleryImage | null>;

/**
 * Address value for the AddressFormControl.
 */
export type AddressFormControlValue = Readonly<{
  country?: string;
  countryCode?: string;
  postalCode?: string;
  city?: string;
  street?: string;
  houseNumber?: string;
  comment?: string;
  placeId?: string;
  latitude?: string;
  longitude?: string;
}>;

export type ColorPickerFormControl = GeneratedFormControlBase & GeneratedFormControlWithValue<string>;
