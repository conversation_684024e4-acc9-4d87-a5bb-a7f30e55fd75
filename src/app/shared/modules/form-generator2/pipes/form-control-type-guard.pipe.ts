import { inject, Pipe, PipeTransform } from '@angular/core';
import { ContentInputType } from 'src/app/core/api.definitons';
import {
  CheckboxFormControl,
  ContributorsSelectFormControl,
  GeneratedFormControl,
  NumberFormControl,
  SelectFormControl,
  TextFormControl,
  DatetimeFormControl,
  RadioButtonFormControl,
  RadioGroupFormControl,
  CheckboxGroupFormControl,
  ImageFormControl,
  TableFormControl,
  AddressFormControl,
  OpeningHoursFormControl,
  OtherTimesFormControl,
  SelectHighlightedImageFormControl,
  PasswordFormControl,
  DatetimeRangeFormControl,
  ColorPickerFormControl,
} from '../definitions/form-generator-adapters.definitions';
import { ComponentFormControl, EadvertFormControl, GeneratedBodyFormControl, HtmlFormControl } from '../definitions/form-generator-body-adapters.definitions';
import { ComponentType } from '../definitions/form-generator.definitions';
import { FormControlAdapterService } from '../services/form-control-adapter.service';

function throwInvalidInputTypeError(inputType: ContentInputType | ComponentType, expectedInputTypes: ContentInputType[] | ComponentType[]) {
  throw new Error(`A megadott formControl típusa (${inputType})
    nem tartozik az elfogadott típusok közé: [${expectedInputTypes.join(', ')}]!`);
}

@Pipe({
  name: 'asTextFormControl',
  standalone: false,
})
export class TextFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedFormControl): TextFormControl {
    if (this.formControlAdapterService.isTextFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['text']);
  }
}

@Pipe({
  name: 'asPasswordFormControl',
  standalone: false,
})
export class PasswordFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedFormControl): PasswordFormControl {
    if (this.formControlAdapterService.isPasswordFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['password']);
  }
}

@Pipe({
  name: 'asNumberFormControl',
  standalone: false,
})
export class NumberFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedFormControl): NumberFormControl {
    if (this.formControlAdapterService.isNumberFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['number']);
  }
}

@Pipe({
  name: 'asSelectFormControl',
  standalone: false,
})
export class SelectFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedFormControl): SelectFormControl {
    if (this.formControlAdapterService.isSelectFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['select', 'searchSelect']);
  }
}

@Pipe({
  name: 'asContributorsSelectFormControl',
  standalone: false,
})
export class ContributorsSelectFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedFormControl): ContributorsSelectFormControl {
    if (this.formControlAdapterService.isContributorsSelectFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['contributorsSelect']);
  }
}

@Pipe({
  name: 'asCheckboxFormControl',
  standalone: false,
})
export class CheckboxFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedFormControl): CheckboxFormControl {
    if (this.formControlAdapterService.isCheckboxFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['checkbox']);
  }
}

@Pipe({
  name: 'asDatePickerFormControl',
  standalone: false,
})
export class DatePickerFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedFormControl): DatetimeFormControl {
    if (this.formControlAdapterService.isDatePickerFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['datePicker']);
  }
}

@Pipe({
  name: 'asDatetimeFormControl',
  standalone: false,
})
export class DatetimeFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedFormControl): DatetimeFormControl {
    if (this.formControlAdapterService.isDatetimeFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['datetime']);
  }
}

@Pipe({
  name: 'asDatetimeRangeFormControl',
  standalone: false,
})
export class DatetimeRangeFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedFormControl): DatetimeRangeFormControl {
    if (this.formControlAdapterService.isDatetimeRangeFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['datetimeRange']);
  }
}

@Pipe({
  name: 'asDateFormControl',
  standalone: false,
})
export class DateFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedFormControl): DatetimeFormControl {
    if (this.formControlAdapterService.isDateFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['date']);
  }
}

@Pipe({
  name: 'asRadioButtonFormControl',
  standalone: false,
})
export class RadioButtonFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedFormControl): RadioButtonFormControl {
    if (this.formControlAdapterService.isRadioButtonFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['radioButton']);
  }
}

@Pipe({
  name: 'asRadioGroupFormControl',
  standalone: false,
})
export class RadioGroupFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedFormControl): RadioGroupFormControl {
    if (this.formControlAdapterService.isRadioGroupFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['radioGroup']);
  }
}

@Pipe({
  name: 'asCheckboxGroupFormControl',
  standalone: false,
})
export class CheckboxGroupFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedFormControl): CheckboxGroupFormControl {
    if (this.formControlAdapterService.isCheckboxGroupFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['checkboxGroup']);
  }
}

@Pipe({
  name: 'asImageFormControl',
  standalone: false,
})
export class ImageFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedFormControl): ImageFormControl {
    if (this.formControlAdapterService.isImageFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['image']);
  }
}

@Pipe({
  name: 'asTableFormControl',
  standalone: false,
})
export class TableFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedFormControl): TableFormControl {
    if (this.formControlAdapterService.isTableFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['collection']);
  }
}

@Pipe({
  name: 'asOtherTimesControl',
  standalone: false,
})
export class OtherTimesFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedFormControl): OtherTimesFormControl {
    if (this.formControlAdapterService.isOtherTimesFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['collection']);
  }
}

@Pipe({
  name: 'asOpeningHoursFormControl',
  standalone: false,
})
export class OpeningHoursFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedFormControl): OpeningHoursFormControl {
    if (this.formControlAdapterService.isOpeningHoursFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['openingHours']);
  }
}

@Pipe({
  name: 'asAddressFormControl',
  standalone: false,
})
export class AddressFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedFormControl): AddressFormControl {
    if (this.formControlAdapterService.isAddressFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['address']);
  }
}

@Pipe({
  name: 'asColorPickerFormControl',
  standalone: false,
})
export class ColorPickerFormControlTypeGuardPipe implements PipeTransform {
  private readonly formControlAdapterService = inject(FormControlAdapterService);

  transform(formControl: GeneratedFormControl): ColorPickerFormControl {
    if (this.formControlAdapterService.isColorPickerFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['colorPicker']);
  }
}

@Pipe({
  name: 'asHtmlFormControl',
  standalone: false,
})
export class HtmlFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedBodyFormControl): HtmlFormControl {
    if (this.formControlAdapterService.isHtmlFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.componentType, ['Basic.Wysiwyg.Wysiwyg', 'Basic.Wysiwyg.WysiwygRequirement']);
  }
}

@Pipe({
  name: 'asEadvertFormControl',
  standalone: false,
})
export class EadvertFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedBodyFormControl): EadvertFormControl {
    if (this.formControlAdapterService.isEadvertFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.componentType, ['Eadvert.Eadvert']);
  }
}

@Pipe({
  name: 'asSelectHighlightedImage',
  standalone: false,
})
export class SelectHighlightedImageFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedFormControl): SelectHighlightedImageFormControl {
    if (this.formControlAdapterService.isSelectHighlightedImage(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.inputType, ['selectHighlightedImage']);
  }
}

@Pipe({
  name: 'asComponentFormControl',
  standalone: false,
})
export class ComponentFormControlTypeGuardPipe implements PipeTransform {
  constructor(private formControlAdapterService: FormControlAdapterService) {}

  transform(formControl: GeneratedBodyFormControl): ComponentFormControl {
    if (this.formControlAdapterService.isComponentFormControl(formControl)) {
      return formControl;
    }
    throwInvalidInputTypeError(formControl.componentType, [
      'Media.Video.Video',
      'Media.Gallery.Gallery',
      'Subsequent.Dossier.Dossier',
      'Voting.Voting',
      'ContentPage.Article',
      'ContentGroup.Quiz',
      'Game.GameBlock',
    ]);
  }
}
