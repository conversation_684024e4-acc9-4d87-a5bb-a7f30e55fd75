import { Injectable } from '@angular/core';
import { cloneDeep } from 'lodash-es';
import {
  AvailableComponentContent,
  BaseContentVariant,
  BodyComponentContent,
  ComponentContent,
  ContentResponseMeta,
  InputFieldContent,
} from 'src/app/core/api.definitons';
import { ContentData } from '../../form-generator/form-generator.definitions';
import {
  CheckboxFormControl,
  CheckboxGroupFormControl,
  ContributorsSelectFormControl,
  CounterInfo,
  DatetimeFormControl,
  DatetimeRangeFormControl,
  GeneratedFormControl,
  GeneratedFormControlBase,
  ImageFormControl,
  NumberFormControl,
  PasswordFormControl,
  RadioButtonFormControl,
  RadioGroupFormControl,
  SelectFormControl,
  SelectHighlightedImageFormControl,
  TableFormControl,
  TextFormControl,
} from '../definitions/form-generator-adapters.definitions';
import { v4 as uuidv4 } from 'uuid';
import { ComponentType } from '../definitions/form-generator.definitions';
import { GeneratedBodyFormControl } from '../definitions/form-generator-body-adapters.definitions';
import { getCharacterCount, getWordCount } from 'src/app/shared/utils/text-utils';

@Injectable({ providedIn: 'root' })
export class FormGeneratorMapperService {
  constructor() {}

  public convertContentDataToFormControls(contentData: ContentData): GeneratedFormControl[] {
    const data: BaseContentVariant[] = contentData?.data || [];
    const formControls: GeneratedFormControl[] = data
      .map((contentVariant: BaseContentVariant) => this.mapInputFieldContentToFormControl(contentVariant as InputFieldContent, contentData.meta))
      .filter((contentVariant) => !!contentVariant);

    return formControls;
  }

  public convertContentDataToBodyFormControls(contentData: ContentData): GeneratedBodyFormControl[] {
    const data: BaseContentVariant[] = contentData.data;
    return data
      .filter((contentVariant) => contentVariant.inputType === 'component')
      .map((contentVariant) => {
        const componentFormControls = this.mapContentDataComponentToFormControls(contentVariant as BodyComponentContent);
        return componentFormControls;
      })
      .flat();
  }

  public convertFormControlsToContentData(formControls: GeneratedFormControl[], originalContentData: ContentData): ContentData {
    const contentData = cloneDeep(originalContentData);
    const data = contentData.data.map((content) => {
      const formControl = formControls.find((fc) => fc.key === content.key);
      return {
        // should put the property that we want to change manually!
        ...content,
        value: formControl ? formControl.value : content.value,
        asserts: formControl?.asserts || content.asserts,
      };
    });

    contentData.data = data;
    return contentData;
  }

  public convertBodyFormControlsToContentData(
    formControls: GeneratedBodyFormControl[],
    originalContentData: ContentData,
    counterInfo: CounterInfo
  ): ContentData {
    const { wordCount, characterCount, rowCount } = counterInfo;
    const contentData = cloneDeep(originalContentData);
    const bodyComponent = <BodyComponentContent>contentData.data[0];
    const contentDataValue: ComponentContent[] = bodyComponent.value;
    const value: ComponentContent[] = [];

    formControls.forEach((formControl, index) => {
      const isNewComponent = formControl.isNew;
      if (isNewComponent) {
        const availableComponent = bodyComponent.availableComponents.find((ac) => ac.type === formControl.componentType);
        const newComponent = this.convertFormControlToNewComponent(formControl, availableComponent);
        value.push(newComponent);
      } else {
        const component = contentDataValue.find((content) => {
          const id = content.tempId || content.id;
          return id === formControl.componentId;
        });
        if (component) {
          const updatedComponent = this.convertFormControlToComponent(formControl, component);
          value[index] = updatedComponent;
        }
      }
    });

    const updatedContentData: ContentData = {
      data: [
        {
          ...contentData.data[0],
          value,
        },
      ],
      meta: {
        ...contentData.meta,
        wordCount,
        characterCount,
        rowCount,
      },
    };

    return updatedContentData;
  }

  public convertAvailableComponentToFormControl(
    availableComponent: AvailableComponentContent,
    index: number,
    customValue: any = null
  ): GeneratedBodyFormControl {
    const { details, type } = availableComponent;
    const { inputLabel, inputType, inputInfo, asserts, key, value } = details[0];
    const tempComponentId = uuidv4();

    return {
      key: `temp_${tempComponentId}_${key}`,
      componentId: `temp_${tempComponentId}`,
      componentType: type as ComponentType,
      label: inputLabel,
      inputType,
      inputInfo,
      isDisabled: false,
      asserts,
      errors: null,
      value: customValue || value,
      initialValue: null,
      wordCount: 0,
      characterCount: 0,
      markedToDelete: false,
      isNew: true,
      index,
      formControls: details.map((inputFieldContent) => this.mapInputFieldContentToFormControl(inputFieldContent)),
    };
  }

  public convertFormControlToNewComponent(formControl: GeneratedBodyFormControl, availableComponent: AvailableComponentContent): ComponentContent {
    return {
      ...availableComponent,
      tempId: formControl?.componentId,
      details: [
        {
          ...availableComponent.details[0],
          value: formControl?.value,
        },
      ],
    };
  }

  public convertFormControlToComponent(formControl: GeneratedBodyFormControl, component: ComponentContent): ComponentContent {
    return {
      ...component,
      ...(formControl.markedToDelete ? { deleted: formControl.markedToDelete } : null),
      details: [
        {
          ...component.details[0],
          value: formControl ? formControl.value : component.details[0].value,
        },
      ],
    };
  }

  public mapInputFieldContentToFormControl(inputFieldContent: InputFieldContent, meta?: ContentResponseMeta): GeneratedFormControl {
    const { inputType, inputInfo } = inputFieldContent;

    switch (inputType) {
      case 'text':
      case 'textarea':
        return this.mapContentDataInputToTextFormControl(inputFieldContent);
      case 'password':
        return this.mapContentDataInputToPasswordFormControl(inputFieldContent);
      case 'number':
        return this.mapContentDataInputToNumberFormControl(inputFieldContent);
      case 'select':
      case 'searchSelect': {
        if (inputInfo.typeSelect) {
          return this.mapContentDataInputToContributorsSelectFormControl(inputFieldContent, meta);
        }
        return this.mapContentDataInputToSelectFormControl(inputFieldContent);
      }
      case 'checkbox':
        return this.mapContentDataInputToCheckboxFormControl(inputFieldContent);
      case 'datetime':
      case 'datePicker':
      case 'date':
        return this.mapContentDataInputToDatetimeFormControl(inputFieldContent);
      case 'datetimeRange':
        return this.mapContentDataInputToDatetimeRangeFormControl(inputFieldContent);
      case 'radioButton':
        return this.mapContentDataInputToRadioButtonFormControl(inputFieldContent);
      case 'radioGroup':
        return this.mapContentDataInputToRadioGroupFormControl(inputFieldContent);
      case 'checkboxGroup':
        return this.mapContentDataInputToCheckboxGroupFormControl(inputFieldContent);
      case 'image':
        return this.mapContentDataInputToImageFormControl(inputFieldContent);
      case 'collection':
        return this.mapContentDataInputToTableFormControl(inputFieldContent);
      case 'eadvert':
        return this.mapContentDataInputToEadvertFormcontrol(inputFieldContent);
      case 'paywall':
        return this.mapContentDataInputToPaywallFormcontrol(inputFieldContent);
      case 'openingHours':
        return this.mapContentDataInputToOpeningHoursFormControl(inputFieldContent);
      case 'openingHoursSeasonal':
        return this.mapContentDataInputToOpeningHoursFormControl(inputFieldContent);
      case 'address':
        return this.mapContentDataInputToAddressFormControl(inputFieldContent);
      case 'otherTimes':
        return this.mapContentDataInputToOtherTimesFormControl(inputFieldContent);
      case 'selectHighlightedImage':
        return this.mapContentDataInputToSelectHighlightedImageFormControl(inputFieldContent);
      case 'graphdata':
        return this.mapContentDataInputToGraphDataFormcontrol(inputFieldContent);
      case 'colorPicker':
        return this.mapContentDataInputToColorPickerFromControl(inputFieldContent);

      /*case 'radio': throw new Error('Not implemented');
      case 'map': throw new Error('Not implemented');*/
      default:
        return null;
    }
  }

  private mapContentDataInputToBaseFormControl(inputField: InputFieldContent): GeneratedFormControlBase {
    const { key, inputLabel, inputInfo, inputType, readOnly, asserts } = inputField;

    const formControlBase: GeneratedFormControlBase = {
      key,
      label: inputLabel,
      inputType,
      inputInfo,
      isDisabled: readOnly,
      isHidden: false,
      asserts,
      errors: null,
      interactionListeners: [],
    };
    return formControlBase;
  }

  private mapContentDataInputToTextFormControl(inputField: InputFieldContent): TextFormControl {
    const { value } = inputField;

    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      value,
      initialValue: value,
      wordCount: 0,
      characterCount: 0,
      rowCount: 0,
    };
  }

  private mapContentDataInputToPasswordFormControl(inputField: InputFieldContent): PasswordFormControl {
    const { value } = inputField;

    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      value,
      initialValue: value,
    };
  }

  private mapContentDataInputToNumberFormControl(inputField: InputFieldContent): NumberFormControl {
    const { value } = inputField;

    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      value,
      initialValue: value,
    };
  }

  private mapContentDataInputToCheckboxFormControl(inputField: InputFieldContent): CheckboxFormControl {
    const { value } = inputField;

    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      value,
      initialValue: value,
    };
  }

  private mapContentDataInputToDatetimeFormControl(inputField: InputFieldContent): DatetimeFormControl {
    const { value } = inputField;

    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      value,
      initialValue: value,
    };
  }

  private mapContentDataInputToDatetimeRangeFormControl(inputField: InputFieldContent): DatetimeRangeFormControl {
    const { value } = inputField;

    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      value,
      initialValue: value,
    };
  }

  private mapContentDataInputToRadioButtonFormControl(inputField: InputFieldContent): RadioButtonFormControl {
    const { value } = inputField;

    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      value,
      initialValue: value,
    };
  }

  private mapContentDataInputToRadioGroupFormControl(inputField: InputFieldContent): RadioGroupFormControl {
    const { value, referenceSource, multiple } = inputField;
    const { url, selectDisplayProperty, selectKeyProperty } = referenceSource || {};

    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      value,
      initialValue: value,
      sourceData: {
        url,
        displayProperty: selectDisplayProperty,
        valueProperty: selectKeyProperty,
      },
    };
  }

  private mapContentDataInputToCheckboxGroupFormControl(inputField: InputFieldContent): CheckboxGroupFormControl {
    const { value, referenceSource } = inputField;
    const { url, selectDisplayProperty, selectKeyProperty } = referenceSource || {};

    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      value,
      initialValue: value,
      sourceData: {
        url,
        displayProperty: selectDisplayProperty,
        valueProperty: selectKeyProperty,
        checked: 'checked',
      },
    };
  }

  private mapContentDataInputToImageFormControl(inputField: InputFieldContent): ImageFormControl {
    const {
      value,
      inputInfo: { isAvatar, isStarDictionaryStar },
    } = inputField;

    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      value,
      initialValue: value,
      isAvatar,
      isStarDictionaryStar,
    };
  }

  private mapContentDataInputToSelectFormControl(inputField: InputFieldContent): SelectFormControl {
    const {
      value,
      referenceSource,
      multiple,
      choices,
      inputInfo: { allowClear, translateSource, createUrl, treeSelect, _tempNewSourceUrl },
    } = inputField;
    const { url, selectDisplayProperty, selectKeyProperty } = referenceSource || {};

    const sourceUrl = !treeSelect ? url : _tempNewSourceUrl;

    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      inputType: 'select',
      value,
      initialValue: value,
      isMultiple: multiple,
      shouldTranslateOptionsLabels: translateSource,
      allowClear: allowClear !== false,
      createOptionUrl: createUrl,
      sourceData: {
        url: sourceUrl,
        displayProperty: selectDisplayProperty,
        valueProperty: selectKeyProperty,
      },
      choices,
    };
  }

  private mapContentDataInputToContributorsSelectFormControl(inputField: InputFieldContent, meta?: ContentResponseMeta): ContributorsSelectFormControl {
    const {
      value,
      referenceSource,
      multiple,
      inputInfo: { translateSource, createUrl },
    } = inputField;
    const { url, selectDisplayProperty, selectKeyProperty } = referenceSource || {};

    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      inputType: 'contributorsSelect',
      value,
      initialValue: value,
      isMultiple: multiple,
      createOptionUrl: createUrl,
      sourceData: {
        url,
        displayProperty: selectDisplayProperty,
        valueProperty: selectKeyProperty,
      },
      contributorUsers: meta.contributorUsers,
      newContributorFeatureEnabled: meta.newContributorFeatureEnabled,
    };
  }

  private mapContentDataInputToTableFormControl(inputField: InputFieldContent): TableFormControl {
    const { value } = inputField;

    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      value,
      initialValue: value,
    };
  }

  private mapContentDataInputToGraphDataFormcontrol(inputField: InputFieldContent) {
    const { value } = inputField;

    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      value,
      initialValue: value,
    };
  }

  private mapContentDataInputToColorPickerFromControl(inputField: InputFieldContent) {
    const { value } = inputField;

    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      value,
      initialValue: value,
    };
  }

  private mapContentDataInputToEadvertFormcontrol(inputField: InputFieldContent) {
    const { value } = inputField;

    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      value,
      initialValue: value,
    };
  }

  private mapContentDataInputToPaywallFormcontrol(inputField: InputFieldContent) {
    const { value } = inputField;

    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      value,
      initialValue: value,
    };
  }

  private mapContentDataInputToOpeningHoursFormControl(inputField: InputFieldContent): GeneratedFormControl {
    const { value } = inputField;
    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      value,
      initialValue: value,
    };
  }

  private mapContentDataInputToOtherTimesFormControl(inputField: InputFieldContent): GeneratedFormControl {
    const { value } = inputField;
    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      value,
      initialValue: value,
    };
  }

  private mapContentDataInputToAddressFormControl(inputField: InputFieldContent): GeneratedFormControl {
    const { value } = inputField;
    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      value,
      initialValue: value,
    };
  }

  private mapContentDataInputToSelectHighlightedImageFormControl(inputField: InputFieldContent): SelectHighlightedImageFormControl {
    const { value, referenceSource } = inputField;
    const { url, selectDisplayProperty, selectKeyProperty } = referenceSource || {};

    return {
      ...this.mapContentDataInputToBaseFormControl(inputField),
      inputType: 'selectHighlightedImage',
      initialValue: value,
      value,
      sourceData: {
        url,
        displayProperty: selectDisplayProperty,
        valueProperty: selectKeyProperty,
      },
    };
  }

  private mapContentDataComponentToFormControls(bodyComponent: BodyComponentContent): GeneratedBodyFormControl[] {
    const components = bodyComponent.value;

    return components.map((component, index) => {
      const { details, id, tempId, type, deleted } = component;
      const inputField = details[0];
      const config = bodyComponent.availableComponents.find((ac) => ac.type === component.type);
      const inputFieldConfig = config?.details[0];
      const { value, readOnly } = inputField || {};
      const { inputLabel, inputType, inputInfo, asserts } = inputFieldConfig || {};
      const componentId = tempId || id;
      const mergedDetails = config.details.map((configDetails, i) => ({
        ...configDetails,
        ...(details[i] || []),
      }));

      if (!componentId) {
        return this.convertAvailableComponentToFormControl(config, index, value);
      }

      let wordCount: number = 0;
      let characterCount: number = 0;
      if (type === 'Basic.Wysiwyg.Wysiwyg' || type === 'Basic.Wysiwyg.WysiwygRequirement') {
        wordCount = getWordCount(value);
        characterCount = getCharacterCount(value);
      }

      return {
        key: componentId,
        componentId: componentId,
        componentType: type as ComponentType,
        label: inputLabel,
        inputType,
        inputInfo,
        isDisabled: readOnly,
        asserts,
        errors: null,
        value,
        initialValue: value,
        wordCount,
        characterCount,
        markedToDelete: deleted,
        isNew: false,
        index,
        formControls: mergedDetails.map((inputFieldContent) => this.mapInputFieldContentToFormControl(inputFieldContent)),
      };
    });
  }
}
