import { Injectable } from '@angular/core';
import { IHttpOptions, ReqService } from '@external/http';
import { StorageService } from '@external/utils';
import { Observable } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import {
  CheckboxFormControl,
  ContributorsSelectFormControl,
  GeneratedFormControl,
  NumberFormControl,
  SelectFormControl,
  TextFormControl,
  DatetimeFormControl,
  RadioButtonFormControl,
  RadioGroupFormControl,
  CheckboxGroupFormControl,
  ImageFormControl,
  ContributorsSelectFormControlApi,
  TableFormControl,
  AddressFormControl,
  OpeningHoursFormControl,
  OtherTimesFormControl,
  SelectHighlightedImageFormControl,
  PasswordFormControl,
  DatetimeRangeFormControl,
  ColorPickerFormControl,
} from '../definitions/form-generator-adapters.definitions';
import {
  ArticleComponentFormControl,
  ComponentFormControl,
  DossierComponentFormControl,
  EadvertFormControl,
  GalleryComponentFormControl,
  GeneratedBodyFormControl,
  HtmlFormControl,
  PaywallFormControl,
  QuizComponentFormControl,
  VideoComponentFormControl,
  VotingComponentFormControl,
} from '../definitions/form-generator-body-adapters.definitions';
import { SelectSourceValueType } from '../../form-controls/definitions/form-controls.definitions';
import { UtilsService } from '@external/utils';
import { Contributor } from 'src/app/core/definitions/article.definitions';

@Injectable({ providedIn: 'root' })
export class FormControlAdapterService {
  constructor(
    private readonly reqService: ReqService,
    private readonly storageService: StorageService,
    private readonly utilsService: UtilsService
  ) {}

  public isTextFormControl(formControl: GeneratedFormControl): formControl is TextFormControl {
    return formControl.inputType === 'text' || formControl.inputType === 'textarea';
  }

  public isPasswordFormControl(formControl: GeneratedFormControl): formControl is PasswordFormControl {
    return formControl.inputType === 'password';
  }

  public isNumberFormControl(formControl: GeneratedFormControl): formControl is NumberFormControl {
    return formControl.inputType === 'number';
  }

  public isSelectFormControl(formControl: GeneratedFormControl): formControl is SelectFormControl {
    return formControl.inputType === 'select' || formControl.inputType === 'searchSelect';
  }

  public isContributorsSelectFormControl(formControl: GeneratedFormControl): formControl is ContributorsSelectFormControl {
    return formControl.inputType === 'contributorsSelect';
  }

  public isCheckboxFormControl(formControl: GeneratedFormControl): formControl is CheckboxFormControl {
    return formControl.inputType === 'checkbox';
  }
  public isDatePickerFormControl(formControl: GeneratedFormControl): formControl is DatetimeFormControl {
    return formControl.inputType === 'datePicker';
  }
  public isDatetimeFormControl(formControl: GeneratedFormControl): formControl is DatetimeFormControl {
    return formControl.inputType === 'datetime';
  }
  public isDatetimeRangeFormControl(formControl: GeneratedFormControl): formControl is DatetimeRangeFormControl {
    return formControl.inputType === 'datetimeRange';
  }

  public isDateFormControl(formControl: GeneratedFormControl): formControl is DatetimeFormControl {
    return formControl.inputType === 'date';
  }

  public isRadioButtonFormControl(formControl: GeneratedFormControl): formControl is RadioButtonFormControl {
    return formControl.inputType === 'radioButton';
  }

  public isRadioGroupFormControl(formControl: GeneratedFormControl): formControl is RadioGroupFormControl {
    return formControl.inputType === 'radioGroup';
  }

  public isCheckboxGroupFormControl(formControl: GeneratedFormControl): formControl is CheckboxGroupFormControl {
    return formControl.inputType === 'checkboxGroup';
  }

  public isImageFormControl(formControl: GeneratedFormControl): formControl is ImageFormControl {
    return formControl.inputType === 'image';
  }

  public isOpeningHoursFormControl(formControl: GeneratedFormControl): formControl is OpeningHoursFormControl {
    return formControl.inputType === 'openingHours' || formControl.inputType === 'openingHoursSeasonal';
  }

  public isAddressFormControl(formControl: GeneratedFormControl): formControl is AddressFormControl {
    return formControl.inputType === 'address';
  }

  public isSelectHighlightedImage(formControl: GeneratedFormControl): formControl is SelectHighlightedImageFormControl {
    return formControl.inputType === 'selectHighlightedImage';
  }

  public isTableFormControl(formControl: GeneratedFormControl): formControl is TableFormControl {
    return formControl.inputType === 'collection';
  }

  public isOtherTimesFormControl(formControl: GeneratedFormControl): formControl is OtherTimesFormControl {
    return formControl.inputType === 'otherTimes';
  }

  public isColorPickerFormControl(formControl: GeneratedFormControl): formControl is ColorPickerFormControl {
    return formControl.inputType === 'colorPicker';
  }

  public isHtmlFormControl(formControl: GeneratedBodyFormControl): formControl is HtmlFormControl {
    return formControl.componentType === 'Basic.Wysiwyg.Wysiwyg' || formControl.componentType === 'Basic.Wysiwyg.WysiwygRequirement';
  }

  public isPaywallFormControl(formControl: GeneratedBodyFormControl): formControl is PaywallFormControl {
    return formControl.componentType === 'Paywall.Paywall';
  }

  public isEadvertFormControl(formControl: GeneratedBodyFormControl): formControl is EadvertFormControl {
    return formControl.componentType === 'Eadvert.Eadvert';
  }

  public isQuizComponentFormcontrol(formControl: GeneratedBodyFormControl): formControl is QuizComponentFormControl {
    return formControl.componentType === 'ContentGroup.Quiz';
  }

  public isGameComponentFormcontrol(formControl: GeneratedBodyFormControl): formControl is QuizComponentFormControl {
    return formControl.componentType === 'Game.GameBlock';
  }

  public isVideoComponentFormControl(formControl: GeneratedBodyFormControl): formControl is VideoComponentFormControl {
    return formControl.componentType === 'Media.Video.Video';
  }

  public isDossierComponentFormControl(formControl: GeneratedBodyFormControl): formControl is DossierComponentFormControl {
    return formControl.componentType === 'Subsequent.Dossier.Dossier';
  }

  public isGalleryComponentFormControl(formControl: GeneratedBodyFormControl): formControl is GalleryComponentFormControl {
    return formControl.componentType === 'Media.Gallery.Gallery';
  }

  public isVotingComponentFormControl(formControl: GeneratedBodyFormControl): formControl is VotingComponentFormControl {
    return formControl.componentType === 'Voting.Voting';
  }

  public isArticleComponentFormControl(formControl: GeneratedBodyFormControl): formControl is ArticleComponentFormControl {
    return formControl.componentType === 'ContentPage.Article';
  }

  public isComponentFormControl(formControl: GeneratedBodyFormControl): formControl is ComponentFormControl {
    return (
      this.isVideoComponentFormControl(formControl) ||
      this.isDossierComponentFormControl(formControl) ||
      this.isGalleryComponentFormControl(formControl) ||
      this.isVotingComponentFormControl(formControl) ||
      this.isArticleComponentFormControl(formControl) ||
      this.isEadvertFormControl(formControl) ||
      this.isQuizComponentFormcontrol(formControl) ||
      this.isGameComponentFormcontrol(formControl) ||
      this.isPaywallFormControl(formControl)
    );
  }

  public fetchSource<T>(resourceUrl: string, params: IHttpOptions['params'] = null): Observable<T[]> {
    return this.reqService.get(resourceUrl, { params }).pipe(map((sourceResponse) => sourceResponse.data));
  }

  public createSourceOption(url: string, value: string, type: 'select'): Observable<SelectSourceValueType>;
  public createSourceOption(url: string, value: string, type: 'contributor'): Observable<Contributor>;
  public createSourceOption(url: string, value: string, type: 'select' | 'contributor'): Observable<SelectSourceValueType | Contributor> {
    /* !!! This is now specialized for tags, if we want more create selects, a protocol for generalization is needed. */
    if (type === 'contributor') {
      const names: [string, string] = this.nameFromFullName(value);
      if (!names) {
        return null;
      }

      return this.reqService.get(url).pipe(
        map((result: ContributorsSelectFormControlApi) => this.setContributorInputFieldValues(names, result)),
        switchMap((result: ContributorsSelectFormControlApi) => this.reqService.post(url, result)),
        map((result: ContributorsSelectFormControlApi) => this.mapContributorResponse(result))
      );
    }

    return this.reqService.get(url).pipe(
      map((result) => this.setTagInputFieldValues(value, result)),
      switchMap((result) => this.reqService.post(url, result)),
      map((result) => {
        const createdOption = {};

        for (const input of result.data) {
          createdOption[input.key] = input.value;
        }

        createdOption['id'] = result.meta.id;

        return createdOption;
      })
    );
  }

  public setOpenEditModalOnInitForComponentFormControls(formControl: GeneratedBodyFormControl, index: number) {
    if (this.isComponentFormControl(formControl)) {
      this.storageService.setSessionStorageData(`shouldOpenEditModalOnInit_${index}`, '1');
    }
  }

  private setTagInputFieldValues(value: string, result) {
    const titleInput = result.data.find((input) => input.key === 'title');
    titleInput.value = value;

    const slugInput = result.data.find((input) => input.key === 'slug');
    slugInput.value = this.utilsService.generateSlug(value);

    return result;
  }

  private setContributorInputFieldValues(names: [string, string], result: ContributorsSelectFormControlApi): ContributorsSelectFormControlApi {
    const { value: firstNameValue, ...firstNameElements } = result.data.find((input) => input.key === 'firstName');
    const { value: lastNameValue, ...lastNameElements } = result.data.find((input) => input.key === 'lastName');

    return {
      data: [
        { ...firstNameElements, value: names[0] },
        { ...lastNameElements, value: names[1] },
      ],
      meta: result.meta,
    };
  }

  public nameFromFullName(fullName: string, language = 'hun'): [string, string] {
    if (!fullName || fullName.split(' ').length < 2) {
      return null;
    }
    const firstName: string = language === 'hun' ? fullName.split(' ').slice(1).join(' ') : fullName.split(' ').slice(0, -1).join(' ');
    const lastName: string = language === 'hun' ? fullName.split(' ').slice(0, 1).join(' ') : fullName.split(' ').slice(-1).join(' ');

    return [firstName, lastName];
  }

  public mapContributorResponse(result: ContributorsSelectFormControlApi): Contributor {
    const name = [];
    for (const input of result.data) {
      if (input.key === 'firstName') {
        name[0] = input.value;
      }
      if (input.key === 'lastName') {
        name[1] = input.value;
      }
    }
    return {
      name: name.join(' '),
      fullName: name.join(' '),
      id: result.meta.id,
      percentage: 1,
    };
  }

  public isComponentSavedInBackend(componentId: string): boolean {
    return !componentId.startsWith('temp_');
  }
}
