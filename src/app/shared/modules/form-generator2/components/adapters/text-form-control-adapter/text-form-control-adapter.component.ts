import { ChangeDetectionStrategy, Component, EventEmitter, HostBinding, Input, Output } from '@angular/core';
import { TextFormControl } from '../../../definitions/form-generator-adapters.definitions';
import { FormControlValidatorService } from '../../../services/form-control-validator.service';
import { UtilsService } from '@external/utils';
import { PortalConfigSetting } from '@shared/definitions/portal-config';
import { PortalConfigService } from '@shared/services/portal-config.service';

const ENTER_FORBIDDEN_INPUT_KEYS = [
  'editedVersion.dataSecondary.printSubTitle',
  'editedVersion.dataSecondary.printTitle',
  'editedVersion.dataSecondary.lead',
  'slug',
];

@Component({
  selector: 'app-text-form-control-adapter',
  templateUrl: 'text-form-control-adapter.component.html',
  styleUrls: ['../form-control-adapter.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class TextFormControlAdapterComponent {
  @Input() public set formControl(value: TextFormControl) {
    this.setProperties(value);
  }

  @Output() valueChange: EventEmitter<TextFormControl> = new EventEmitter<TextFormControl>();
  @Output() inputChange: EventEmitter<TextFormControl> = new EventEmitter<TextFormControl>();

  public label: string;
  public value: string;
  public maxLength: number;
  public softLimit: number;
  public isRequired: boolean;
  public isDisabled: boolean;
  @HostBinding('class.hidden')
  public isHidden: boolean;
  public errorMessage: string;
  public forbiddenKeyboardKeys: string[] = [];
  public showTitleGenerator: boolean = false;

  private _formControl: TextFormControl;

  constructor(
    private readonly formControlValidatorService: FormControlValidatorService,
    private readonly utilsService: UtilsService,
    private readonly portalConfigService: PortalConfigService
  ) {}

  protected setProperties(formControl: TextFormControl) {
    const { value, label, asserts, inputInfo, isDisabled, errors, isHidden, key } = formControl || {};

    this._formControl = formControl;
    this.label = label;
    this.value = value || '';
    this.maxLength = asserts?.Length?.max;
    this.softLimit = inputInfo?.softLimit;
    this.isRequired = !!asserts?.NotBlank || (inputInfo?.required ? inputInfo?.required : false) || asserts?.Length?.min > 0;
    this.isDisabled = isDisabled;
    this.isHidden = isHidden;
    this.errorMessage = errors && Object.values(errors).find((error) => !!error)?.message;

    if (ENTER_FORBIDDEN_INPUT_KEYS.includes(key)) {
      this.forbiddenKeyboardKeys = ['Enter'];
    }

    this.showTitleGenerator =
      this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_GENERATE_TITLE_TO_ARTICLE_WITH_AI) && this.label === 'editedVersion.dataPrimary.title';
  }

  onValueChange(value: string) {
    if (this._formControl.label === 'slug') {
      value = this.utilsService.generateSlug(value);
    }
    const errors = this.formControlValidatorService.validate<string>(value, this._formControl.asserts);
    const newFormControl: TextFormControl = {
      ...this._formControl,
      value,
      errors,
    };
    this.valueChange.emit(newFormControl);
  }

  onInputChange(value: string) {
    const newFormControl: TextFormControl = {
      ...this._formControl,
      value,
    };
    this.inputChange.emit(newFormControl);
  }
}
