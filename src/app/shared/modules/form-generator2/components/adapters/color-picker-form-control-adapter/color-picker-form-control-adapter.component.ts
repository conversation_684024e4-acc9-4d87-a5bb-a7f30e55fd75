import { ChangeDetectionStrategy, Component, computed, inject, input, output } from '@angular/core';
import { ColorPickerFormControl } from '@shared/modules/form-generator2/definitions/form-generator-adapters.definitions';
import { FormControlValidatorService } from '@shared/modules/form-generator2/services/form-control-validator.service';

@Component({
  selector: 'app-color-picker-form-control-adapter',
  templateUrl: './color-picker-form-control-adapter.component.html',
  styleUrl: './color-picker-form-control-adapter.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
  host: {
    '[class.hidden]': '!!this.formControl().isHidden',
  },
})
export class ColorPickerFormControlAdapterComponent {
  private readonly formControlValidatorService = inject(FormControlValidatorService);

  readonly formControl = input.required<ColorPickerFormControl>();

  readonly valueChange = output<ColorPickerFormControl>();

  readonly isRequired = computed(() => !!this.formControl().asserts?.NotBlank || this.formControl().inputInfo?.required);
  readonly errorMessage = computed(() => this.formControl().errors && Object.values(this.formControl().errors).find((error) => !!error)?.message);

  onValueChange(value: string): void {
    this.valueChange.emit({
      ...this.formControl(),
      value,
      errors: this.formControlValidatorService.validate(value, this.formControl().asserts),
    });
  }
}
