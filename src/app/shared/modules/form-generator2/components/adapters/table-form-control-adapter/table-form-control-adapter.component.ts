import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ReqService } from '@external/http';
import { TableFormControlData } from 'src/app/shared/modules/form-controls/definitions/table-form-control.definitions';
import { ContentData } from 'src/app/shared/modules/form-generator/form-generator.definitions';
import { TableFormControl } from '../../../definitions/form-generator-adapters.definitions';

@Component({
  selector: 'app-table-form-control-adapter',
  templateUrl: 'table-form-control-adapter.component.html',
  styleUrls: ['../form-control-adapter.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class TableFormControlAdapterComponent implements OnInit {
  @Input() public set formControl(value: TableFormControl) {
    this.setProperties(value);
  }

  @Output() valueChange: EventEmitter<TableFormControl> = new EventEmitter<TableFormControl>();

  public label: string;
  public isDisabled: boolean;
  public isRequired: boolean;
  public addNewButtonTitle: string;
  public tableData: TableFormControlData;
  public formContentData: ContentData;
  public isHidden: boolean;
  private _formControl: TableFormControl;

  constructor(
    private readonly reqService: ReqService,
    private readonly cd: ChangeDetectorRef
  ) {}

  ngOnInit() {}

  private setProperties(formControl: TableFormControl) {
    const {
      value,
      label,
      asserts,
      isDisabled,
      inputInfo: { formUrl },
    } = formControl;
    this._formControl = formControl;
    this.label = label;
    this.isDisabled = isDisabled;
    this.isRequired = !!asserts?.NotBlank;
    this.tableData = this.mapValueToTableData(value);
    this.addNewButtonTitle = this.getAddNewButtonTitle(label);
    this.isHidden = formControl?.inputInfo?.isHidden ?? false;

    this.fetchFormData(formUrl);
  }

  onCreateRow(tableData: TableFormControlData) {
    this.emitValueChange(tableData);
  }

  onEditRow(tableData: TableFormControlData) {
    this.emitValueChange(tableData);
  }

  onDeleteRow(tableData: TableFormControlData) {
    this.emitValueChange(tableData);
  }

  private emitValueChange(tableData: TableFormControlData) {
    const value = this.mapTableDataToValue(tableData);

    const newFormControl: TableFormControl = {
      ...this._formControl,
      value,
    };

    this.valueChange.emit(newFormControl);
  }

  private fetchFormData(url: string) {
    this.reqService.get<ContentData>(url).subscribe((contentData) => {
      this.formContentData = contentData;
      this.cd.detectChanges();
    });
  }

  private mapValueToTableData(value: TableFormControl['value']): TableFormControlData {
    const tableData: TableFormControlData = {
      headers: Object.keys(value[0] || {}),
      rows: value.map((row) => {
        return Object.keys(row).reduce((obj, headerName) => {
          return {
            ...obj,
            [headerName]: {
              value: row[headerName],
            },
          };
        }, {});
      }),
    };

    return tableData;
  }

  private mapTableDataToValue(tableData: TableFormControlData): TableFormControl['value'] {
    const value: TableFormControl['value'] = tableData.rows.map((row) => {
      return Object.keys(row).reduce(
        (obj, key) => ({
          ...obj,
          [key]: row[key].value,
        }),
        {}
      );
    });

    return value;
  }

  private getAddNewButtonTitle(label: string): string {
    switch (label) {
      case 'answers':
        return 'Válasz hozzáadása';
      case 'CMS.weeklyMenuItems':
        return 'Napi menü hozzáadása';
      default:
        return 'Hozzáadás';
    }
  }
}
