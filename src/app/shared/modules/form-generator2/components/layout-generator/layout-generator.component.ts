import { AfterViewInit, ChangeDetectionStrategy, Component, ElementRef, Input, QueryList, Renderer2, TemplateRef, ViewChildren } from '@angular/core';
import { format } from 'date-fns';
import { takeUntil } from 'rxjs/operators';
import { FormControlImage } from 'src/app/core/api.definitons';
import { DestroyService } from 'src/app/shared/services/destroy.service';
import { FormLayout } from '../../../form-generator/form-generator.definitions';
import { ImageFormControl } from '../../definitions/form-generator-adapters.definitions';
import { FormGenerator2Service } from '../../services/form-generator.service';
import { Image } from '@media/media-image/definitions';

const NON_HIDDEN_CONTENT_QUERY = '[data-layout-content] > *:not(.hidden)';

@Component({
  selector: 'app-layout-generator',
  templateUrl: 'layout-generator.component.html',
  styleUrls: ['./layout-generator.component.scss'],
  providers: [DestroyService],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class LayoutGeneratorComponent implements AfterViewInit {
  @ViewChildren('formLayoutColumn') private formLayoutColumn: QueryList<ElementRef<HTMLDivElement>>;

  @Input() layout: FormLayout;
  @Input() formControlAdaptersTemplate: TemplateRef<any>;

  private notInLayoutKeys: string[] = [];

  constructor(
    private formGeneratorService: FormGenerator2Service,
    private destroy$: DestroyService,
    private renderer: Renderer2
  ) {}

  ngAfterViewInit() {
    this.setupHideColumnIfHasNoContentListener();
  }

  public onToggleColumnCollapse(collapseElement: HTMLDivElement) {
    collapseElement.classList.toggle('closed');
  }

  private setupHideColumnIfHasNoContentListener() {
    this.formGeneratorService.formControls$.pipe(takeUntil(this.destroy$)).subscribe(() => {
      setTimeout(() => {
        this.formLayoutColumn.forEach(({ nativeElement: columnElement }) => {
          const nonHiddenContents = columnElement.querySelectorAll(NON_HIDDEN_CONTENT_QUERY);
          if (!nonHiddenContents.length) {
            this.renderer.addClass(columnElement, 'hidden');
          } else {
            this.renderer.removeClass(columnElement, 'hidden');
          }
        });
      }, 0);
    });
  }

  /**
   * Use the result from the Mosaic Image Editor to override the thumbnail image.
   * The Mosaic Editor is closely tied with the thumbnail image, so it is directly used with it.
   * @param image
   */
  onMosaicImageGenerated(image: Image): void {
    const control = this.formGeneratorService.formControls.find((c) => c.key === 'editedVersion.dataSecondary.thumbnail');
    if (control) {
      const value: FormControlImage = {
        altText: image?.altText,
        caption: image?.caption,
        articleSizeUrl: image?.selectedVariant.articleSize,
        createdAt: {
          date: format(image?.date, 'yyyy-MM-dd HH:mm:ss'),
          timezone: '',
          timezone_type: 0,
        },
        fileSize: image?.fileSize,
        fullSizeUrl: image?.selectedVariant.url,
        photographer: image?.photographer,
        mimeType: '',
        resolution: image?.selectedVariant.resolution,
        source: image?.source,
        thumbnailUrl: image?.selectedVariant.thumbnail,
        title: image?.title,
        variantId: image.selectedVariant.id,
      };
      this.formGeneratorService.setFormControlByKey({
        ...control,
        value: value as FormControlImage,
      } as ImageFormControl);
    }
  }
}
