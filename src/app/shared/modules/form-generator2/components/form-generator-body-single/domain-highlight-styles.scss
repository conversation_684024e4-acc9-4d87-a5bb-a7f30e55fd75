@mixin szegedma-unordered-list() {
  li {
    @include listItemBase();
    list-style: none;
    font-weight: 700;
    padding-left: 20px;

    &::before {
      content: '';
      display: inline-block;
      position: absolute;
      top: 7px;
      left: 0;
      background-size: contain;
      background-repeat: no-repeat;
      height: 9px;
      width: 9px;
      background-image: url('/assets/images/icons/ellipse.svg');
    }
  }
}

@mixin mandiner-unordered-list() {
  li {
    @include listItemBase();
    list-style: none;
    font-weight: 700;
    padding-left: 20px;

    &::before {
      content: '';
      display: inline-block;
      position: absolute;
      top: calc(0.6em - 5px);
      left: 0;
      background-size: contain;
      background-repeat: no-repeat;
      height: 8px;
      width: 8px;
      background-image: url('/assets/images/icons/ellipse.svg');
    }
  }
}

@mixin ordered-list() {
  padding-left: 0;

  li {
    @include listItemBase();
    font-weight: 600;
    text-transform: uppercase;
    list-style: none;
    counter-increment: all;
    padding-left: 27px;

    &::before {
      content: counter(list-item) '.';
      display: inline-block;
      position: absolute;
      top: 0;
      left: 0;
      font-family: var(--kui-font-primary);
      font-weight: 700;
      font-style: italic;
      color: var(--kui-orange-600);
    }
  }
}

@mixin listItemBase() {
  position: relative;
  font-size: 18px;
  line-height: 24px;

  ol,
  ul {
    margin: 0 0 0 5px;
  }
}

@mixin basicQuote {
  font-style: italic;

  &::before {
    content: '\201E';
  }

  &::after {
    content: '\201D';
  }
}

@mixin block() {
  display: block;
}

@mixin inlineQuotationMarks {
  font-style: italic;
  p:first-of-type::before {
    content: '\201E';
  }
  p:last-of-type::after {
    content: '\201D';
  }

  /* This part is needed when the quote is empty to place the cursor between the two quotation marks.*/
  p > br[data-cke-filler] {
    display: none;
    visibility: hidden;
  }
  p:has(br[data-cke-filler]) {
    padding-left: 5px;

    &:first-of-type::before {
      margin-left: -5px;
    }
  }
}

@mixin vilaggazdasagHighlightStyles {

  figcaption {
    text-align: left;
  }
  .custom-text-style {
    &.quote {
      @include block();
      padding: 16px;
      color: #f0fdf9;
      background: #134e47;

      &:before {
        content: '';
        display: block;
        width: 48px;
        height: 48px;
        margin-bottom: 4px;
        background-image: url('/assets/images/icons/vg-quote-cms.svg');
      }

      &:after {
        content: none;
      }

      .ck-editor__nested-editable_focused,
      .ck-editor__nested-editable_focused > * {
        color: #134e47;
      }
    }

    &.highlight {
      @include block();
      border-left: 8px solid #2dd4b7;
      padding: 4px 24px;
      color: #134e47;
    }

    &.border-text {
      @include block();
      background: #042f2c;
      border-radius: 2px;
      border: 1px solid var(--Deep-Teal-400, #2dd4b7);
      border-left-width: 8px;
      padding: 16px 24px;
      color: #fff;

      h2,
      h3,
      h4 {
        color: #fff;
      }

      .ck-editor__nested-editable_focused,
      .ck-editor__nested-editable_focused > * {
        color: #134e47;
      }
    }
  }
}

@mixin magyarNemzetHighlightStyles {
  .custom-text-style {
    &.quote {
      @include block();

      .quoteBlock-content {
        @include inlineQuotationMarks();
      }

      color: #143d5d;
      font-size: 24px;
      font-weight: 700;
      line-height: 30px;
    }

    &.highlight {
      @include block();
      color: #143d5d;
      padding: 0 0 0 24px;
      border-left: 8px solid #143d5d;
      font-size: 20px;
      font-weight: 700;
      line-height: 26px;
      letter-spacing: 0.015em;
    }

    &.border-text {
      @include block();
      border: 1px solid #e6e6e6;
      border-radius: 3px;
      padding: 40px;
    }
  }
}

@mixin megyeiLapokHighlightStyles {
  .custom-text-style {
    &.quote {
      @include block();
      color: #005ca2;
      padding-left: 30px;

      .quoteBlock-content {
        @include inlineQuotationMarks();
      }
    }

    &.highlight {
      @include block();
      background-color: #005ca2;
      color: white;
      padding: 0 10px;

      a {
        color: white;
      }

      & > div.ck-editor__nested-editable_focused {
        background-color: #005ca2;
      }
    }

    &.border-text {
      @include block();
      color: #005ca2;
      border: 1px solid #cfcfcf;
      padding: 15px 25px;
    }
  }
}

@mixin borsHighlightStyles {
  .custom-text-style {
    &.quote {
      @include block();
      border-left: 3px solid #e2003b;
      padding-left: 32px;
      padding-top: 30px;
    }

    &.quote-block {
      &:before {
        content: ' ';
        width: 30px;
        height: 26px;
        display: block;
        position: absolute;
        top: 0;
        left: 32;
        z-index: 0;
        background-image: url(../../../../../../assets/images/bors-icon-quote.svg);
        background-repeat: no-repeat;
      }
    }

    &.highlight, &.border-text {
      @include block();
      padding: 32px;
      background-color: #f2f4f5;

      &-style2 {
        @include block();
        border-left: 3px solid #e2003b;
        padding-left: 32px;
      }
    }

    &.underlined-text {
      font-weight: bold;
      text-decoration: underline;
      text-decoration-color: #e2003b;
      text-decoration-thickness: 3px;
    }
  }
}

@mixin ripostHighlightStyles {
  .custom-text-style {
    &.quote {
      @include block();
      border-left: 10px solid #ffbd45;
      padding-left: 30px;

      .quoteBlock-content {
        @include inlineQuotationMarks();
      }
    }

    &.highlight {
      @include block();
      background-color: #e2003b;
      color: white;
      padding: 0 10px;

      a {
        color: white;
      }

      & > div.ck-editor__nested-editable_focused {
        background-color: #e2003b;
      }
    }

    &.border-text {
      @include block();
      border: 1px solid #cfcfcf;
      border-bottom: 6px solid #e2003b;
      padding: 15px 25px;
    }

    &.underlined-text {
      @include block();
      font-weight: bold;
      font-size: 20px;
      margin: 40px 0;
      text-decoration: underline;
      text-decoration-color: #e2003b;
      text-decoration-thickness: 3px;
    }
  }
}

@mixin metropolHighlightStyles {
  .custom-text-style {
    margin: 30px 0;

    &.quote {
      @include block();
      border-left: 10px solid #143c6d;
      padding-left: 30px;

      .quoteBlock-content {
        @include inlineQuotationMarks();
      }
    }

    &.highlight {
      @include block();
      background-color: #143c6d;
      color: white;
      padding: 0 10px;
      font-size: 20px;

      a {
        color: white;
      }

      & > div.ck-editor__nested-editable_focused {
        background-color: #143c6d;
      }
    }

    &.border-text {
      @include block();
      padding: 15px 25px;
      border: 1px solid #dee2e6;
    }
  }
}

@mixin mandinerHighlightStyles {
  .image-border {
    border: 1px solid #d74929;
    padding: 20px;
  }
  figcaption {
    text-align: left;
  }

  ul {
    @include mandiner-unordered-list();
  }
  ol {
    @include ordered-list();
    li:before {
      color: #d74929;
    }
  }

  figure.image-style-align-left {
    position: relative;
    margin-left: 20px;

    &:before {
      content: '';
      position: absolute;
      left: -20px;
      top: 0;
      width: 2px;
      height: 100%;
      background: #d74929;
    }
  }
  figure.image-style-align-right {
    position: relative;
    margin-right: 20px;

    &:before {
      content: '';
      position: absolute;
      right: -20px;
      top: 0;
      width: 2px;
      height: 100%;
      background: #d74929;
    }
  }
  .custom-text-style {
    margin: 30px 0;

    &.underlined-text {
      text-decoration: underline;
    }

    &.quote {
      @include block();
      color: #d74929;
      font-weight: bold;
      font-style: italic;
      padding-left: 30px;
      position: relative;

      &::before {
        content: '\201d';
        position: absolute;
        margin-top: 30px;
        left: 0;
        font-size: 56pt;
      }

      .quoteBlock-content {
        margin-left: 30px;
      }
    }

    &.highlight {
      @include block();
      background-color: #d74929;
      text-transform: uppercase;
      font-family: inherit;
      color: white;
      padding: 0 10px;
      font-size: 18px;

      a {
        color: #0222a2;
      }

      & > .highlightBlock-content {
        padding: 20px;

        &::after {
          content: '';
          display: block;
          background: #fff;
          width: 100px;
          height: 1px;
        }
      }

      & > div.ck-editor__nested-editable_focused {
        background-color: #d74929;
      }
    }

    &.highlight-style2 {
      @include block();
      background-color: #f5f5f5;
      color: #000;
      font-weight: bold;
      font-size: 18px;
      line-height: 22px;
      padding: 20px;

      & > .highlightBlock-style2-content {
        padding-left: 20px;
        position: relative;

        &::before {
          content: '';
          display: block;
          background: #d74929;
          position: absolute;
          left: 0;
          top: 10%;
          height: 80%;
          width: 2px;
        }
      }

      & > div.ck-editor__nested-editable_focused {
        background-color: #f5f5f5;
      }
    }

    &.border-text {
      @include block();
      padding: 15px 25px;
      border: 1px solid #d74929;

      & > .borderBlock-content {
        padding: 10px;

        &::after {
          content: '';
          display: block;
          background: #000;
          width: 100px;
          height: 1px;
        }
      }
    }
  }
}

@mixin koponyegHighlightStyles {
  .custom-text-style {
    &.underlined-text {
      text-decoration: underline;
    }

    &.quote {
      @include block();
      border-left: 5px solid #0e96dc;
      padding-left: 30px;
    }

    &.highlight {
      @include block();
      background: #e8f5fc;
      padding: 40px;
    }

    &.border-text {
      display: block;
      width: fit-content;

      .borderBlock-content {
        border: 1px solid #0e96dc;
        padding: 5px;
      }
    }
  }

  & > ol {
    & > li {
      padding-left: 0 !important;

      &:before {
        display: inline-block !important;
        margin-right: 5px;
      }
    }
  }

  ol {
    counter-reset: index;

    li {
      margin: 15px 0;
      display: block;
      position: relative;
      padding-left: 20px;

      &:before {
        counter-increment: index;
        content: counters(index, '.') '.';
        display: block;
      }
    }
  }

  figure.image {
    img {
      border-radius: 20px;
      border: 1px solid var(--kui-gray-500);
    }

    figcaption {
      font-size: 16px;
      line-height: 32px;
      padding-left: 30px;
      font-weight: 600;
      text-align: left;

      &:before {
        content: none;
      }
    }
  }
}

@mixin szabadfoldHighlightStyles {
  .custom-text-style {
    &.underlined-text {
      text-decoration: underline;
      text-decoration-color: #008c44;
      text-underline-offset: 5px;
      color: #008c44;
    }

    &.initial-letter {
      display: inline-block;

      &::first-letter {
        color: #fff;
        background: #008c44;
        padding: 5px 11px;
        margin-right: 13px;
        font-size: 40px;
        float: left;
        line-height: 1;
      }
    }

    &.highlight {
      @include block();
      border-left: 16px solid #06d36a;
      padding-left: 16px;
      color: #046850;
      font-size: 24px;
    }

    &.quote {
      @include block();
      background: rgba(6, 211, 106, 0.08);
      color: #046850;
      padding: 8px 80px 8px 16px;
      position: relative;
      font-size: 24px;

      &::before {
        content: '\201d';
        position: absolute;
        top: 8px;
        right: 16px;
        font-size: 100px;
        color: #06d36a;
        line-height: 1;
      }
    }
  }

  & > ol {
    & > li {
      padding-left: 0 !important;

      &:before {
        display: inline-block !important;
        margin-right: 5px;
      }
    }
  }

  ol {
    counter-reset: index;

    li {
      margin: 15px 0;
      display: block;
      position: relative;
      padding-left: 20px;

      &:before {
        counter-increment: index;
        content: counters(index, '.') '.';
        display: block;
      }
    }
  }

  figure.image {
    figcaption {
      font-size: 16px;
      color: #008c44;
      background: #ebfcf3;

      &:before {
        content: none;
      }
    }
  }
}

@mixin nsoHighlightStyles {
  table {
    table-layout: fixed;
  }
  figure.table {
    figcaption {
      font-family: sans-serif;
      font-weight: 600;
      text-align: left;
      text-transform: uppercase;
      font-size: 18px;
    }

    tr:nth-child(even) {
      background-color: #f2f2f2;
    }
  }
  .custom-text-style {
    &.highlight {
      @include block();
      background-color: #da0000;
      color: #fff;
      padding: 16px;
      font-size: 18px;

      & > div.ck-editor__nested-editable_focused {
        background-color: #da0000;
      }
    }

    &.quote {
      background: #f1f1f1;
      color: #1d1919;
      padding: 12px 24px;
      font-size: 24px;
      line-height: 38px;
      font-weight: 400;
      font-style: italic;
      display: flex;
      justify-content: center;
      align-items: center;
      border: none;
      gap: 16px;

      &::after,
      &::before {
        font-size: 48px;
        color: #1d1919;
        line-height: 72px;
        font-weight: 400;
        display: block;
      }

      &::after {
        content: '”';
        margin-left: -15px;
      }

      &::before {
        content: '„';
      }
    }
  }
}

@mixin szegedmaHighlightStyles {
  .custom-text-style {
    margin: 30px 0;

    &.border-text {
      @include block();
      padding: 8px 16px 16px;
      border: 1px solid #cac7c5;
      border-top: 4px solid #ff4500;
    }

    &.underlined-text {
      text-decoration: underline;
    }

    &.highlight {
      @include block();
      border-left: 4px solid #ff4500;
      padding-left: 16px;
      font-size: 18px;
    }

    &.highlight-style2 {
      @include block();
      background-color: #f7f7f6;
      color: #171615;
      font-weight: 400;
      font-size: 16px;
      line-height: normal;
      padding: 8px 12px;

      & > .highlightBlock-style2-content {
        padding-left: 50px;
        position: relative;
        font-style: italic;

        &::before {
          content: '\2139';
          display: block;
          position: absolute;
          width: 32px;
          height: 32px;
          font-size: 26px;
          text-align: center;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          border: 1px solid #000;
          border-radius: 50%;
        }
      }
    }

    &.quote {
      @include block();
      color: #171615;
      background: #fff6ec;
      font-weight: 600;
      padding-left: 30px;
      position: relative;
      font-size: 20px;

      &::before {
        content: '\201d';
        position: absolute;
        font-size: 80px;
        left: 0;
        top: 50%;
        line-height: 1;
        height: 40px;
        transform: translateY(-50%);
        padding-left: 16px;
        color: #ff4500;
      }

      .quoteBlock-content {
        padding-left: 40px;
      }
    }
  }

  figcaption {
    text-align: left;
    background: none;
    font-size: 16px;
    font-weight: 600;
  }

  a {
    text-decoration: none;
    color: #ff4500;
  }

  ul {
    @include szegedma-unordered-list();
  }

  ol {
    // It comes from Mandiner _mixins.scss
    // To define own mixin of SzegedMa ==> create szegedma-ordered-list() into _mixins.scss of SzegedMa
    // and include below instead of simple 'ordered-list'
    @include ordered-list();
  }
}

@mixin sheHighlightStyles {
  .custom-text-style {
    margin: 30px 0;

    &.quote {
      @include block();
      @include basicQuote();
      position: relative;
      padding-left: 56px;
      padding-right: 56px;
      font-style: italic;

      &::before,
      &::after {
        position: absolute;
        font-size: 80px;
        color: #881832;
        font-weight: bold;
      }

      &::before {
        content: '„';
        left: 0;
        top: -20px;
      }

      &::after {
        content: '“';
        right: 0;
        bottom: 0;
      }

      .quoteBlock-content {
        color: #000;
        font-weight: 600;
        text-align: center;
        padding: 32px;
      }
    }

    &.highlight {
      @include block();
      background-color: #881832;
      color: white;
      padding: 32px;
      font-size: 16px;
      font-weight: 700;
      line-height: 24px;

      a {
        color: white;
      }

      & > div.ck-editor__nested-editable_focused {
        background-color: #881832;
      }
    }

    &.highlight-style2 {
      @include block();
      background-color: #fccfd1;
      color: #000;
      font-size: 16px;
      font-weight: 700;
      line-height: 24px;
      padding: 16px 32px;
    }

    &.border-text {
      @include block();
      padding: 32px;
      border: 3px solid #881832;
    }

    &.border-text-black {
      @include block();
      padding: 32px;
      border: 3px solid #262324;
      font-weight: 700;
      font-size: 16px;
      line-height: 24px;
      color: #262324;
    }
  }
}

@mixin lifeHighlightStyles {
  .custom-text-style {
    margin: 30px 0;

    &.quote {
      @include block();
      @include basicQuote();
      position: relative;
      padding: 100px 80px;
      font-style: normal;

      &::before,
      &::after {
        position: absolute;
        background-repeat: no-repeat;
        width: 98px;
        height: 80px;
      }

      &::before {
        content: '';
        background-image: url('/assets/images/icons/quote-6.svg');
        left: 0;
        top: 0;
      }

      &::after {
        content: '';
        background-image: url('/assets/images/icons/quote-9.svg');
        right: 0;
        bottom: 0;
      }

      .quoteBlock-content {
        color: #000;
        font-weight: 600;
        text-align: center;
        padding: 32px;
      }
    }

    &.highlight {
      @include block();
      padding: 32px 16px;
      color: var(--kui-black);
      background: white;
      border-left: 16px solid #ffcefe;
      font-weight: 600;

      &-style2 {
        @include block();
        background: #ffcefe;
        padding: 32px;
      }
    }

    &.border-text {
      @include block();
      border: 3px solid #ffcefe;
      padding: 32px;
      margin-top: 40px;
      background: white;
      font-size: 18px;
      font-weight: 600;
      line-height: 32px;
    }

    &.border-text-black {
      @include block();
      padding: 32px;
      border: 3px solid #171517;
      font-weight: 700;
      font-size: 16px;
      line-height: 24px;
      font-style: normal;
      color: #171517;
    }

    &.underlined-text {
      text-decoration: underline;
    }
  }
}

@mixin origoHighlightStyles {
  a {
    color: #0519d2;
  }
  &.highlight {
    @include block();
    padding: 8px 16px;
    font-weight: 600;
    color: #32353d;
    border-left: 4px solid #0519d2;
  }
  &.highlight-style2 {
    @include block();
    padding: 16px 20px;
    color: #32353d;
    border-top: 4px solid #0519d2;
    border-bottom: 4px solid #0519d2;
  }
  &.highlight-style3 {
    @include block();
    padding: 32px;
    border-radius: 4px;
    border-top: 8px solid #0519d2;
    border-right: 1px solid #0519d2;
    border-bottom: 1px solid #0519d2;
    border-left: 1px solid #0519d2;
    background: #f1f6ff;

    &:before {
      content: '';
      position: absolute;
      top: 36px;
      left: 32px;
      width: 24px;
      height: 24px;
      background: url('/assets/images/icons/origo-pin.svg');
    }

    .highlightBlock-style3-content > h2:first-child {
      margin-left: 32px;
    }

    li {
      position: relative;
      list-style: none;
      padding-left: 18px;

      &:before {
        content: '';
        position: absolute;
        top: 10px;
        left: 0;
        width: 10px;
        height: 10px;
        background: url('/assets/images/icons/origo-bullet.svg');
      }
    }
  }
  &.highlight-comment {
    @include block();
    background-color: #f6f6f7;
    position: relative;
    font-style: italic;

    &:before {
      content: '';
      position: absolute;
      left: 12px;
      top: 50%;
      margin-top: -16px;
      display: block;
      width: 32px;
      height: 32px;
      background-position: center center;
      background-image: url('/assets/images/icons/origo-info.svg');
    }

    padding-left: 60px;

    .highlightBlock-comment-content {
    }
  }
  &.quote {
    @include block();
    @include basicQuote();
    padding: 0 16px;
    font-weight: 600;
    color: #32353d;
    border-left: 4px solid #0519d2;

    &:before {
      content: '';
      display: block;
      width: 23px;
      height: 19px;
      margin-bottom: 5px;
      background-image: url('/assets/images/icons/origo-quote.svg');
    }

    &:after {
      content: none;
    }
  }
  &.border-text {
    @include block();
    padding: 20px;
    border-radius: 4px;
    border: 1px solid #0519d2;
    border-top: 4px solid #0519d2;
  }
  &.underlined-text {
    display: block;
    clear: both;
    text-decoration: underline;
    font-weight: 700;
    font-size: 20px;
    margin: 40px 0;
    text-decoration-color: #0d6efd;
    text-decoration-thickness: 3px;
    word-break: break-word;
  }
}

@mixin mindmegetteHighlightStyles {
  a,
  ol li::marker,
  ul li::marker {
    color: #00964a;
  }

  .image {
    img {
      border-radius: 8px;
    }

    figcaption {
      background: transparent;
      color: #888;
    }
  }

  .custom-text-style {
    margin: 30px 0;

    &.quote {
      @include block();
      position: relative;
      padding-left: 24px;
      padding-right: 24px;

      &::before {
        content: '';
        position: absolute;
        left: 50%;
        transform: translateX(-50%) rotate(180deg);
        background: url('/assets/images/icons/mindmegette-quote.svg');
        width: 38px;
        height: 32px;
      }

      .quoteBlock-content {
        color: #00964a;
        font-style: italic;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: 0.16px;
        padding: 50px 24px 0;

        h1,
        h2,
        h3,
        h4 {
          color: var(--kui-green-700);
          font-style: italic;
          font-weight: 500;
          line-height: 24px;
          letter-spacing: 0.16px;
          text-align: center;
        }
      }
    }

    &.border-text {
      @include block();
      padding: 24px;
      border-radius: 8px;
      border: 1px solid #00964a;
      color: #00964a;
      font-weight: 500;
    }

    &.underlined-text {
      text-decoration: underline;
    }
  }

  &.highlight {
    @include block();
    padding: 24px;
    font-weight: 500;
    color: #fff;
    background-color: #00964a;
    border-radius: 8px;

    .ck-editor__nested-editable_focused {
      color: #000;
    }
  }

  &.highlight-style2 {
    @include block();
    padding-left: 24px;
    font-weight: 500;
    border-left: 5px solid #00964a;
  }

  &.highlight-style3 {
    @include block();
    padding: 32px;
    border-radius: 8px;
    border: 1px solid #ff4d6a;
    border-top-width: 8px;

    &:before {
      content: '';
      position: absolute;
      top: 32px;
      left: 32px;
      width: 32px;
      height: 32px;
      background: url('/assets/images/icons/icon-pin.svg');
      background-repeat: round;
    }

    .highlightBlock-style3-content {
      > :first-child {
        text-indent: 40px;
        margin-bottom: 16px;
      }

      p {
        margin-bottom: 24px;
        line-height: 28px;
      }
    }
  }

  &.highlight-style4 {
    @include block();
    padding-left: 24px;
    font-weight: 500;
    border-radius: 8px;
    border: 1px solid #00964a;

    li {
      font-size: 20px;
      font-weight: 700;
      color: #00964a;
    }
  }
}

@mixin pestiSracokHighlightStyles {
  a {
    color: #4E9196;
    text-decoration: none
  }
  &.highlight {
    @include block();
    padding: 30px;
    font-weight: 700;
    color: #000;
    background-color: rgba(78, 145, 150, 0.1);
    border-left: 6px solid #4E9196;
    margin-top: 20px;
  }

  &.quote {
    @include block();
    @include basicQuote();
    margin-top: 20px;
    padding: 30px;
    font-weight: 600;
    color: #000;
    border: 1px solid #4E9196;

    &:before {
      content: '';
      position: absolute;
      background-size: contain;
      background-repeat: no-repeat;
      width: 100px;
      height: 70px;
      background-image: url('/assets/images/icons/ps-quote.svg');
      right: 20px;
      bottom: 10px;
    }

    &:after {
      content: none;
    }
  }
}
