<div class="form-generator" *ngIf="!layout">
  <ng-container [ngTemplateOutlet]="formControlAdapters"> </ng-container>
</div>

<!-- Layout -->
<app-layout-generator *ngIf="layout" [layout]="layout" [formControlAdaptersTemplate]="formControlAdapters"> </app-layout-generator>

<!-- Adapters -->
<ng-template #formControlAdapters let-key="key">
  <ng-container *ngFor="let formControl of formControls$ | async; let i = index; trackBy: trackByFunction">
    <ng-container *ngIf="!key || formControl.key === key">
      <ng-container [ngSwitch]="formControl.inputType">
        <app-text-form-control-adapter
          *ngSwitchCase="'text'"
          [formControl]="formControl | asTextFormControl"
          (valueChange)="onFormControlValueChange($event, i)"
          (inputChange)="onFormControlInputChange($event)"
        >
        </app-text-form-control-adapter>

        <app-text-form-control-adapter
          *ngSwitchCase="'textarea'"
          [formControl]="formControl | asTextFormControl"
          (valueChange)="onFormControlValueChange($event, i)"
          (inputChange)="onFormControlInputChange($event)"
        >
        </app-text-form-control-adapter>

        <app-password-form-control-adapter
          *ngSwitchCase="'password'"
          [formControl]="formControl | asPasswordFormControl"
          (valueChange)="onFormControlValueChange($event, i)"
        >
        </app-password-form-control-adapter>

        <app-number-form-control-adapter
          *ngSwitchCase="'number'"
          [formControl]="formControl | asNumberFormControl"
          (valueChange)="onFormControlValueChange($event, i)"
        >
        </app-number-form-control-adapter>

        <app-select-form-control-adapter
          *ngSwitchCase="'select'"
          [formControl]="formControl | asSelectFormControl"
          (valueChange)="onFormControlValueChange($event, i)"
          [contentId]="originalContentData ? contentData?.meta?.id : null"
        >
        </app-select-form-control-adapter>

        <app-checkbox-form-control-adapter
          *ngSwitchCase="'checkbox'"
          [formControl]="formControl | asCheckboxFormControl"
          (valueChange)="onFormControlValueChange($event, i)"
        >
        </app-checkbox-form-control-adapter>

        <app-datetime-form-control-adapter
          *ngSwitchCase="'datetime'"
          [formControl]="formControl | asDatetimeFormControl"
          (valueChange)="onFormControlValueChange($event, i)"
        >
        </app-datetime-form-control-adapter>

        <app-datetime-form-control-adapter
          *ngSwitchCase="'datePicker'"
          [formControl]="formControl | asDatePickerFormControl"
          [withTime]="false"
          (valueChange)="onFormControlValueChange($event, i)"
        >
        </app-datetime-form-control-adapter>

        <app-datetime-form-control-adapter
          *ngSwitchCase="'date'"
          [formControl]="formControl | asDateFormControl"
          [withTime]="false"
          (valueChange)="onFormControlValueChange($event, i)"
        >
        </app-datetime-form-control-adapter>

        <app-datetime-range-form-control-adapter
          *ngSwitchCase="'datetimeRange'"
          [formControl]="formControl | asDatetimeRangeFormControl"
          (valueChange)="onFormControlValueChange($event, i)"
        >
        </app-datetime-range-form-control-adapter>

        <app-radio-button-form-control-adapter
          *ngSwitchCase="'radioButton'"
          [formControl]="formControl | asRadioButtonFormControl"
          (valueChange)="onFormControlValueChange($event, i)"
        >
        </app-radio-button-form-control-adapter>

        <app-radio-group-form-control-adapter
          *ngSwitchCase="'radioGroup'"
          [formControl]="formControl | asRadioGroupFormControl"
          (valueChange)="onFormControlValueChange($event, i)"
        >
        </app-radio-group-form-control-adapter>

        <app-image-form-control-adapter
          *ngSwitchCase="'image'"
          [formControl]="formControl | asImageFormControl"
          (valueChange)="onFormControlValueChange($event, i)"
        >
        </app-image-form-control-adapter>

        <app-contributors-select-form-control-adapter
          *ngSwitchCase="'contributorsSelect'"
          [formControl]="formControl | asContributorsSelectFormControl"
          (valueChange)="onFormControlValueChange($event, i)"
        >
        </app-contributors-select-form-control-adapter>

        <app-color-picker-form-control-adapter
          *ngSwitchCase="'colorPicker'"
          [formControl]="formControl | asColorPickerFormControl"
          (valueChange)="onFormControlValueChange($event, i)"
        />

        <app-checkbox-group-form-control-adapter
          *ngSwitchCase="'checkboxGroup'"
          [formControl]="formControl | asCheckboxGroupFormControl"
          (valueChange)="onFormControlValueChange($event, i)"
        >
        </app-checkbox-group-form-control-adapter>

        <app-table-form-control-adapter
          *ngSwitchCase="'collection'"
          [formControl]="formControl | asTableFormControl"
          (valueChange)="onFormControlValueChange($event, i)"
        >
        </app-table-form-control-adapter>

        <app-opening-hours-form-control-adapter
          *ngSwitchCase="'openingHours'"
          [formControl]="formControl | asOpeningHoursFormControl"
          (valueChange)="onFormControlValueChange($event, i)"
        >
        </app-opening-hours-form-control-adapter>

        <app-opening-hours-form-control-adapter
          *ngSwitchCase="'openingHoursSeasonal'"
          [onlyWeekdays]="false"
          [formControl]="formControl | asOpeningHoursFormControl"
          (valueChange)="onFormControlValueChange($event, i)"
        >
        </app-opening-hours-form-control-adapter>

        <app-other-times-form-control-adapter
          *ngSwitchCase="'otherTimes'"
          [formControl]="formControl | asOtherTimesControl"
          (valueChange)="onFormControlValueChange($event, i)"
        >
        </app-other-times-form-control-adapter>

        <app-address-form-control-adapter
          *ngSwitchCase="'address'"
          [formControl]="formControl | asAddressFormControl"
          (valueChange)="onFormControlValueChange($event, i)"
        >
        </app-address-form-control-adapter>

        <app-select-highlighted-image-form-control-adapter
          *ngSwitchCase="'selectHighlightedImage'"
          [formControl]="formControl | asSelectHighlightedImage"
          (valueChange)="onFormControlValueChange($event, i)"
        >
        </app-select-highlighted-image-form-control-adapter>
      </ng-container>
    </ng-container>
  </ng-container>
</ng-template>
