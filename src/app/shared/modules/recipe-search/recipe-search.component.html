<div class="header">
  <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
    <input
      class="title-input"
      type="text"
      nz-input
      placeholder="Keresés a címben"
      [(ngModel)]="globalSearchTerm"
      (keydown.enter)="emitSearch({ basic: viewType === 'basic' })"
    />

    <ng-template #suffixIconButton>
      <button nz-button nzType="primary" nzSearch (click)="emitSearch({ basic: viewType === 'basic' })">
        <i nz-icon nzType="search"></i>
      </button>
    </ng-template>
  </nz-input-group>

  <button
    class="advanced-search-view-button"
    nz-button
    [nzType]="viewType !== 'advanced-search' ? 'primary' : undefined"
    (click)="toggleView('advanced-search')"
  >
    R<PERSON>zletes keresés
  </button>
</div>

<div class="advanced-search-view" [ngClass]="{ show: viewType === 'advanced-search' }">
  <div class="main-content">
    <div>
      <nz-range-picker
        class="date-range-input"
        nzAllowClear
        [(ngModel)]="searchParams.publishDateRange"
        [nzPlaceHolder]="['Publikálástól', 'Publikálásig']"
      ></nz-range-picker>
    </div>

    <app-author-select-form-control
      placeHolder="Nyilvános szerző"
      authorType="publicAuthor"
      [dataModel]="searchParams.publicAuthorIds"
      (dataChange)="searchParams.publicAuthorIds = $event"
    >
    </app-author-select-form-control>

    <app-author-select-form-control
      placeHolder="Beküldő felhasználó"
      authorType="portalUser"
      [dataModel]="searchParams.portalUserIds"
      (dataChange)="searchParams.portalUserIds = $event"
    >
    </app-author-select-form-control>

    @if (isNationalDish2025Enabled) {
      <div class="national-dish-checkbox-container">
        <label nz-checkbox [(ngModel)]="searchParams.isNationalDish2025"> Az ország étele 2025 </label>
      </div>
    }
  </div>

  <ng-container *ngTemplateOutlet="buttonsBlock; context: { searchButtonText: 'Keresés', resetType: 'search' }"></ng-container>
</div>

<ng-template #buttonsBlock let-searchButtonText="searchButtonText" let-resetType="resetType">
  <div class="buttons">
    <div class="left-buttons">
      <button class="cancel-button" nz-button (click)="viewType = 'basic'">Mégse</button>
      <button nz-button nzType="primary" (click)="resetFilters()">
        <span *ngIf="resetType === 'search'">Keresési beállítások törlése</span>
      </button>
    </div>

    <button class="search-button" nz-button nzType="primary" (click)="emitSearch()">
      {{ searchButtonText }}
    </button>
  </div>
</ng-template>
