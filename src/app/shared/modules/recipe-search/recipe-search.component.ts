import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { ViewType } from '@shared/modules/article-search/article-search.definitions';
import { ActivatedRoute } from '@angular/router';
import { DatePipe } from '@angular/common';
import { RecipeSearchParams } from '@shared/modules/recipe-search/recipe-search.definitions';
import { PortalConfigService } from '@shared/services/portal-config.service';
import { PortalConfigSetting } from '@shared/definitions/portal-config';
import { toBool } from '@trendency/kesma-ui';

@Component({
  selector: 'app-recipe-search',
  templateUrl: './recipe-search.component.html',
  styleUrls: ['./recipe-search.component.scss'],
  standalone: false,
})
export class RecipeSearchComponent implements OnInit {
  @Output() dataChange = new EventEmitter<object>();

  public globalSearchTerm: string;
  public viewType: ViewType = 'basic';
  public searchParams;
  public isNationalDish2025Enabled: boolean;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly datePipe: DatePipe,
    private readonly portalConfigService: PortalConfigService
  ) {
    this.initValuesFromQueryParams();
  }

  ngOnInit(): void {
    this.isNationalDish2025Enabled = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_SEND_RECIPE_TO_NATIONAL_DISH_2025);
    this.initValuesFromQueryParams();
  }

  public emitSearch({ basic = false } = {}): void {
    const params = {
      global_filter: this.globalSearchTerm ?? undefined,
      'bySenderUsers_filter[]': !basic ? this.searchParams.portalUserIds : undefined,
      publishDateGte_filter:
        !basic && this.searchParams.publishDateRange?.[0] ? this.datePipe.transform(this.searchParams.publishDateRange[0], 'yyyy-MM-dd') : undefined,
      publishDateLte_filter:
        !basic && this.searchParams.publishDateRange?.[1] ? this.datePipe.transform(this.searchParams.publishDateRange[1], 'yyyy-MM-dd') : undefined,
      'byPublicAuthors_filter[]': this.searchParams.publicAuthorIds,
      ...(this.isNationalDish2025Enabled && { isNationalDish2025_filter: !basic && this.searchParams.isNationalDish2025 ? '1' : undefined }),
    };
    this.dataChange.emit(params);
  }

  public resetFilters() {
    this.searchParams = this.initSearchParams();
    this.globalSearchTerm = null;
    this.emitSearch();
  }

  public toggleView(viewType: Exclude<ViewType, 'basic'>): void {
    if (this.viewType !== viewType) {
      this.viewType = viewType;
    } else {
      this.viewType = 'basic';
    }
  }

  private initSearchParams(initValue: Partial<RecipeSearchParams> = {}) {
    return {
      publishDateRange: initValue.publishDateRange || [],
      publicAuthorIds: initValue.publicAuthorIds || [],
      portalUserIds: initValue.portalUserIds || [],
      ...(this.isNationalDish2025Enabled && initValue.isNationalDish2025 && { isNationalDish2025: initValue.isNationalDish2025 }),
    };
  }

  private initValuesFromQueryParams() {
    const queryParams = this.route.snapshot.queryParams;
    this.globalSearchTerm = queryParams['global_filter'] || null;

    const publicAuthorIds = this.queryParamToStringArray(queryParams['byPublicAuthors_filter[]']);
    const portalUserIds = this.queryParamToStringArray(queryParams['bySenderUsers_filter[]']);
    const publishDateRange = this.queryParamToRangeDateArray(queryParams['publishDateGte_filter'], queryParams['publishDateLte_filter']);
    const isNationalDish2025 = toBool(queryParams['isNationalDish2025_filter']);

    const initSearchValues: Partial<RecipeSearchParams> = {
      publicAuthorIds,
      portalUserIds,
      publishDateRange,
      ...(this.isNationalDish2025Enabled && { isNationalDish2025 }),
    };
    this.searchParams = this.initSearchParams(initSearchValues);
  }

  private queryParamToStringArray(value: string | string[] | null): string[] {
    if (value && !Array.isArray(value)) {
      return [value];
    }
    return value as string[];
  }

  private queryParamToRangeDateArray(gte: string, lte: string): [Date?, Date?] {
    if (gte && lte) {
      try {
        return [new Date(gte), new Date(lte)];
      } catch (e) {
        console.info('Failed to convert query param to DateRange value', e);
      }
    }
    return [];
  }
}
