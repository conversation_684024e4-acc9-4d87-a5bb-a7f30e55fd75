@use 'shared' as *;

:host {
  $gap: 10px;

  display: block;
  width: 100%;

  .header {
    display: grid;
    grid-template-columns: 1fr min-content;
    gap: $gap * 2;
  }

  %view {
    margin-top: $gap * 2;

    .main-content {
      margin-bottom: $gap * 2;
    }
  }

  .advanced-search-view {
    @extend %view;
    display: none;

    .main-content {
      display: grid;
      gap: $gap;
      grid-template-columns: repeat(auto-fill, minmax(#{'min(200px, 100%)'}, 1fr));
    }

    &.show {
      display: block;
    }
  }

  .buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: $gap;
  }

  .left-buttons {
    justify-self: start;
  }

  .cancel-button {
    margin-right: 20px;
  }

  .search-button {
    justify-self: end;
  }
}

.national-dish-checkbox-container {
  display: flex;
  align-items: center;
}

//// TODO: ha lesz rá jobb megoldás akkor csere
::ng-deep .date-range-input {
  .ant-picker-input > input {
    font-size: 13px;
  }
}
