<div class="main-element-wrapper">
  <div [class]="createVisible ? 'slidein' : 'slideout'">
    <div class="create-form" *ngIf="formInfo">
      <app-basic-item-editor
        [formInfo]="formInfo"
        [itemId]="itemId"
        mode="list"
        [contentType]="contentType"
        [contentGroup]="tableConfig.config.contentGroup"
        (close)="closeSide()"
        (basicItemCreated)="basicItemCreated()"
      >
      </app-basic-item-editor>
    </div>
  </div>
  <div class="table-wrapper" [ngClass]="{ opened: createVisible, closed: !createVisible }">
    <div class="tool-container">
      <div class="global-actions">
        <h1 class="table-title">
          {{ tableConfig.config.tableTitle | translate: tableConfig.config.tableTitleParams ?? {} }}
        </h1>
        <ng-container *ngFor="let action of globalActions; trackBy: tableActionsTrackByFn">
          <button
            *ngIf="action.inputType === dataTableInputTypes.BUTTON"
            class="global-action"
            nz-button
            [nzType]="action.buttonType ? action.buttonType : 'default'"
            [nzLoading]="getActionLoadingState('global_action', action.key) || loading"
            [nzCondition]="action.actionType !== 'confirm'"
            nz-popconfirm
            [nzPopconfirmTitle]="'CMS.confirm_action' | translate"
            (nzOnConfirm)="globalActionHandler(action.key, action)"
          >
            <i *ngIf="action.iconName" nz-icon [nzType]="action.iconName"></i>
            {{ 'CMS.' + action.label | translate }}
          </button>

          <nz-switch
            *ngIf="action.inputType === dataTableInputTypes.SWITCH"
            [(ngModel)]="buttonStates[action.key]"
            [nzLoading]="getActionLoadingState('global_action', action.key) || loading"
            [nzCondition]="action.actionType !== 'confirm'"
            nz-popconfirm
            [nzPopconfirmTitle]="'CMS.confirm_action' | translate"
            (nzOnConfirm)="globalActionSwitchHandler()"
            class="global-action switch"
            [class]="action.cssClass ? action.cssClass : ''"
            [nzCheckedChildren]="'CMS.' + action.onLabel | translate"
            [nzUnCheckedChildren]="'CMS.' + action.label | translate"
          >
          </nz-switch>

          <ng-container *ngIf="action.customTemplate" [ngTemplateOutlet]="action.customTemplate" [ngTemplateOutletContext]="{ action: action }"> </ng-container>
        </ng-container>

        <button
          *ngIf="hasCheckboxColumn && this.selectedItems.length"
          class="global-action"
          nz-button
          nzDanger
          [nzType]="'default'"
          (click)="resetSelections()"
        >
          {{ 'Kijelölés megszűntetése (' + this.selectedItems.length + ')' }}
        </button>
        <ng-content select="[data-table-custom-global-actions]"></ng-content>
      </div>

      <ng-container *ngIf="contentType === 'tag'">
        <button nz-button nzType="primary" (click)="navigateToMergeTags()">
          {{ 'CMS.mergeTags.title' | translate }}
        </button>
      </ng-container>

      <ng-container *ngIf="contentType !== 'article' && contentType !== 'branding-box' && contentType !== 'subscription' && contentType !== 'map' && contentType !== 'user-notification'">
        <ng-content select="[data-table-header-buttons]"></ng-content>
        <nz-input-group nzSearch nzCompact [nzSuffix]="inputClear" [nzAddOnAfter]="suffixIconButton" class="search-bar" *ngIf="enableSearch">
          <input type="text" nz-input placeholder="Keresés" [(ngModel)]="searchFieldData" (keydown.enter)="handleSearchParamsChange()" />
        </nz-input-group>

        <ng-template #inputClear>
          <span
            *ngIf="searchFieldData"
            nz-icon
            class="ant-input-clear-icon"
            nzTheme="fill"
            nzType="close-circle"
            (click)="searchFieldData = undefined; handleSearchParamsChange()"
          ></span>
        </ng-template>

        <ng-template #suffixIconButton>
          <button nz-button nzType="primary" nzSearch (click)="handleSearchParamsChange()">
            <i nz-icon nzType="search"></i>
          </button>
        </ng-template>
      </ng-container>

      <app-article-search *ngIf="contentType === 'article'" [overrideData]="articleSearchData" (dataChange)="handleArticleSearchParamsChange($event)">
      </app-article-search>

      <app-recipe-search *ngIf="contentType === 'recipe'" (dataChange)="handleRecipeSearchParamsChange($event)"></app-recipe-search>

      <app-branding-box-search
        *ngIf="contentType === 'branding-box'"
        [overrideSearchParams]="brandingBoxSearchData"
        (dataChange)="handleBrandingBoxeSearchParamsChange($event)"
      >
      </app-branding-box-search>

      <app-comments-search
        *ngIf="contentType === 'comment' || contentType === 'comment-reports'"
        [contentType]="contentType"
        [searchParams]="commentsSearchParams"
        (dataChange)="handleCommentsSearchParamsChange($event)"
      >
      </app-comments-search>
    </div>
    <app-subscription-search
      *ngIf="contentType === 'subscription'"
      [overrideData]="subscriptionSearchData"
      (dataChange)="handleSubscriptionSearchParamsChange($event)"
    >
    </app-subscription-search>

    <ng-content select="[data-table-header-field]"></ng-content>

    <div #upperScrollbar class="upper-scrollbar-container">
      <div class="upper-scrollbar" [ngStyle]="{ width: dataTableScrollWidth + 'px' }"></div>
    </div>

    <nz-table
      [nzData]="tableData"
      [nzLoading]="loading"
      #dataTable
      nzBordered
      [nzScroll]="{ x: dataTableScrollWidth + 'px' }"
      [nzPageSize]="tableParams.pageSize"
      [nzPageIndex]="tableParams.pageIndex"
      nzPaginationPosition="bottom"
      [nzFrontPagination]="false"
      [nzShowPagination]="true"
    >
      <thead>
        <tr>
          <th *ngIf="hasCheckboxColumn" [nzLeft]="firstColumnStick" [nzWidth]="'48px'">
            <label
              nz-checkbox
              [nzChecked]="allRowSelectCheckboxState?.checked"
              [nzIndeterminate]="allRowSelectCheckboxState?.indeterminate"
              (nzCheckedChange)="selectAllCurrentItem($event)"
            ></label>
          </th>
          <th
            *ngFor="let col of dataColumns; let i = index; let last = last; let first = first"
            [nzLeft]="firstColumnStick ? first : false"
            [nzRight]="lastColumnStick ? last : false"
            [nzColumnKey]="col.property"
            [nzWidth]="getColumnWidth(first, last)"
            [nzSortOrder]="enableSorting && col.orderable ? col.sort?.direction : undefined"
            [attr.data-columnKey]="col.key"
            [nzSortFn]="enableSorting && col.orderable ? true : undefined"
            [nzSortPriority]="true"
            [nzSortDirections]="mockSort.sortDirections"
            (nzSortOrderChange)="onSortOrderChange($event, col)"
          >
            {{ 'CMS.' + col.title | translate }}
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          [class.has-color]="getRowHasColor(tableConfig?.config?.rowStyle?.(data, index))"
          [style]="tableConfig?.config?.rowStyle?.(data, index) || ''"
          [attr.test-key]="'row' + index"
          *ngFor="let data of tableData; index as index"
        >
          <td *ngIf="hasCheckboxColumn" [nzLeft]="firstColumnStick">
            <label nz-checkbox [(ngModel)]="data.selectedInTable" (nzCheckedChange)="rowSelectionChanged(data, $event)"></label>
          </td>
          <ng-container *ngFor="let column of dataColumns; let i = index; let last = last">
            <td [nzLeft]="firstColumnStick ? i === 0 : false" *ngIf="!last || !enableActions">
              <ng-container *ngIf="!column.customTemplate">
                {{ getDataByProperty(data, column.property) }}
              </ng-container>

              <ng-container
                *ngIf="column.customTemplate"
                [ngTemplateOutlet]="column.customTemplate"
                [ngTemplateOutletContext]="{
                  data: getDataByProperty(data, column.property),
                  rowData: data,
                }"
              >
              </ng-container>
            </td>
            <td *ngIf="last && enableActions" [nzRight]="lastColumnStick ? true : false">
              <ng-container *ngFor="let action of data['rowActionsFull']">
                <app-row-action-button
                  [class]="action.customClass"
                  [testKey]="action.key + '-' + index"
                  [rowData]="action.usesData === true ? data : null"
                  [options]="action.options"
                  [userRoles]="userRoles"
                  [rowActions]="rowActions"
                  [actionKey]="action.key"
                  [disabled]="action.disabled"
                  [nzCancelText]="action?.nzCancelText"
                  [nzOkText]="action?.nzOkText"
                  [newTab]="action.newTabOption"
                  *ngIf="!action.customTemplate && !action.isOption"
                  [buttonType]="action.buttonType ? action.buttonType : 'default'"
                  [loading]="getActionLoadingState(data.id, action.key)"
                  [tooltipTitle]="'CMS.' + action.label | translate"
                  [actionType]="action.actionType"
                  [popconfirmTitle]="action?.generateDynamicTitle?.(data) || 'CMS.confirm_action' | translate"
                  [iconName]="action.iconName"
                  (action)="rowActionHandler(data, $event, data.id, action)"
                  (openInNewTab)="rowActionHandler(data, $event, data.id, action, true)"
                >
                </app-row-action-button>
                <ng-container
                  *ngIf="action.customTemplate"
                  [ngTemplateOutlet]="action.customTemplate"
                  [ngTemplateOutletContext]="{ action: action, data: data }"
                >
                </ng-container>
              </ng-container>
              <ng-container
                *ngIf="customActionTemplate"
                [ngTemplateOutlet]="customActionTemplate"
                [ngTemplateOutletContext]="{ data: data, isDeletedList: isDeletedList() }"
              >
              </ng-container>
            </td>
          </ng-container>
        </tr>
      </tbody>
      <tfoot>
        <ng-content select="[data-table-footer]"></ng-content>
      </tfoot>
    </nz-table>
    <div class="table-bottom">
      <div class="pagination-container">
        <small class="total-results" *ngIf="(allRowCount ?? tableConfig.meta?.dataCount) !== undefined"
          >{{ 'CMS.totalResults' | translate }}: <span>{{ allRowCount ?? tableConfig.meta?.dataCount }}</span></small
        >
        <nz-pagination
          *ngIf="tableConfig && tableConfig.meta && tableConfig.meta.limitable"
          class="pagination"
          [nzTotal]="allRowCount"
          [nzPageIndex]="tableParams.pageIndex"
          [(nzPageSize)]="tableParams.pageSize"
          [nzHideOnSinglePage]="false"
          [nzShowSizeChanger]="true"
          [nzPageSizeOptions]="pageSizeOptions"
          (nzPageIndexChange)="onPageIndexChange($event)"
          (nzPageSizeChange)="onPageSizeChange($event)"
        ></nz-pagination>
      </div>
      <div class="status-chart" *ngIf="contentType === ContentTypeEnum.ARTICLE">
        <div *ngFor="let workflow of workflowService.config.detailedSearchMeta" class="status-item">
          <ng-container *ngIf="workflowService.hasUniqueColor(workflow); else normalColor">
            <ng-container *ngFor="let item of $any(workflow.color) | keyvalue; last as last; first as first">
              <div [style.background-color]="item.value" [class.last]="last && !first" class="panel">
                {{ item.key[0] | titlecase }}
              </div>
            </ng-container>
          </ng-container>
          <ng-template #normalColor>
            <div [style.background-color]="workflow.color" class="panel"></div>
          </ng-template>
          <div class="color-title">{{ workflow.inputLabel }}</div>
        </div>
      </div>
    </div>
  </div>

  <ng-template #editTemplate>
    <app-form-generator mode="modal" [contentData]="formInfo" (cancel)="onCancelEdit()"> </app-form-generator>
  </ng-template>

  <ng-template #dateTemplate let-date="data">
    <nz-tag>{{ date | dateFormat: undefined : 'yyyy-MM-dd' }}</nz-tag>
  </ng-template>

  <ng-template #dateTimeTemplate let-dateTime="data">
    <nz-tag>{{ dateTime | dateFormat }}</nz-tag>
  </ng-template>

  <ng-template #isActiveTemplate let-isActive="data">
    <i
      *ngIf="isActive === true || isActive === '1' || isActive === 1"
      nz-icon
      nzType="check-circle"
      nzTheme="twotone"
      nzTwotoneColor="#52c41a"
      style="font-size: 24px"
    ></i>
    <i
      *ngIf="isActive === false || isActive === '0' || isActive === 0"
      nz-icon
      nzType="close-circle"
      nzTheme="twotone"
      nzTwotoneColor="#FF0000"
      style="font-size: 24px"
    ></i>
  </ng-template>

  <ng-template #slugTemplate let-slug="data">
    <span class="copyable-text" nz-tooltip [nzTooltipTitle]="copyTooltip | translate" (click)="copyText($event)" (mouseleave)="resetCopyTooltip()">
      {{ slug | slugComponentType: tableConfig.config }}
    </span>
  </ng-template>

  <ng-template #urlTemplate let-url="data">
    <span class="copyable-text" nz-tooltip [nzTooltipTitle]="copyTooltip | translate" (click)="copyText($event)" (mouseleave)="resetCopyTooltip()">
      {{ url }}
    </span>
  </ng-template>

  <ng-template #tagsDataTemplate let-tags="data">
    <nz-tag *ngFor="let tag of tags">{{ tag.title }}</nz-tag>
  </ng-template>

  <ng-template #imageTemplate let-imageUrl="data">
    <div *ngIf="imageUrl" class="image-wrapper" (mousemove)="onMouseMoveOverImage($event)" (click)="onImageClick($event, imageUrl)">
      <div class="image-box">
        <img [src]="imageUrl" alt="image" />
      </div>
    </div>
  </ng-template>
</div>

<app-image-preview *ngIf="previewImage$ | async as image" [previewImage]="image" (close)="previewImage$.next(null)"> </app-image-preview>
