<div class="header">
  <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
    <input
      class="title-input"
      type="text"
      nz-input
      placeholder="Keresés a címben"
      [(ngModel)]="searchParamsTitle"
      (keydown.enter)="emitSearchParams({ basic: viewType === 'basic' }); saveMainSearchToSession()"
    />

    <ng-template #suffixIconButton>
      <button nz-button nzType="primary" nzSearch (click)="emitSearchParams({ basic: viewType === 'basic' }); saveMainSearchToSession()">
        <i nz-icon nzType="search"></i>
      </button>
    </ng-template>
  </nz-input-group>

  <button
    class="advanced-search-view-button"
    nz-button
    [nzType]="viewType !== 'advanced-search' ? 'primary' : undefined"
    (click)="toggleView('advanced-search')"
  >
    R<PERSON><PERSON>tes keresés
  </button>
  <button class="advanced-search-view-button" nz-button [nzType]="viewType !== 'saved-searches' ? 'primary' : undefined" (click)="toggleView('saved-searches')">
    Mentett keresések
  </button>
  <button class="advanced-search-view-button" nz-button nzType="primary" (click)="exportArticles()">Exportálás</button>

  <button class="data-column-filter-view-button" (click)="toggleView('data-column-filter')"></button>
</div>

<div class="advanced-search-view" [ngClass]="{ show: viewType === 'advanced-search' }">
  <div class="main-content">
    <nz-select class="publication-status-input" nzPlaceHolder="Közzétételi állapot" nzAllowClear [(ngModel)]="data.searchParams.publicationStatus">
      <nz-option nzValue="published" nzLabel="Publikált"></nz-option>
      <nz-option nzValue="scheduled" nzLabel="Időzített"></nz-option>
      <nz-option nzValue="draft" nzLabel="Vázlat"></nz-option>
      <nz-option nzValue="deleted" nzLabel="Lomtárban"></nz-option>
    </nz-select>

    <nz-select class="work-process-step-input" nzPlaceHolder="Munkafolyamat" nzMode="multiple" nzAllowClear [(ngModel)]="data.searchParams.workProcessStep">
      <nz-option *ngFor="let workflow of workflowService.config.detailedSearchMeta" [nzValue]="workflow.filterKey" [nzLabel]="workflow.inputLabel"> </nz-option>
    </nz-select>

    <app-column-select-form-control
      [(columnIds)]="data.searchParams.columnIds"
      [columnSearchTermSubject]="columnSearchTermSubject"
    ></app-column-select-form-control>

    <app-author-select-form-control
      placeHolder="Szerző"
      authorType="author"
      [dataModel]="data.searchParams.authorIds"
      (dataChange)="data.searchParams.authorIds = $event"
    >
    </app-author-select-form-control>

    <app-author-select-form-control
      placeHolder="Nyilvános szerző"
      authorType="publicAuthor"
      [dataModel]="data.searchParams.publicAuthorIds"
      (dataChange)="data.searchParams.publicAuthorIds = $event"
      *ngIf="isExternalContributors"
    >
    </app-author-select-form-control>

    <app-author-select-form-control
      placeHolder="Elszámolási szerző"
      authorType="contributor"
      [dataModel]="data.searchParams.contributingAuthorIds"
      (dataChange)="data.searchParams.contributingAuthorIds = $event"
    >
    </app-author-select-form-control>

    <nz-select
      class="tags-input"
      nzMode="multiple"
      nzPlaceHolder="Címkék"
      nzAllowClear
      nzShowSearch
      nzServerSearch
      (nzOnSearch)="tagSearchTermSubject.next($event)"
      (nzScrollToBottom)="tagScrollTermSubject.next()"
      [(ngModel)]="data.searchParams.tagIds"
    >
      <ng-container *ngIf="tags?.length">
        <nz-option *ngFor="let tag of tags" [nzValue]="tag.id" [nzLabel]="tag.title"></nz-option>
      </ng-container>

      <nz-option *ngIf="isInitialInteraction['tags'] || isSearchInProgress['tags']" nzDisabled nzCustomContent>
        <i nz-icon nzType="loading" class="loading-icon"></i> Betöltés...
      </nz-option>
    </nz-select>

    <nz-select
      *ngIf="isNewsFeed"
      class="tags-input"
      nzMode="multiple"
      nzPlaceHolder="Hírfolyamok"
      nzAllowClear
      nzShowSearch
      nzServerSearch
      (nzOnSearch)="newsFeedSearchTermSubject.next($event)"
      [(ngModel)]="data.searchParams.newsFeedIds"
    >
      <ng-container *ngIf="newsFeeds?.length">
        <nz-option *ngFor="let newsFeed of newsFeeds" [nzValue]="newsFeed.id" [nzLabel]="newsFeed.title"></nz-option>
      </ng-container>

      <nz-option *ngIf="isInitialInteraction['newsFeeds'] || isSearchInProgress['newsFeeds']" nzDisabled nzCustomContent>
        <i nz-icon nzType="loading" class="loading-icon"></i> Betöltés...
      </nz-option>
    </nz-select>

    <nz-select
      *ngIf="hasMultipleDossierSearchTerm"
      class="tags-input"
      nzMode="multiple"
      nzPlaceHolder="Dossziék"
      nzAllowClear
      nzShowSearch
      nzServerSearch
      (nzOnSearch)="dossierSearchTermSubject.next($event)"
      [(ngModel)]="data.searchParams.dossierIds"
    >
      <ng-container *ngIf="dossiers?.length">
        <nz-option *ngFor="let dossier of dossiers" [nzValue]="dossier.id" [nzLabel]="dossier.title"></nz-option>
      </ng-container>

      <nz-option *ngIf="isInitialInteraction['dossiers'] || isSearchInProgress['dossiers']" nzDisabled nzCustomContent>
        <i nz-icon nzType="loading" class="loading-icon"></i> Betöltés...
      </nz-option>
    </nz-select>

    <nz-select
      *ngIf="hasMultipleRegionSearchTerm"
      class="tags-input"
      nzMode="multiple"
      nzPlaceHolder="Régiók"
      nzAllowClear
      nzShowSearch
      nzServerSearch
      (nzOnSearch)="regionSearchTermSubject.next($event)"
      [(ngModel)]="data.searchParams.regionIds"
    >
      <ng-container *ngIf="regions?.length">
        <nz-option *ngFor="let region of regions" [nzValue]="region.id" [nzLabel]="region.title"></nz-option>
      </ng-container>

      <nz-option *ngIf="isInitialInteraction['regions'] || isSearchInProgress['regions']" nzDisabled nzCustomContent>
        <i nz-icon nzType="loading" class="loading-icon"></i> Betöltés...
      </nz-option>
    </nz-select>

    <nz-select
      *ngIf="isMegyeiLap"
      class="tags-input"
      nzMode="multiple"
      nzPlaceHolder="Sportok"
      nzAllowClear
      nzShowSearch
      nzServerSearch
      (nzOnSearch)="sportSearchTermSubject.next($event)"
      [(ngModel)]="data.searchParams.sportIds"
    >
      <ng-container *ngIf="sports?.length">
        <nz-option *ngFor="let sport of sports" [nzValue]="sport.id" [nzLabel]="sport.title"></nz-option>
      </ng-container>

      <nz-option *ngIf="isInitialInteraction['sports'] || isSearchInProgress['sports']" nzDisabled nzCustomContent>
        <i nz-icon nzType="loading" class="loading-icon"></i> Betöltés...
      </nz-option>
    </nz-select>

    <div>
      <nz-range-picker
        class="date-range-input"
        nzAllowClear
        [(ngModel)]="data.searchParams.dateRange"
        [nzPlaceHolder]="['Publikálástól', 'Publikálásig']"
      ></nz-range-picker>
    </div>

    <nz-select class="ownership-input" nzPlaceHolder="Saját cikkek" nzAllowClear [(ngModel)]="data.searchParams.ownershipType">
      <nz-option nzValue="own" nzLabel="Saját cikkek"></nz-option>
    </nz-select>

    <nz-select class="ownership-input" nzPlaceHolder="Cikk típus" nzAllowClear [(ngModel)]="data.searchParams.kind" *ngIf="domainKey !== 'ripost'">
      <nz-option *ngIf="!isMegyeiLap" nzValue="opinion" nzLabel="Vélemény cikkek"></nz-option>
      <nz-option *ngIf="isMagyarNemzet" nzValue="reviewable" nzLabel="Így írnak ők cikk"></nz-option>
      <nz-option *ngIf="isMagyarNemzet" nzValue="reviewable-article" nzLabel="Így írnak ők vélemény"></nz-option>
      <nz-option *ngIf="isMegyeiLap" nzValue="notebook" nzLabel="Jegyzet cikkek"></nz-option>
      <nz-option *ngIf="isShortNewsEnabled" nzValue="short-news" nzLabel="Rövid hír"></nz-option>
      <nz-option *ngIf="isFastNewsEnabled" nzValue="fast-news" nzLabel="Gyors hír"></nz-option>
      <nz-option *ngIf="isFoundationContentEnable" nzValue="foundation-content" nzLabel="Alapkő"></nz-option>
      <!-- Későbbiekben szükség lehet majd portal-configra hozzá, de egyelőre BE nem készített ilyet -->
      <nz-option *ngIf="isVilaggazdasag" nzValue="interview_filter" nzLabel="Interjús tartalom"></nz-option>
      <nz-option *ngIf="isVilaggazdasag" nzValue="video_filter" nzLabel="Videós tartalom"></nz-option>
      <nz-option *ngIf="isVilaggazdasag" nzValue="gallery_filter" nzLabel="Galériás tartalom"></nz-option>
      <nz-option *ngIf="isVilaggazdasag" nzValue="podcast_filter" nzLabel="Podcast tartalom"></nz-option>
    </nz-select>

    <nz-date-picker *ngIf="!isPestiSracok" class="issue-date-input" nzAllowClear nzShowToday nzPlaceHolder="Lapszám" [(ngModel)]="data.searchParams.issueDate">
    </nz-date-picker>

    <nz-select
      class="media-input"
      nzPlaceHolder="Print oldalszám"
      nzAllowClear
      nzMode="multiple"
      [(ngModel)]="data.searchParams.printPageCount"
      *ngIf="!isMetropol && !isBors && !isRipost && !isVilaggazdasag && !isPestiSracok"
    >
      <nz-option *ngFor="let pageNumber of printPages" [nzValue]="pageNumber" [nzLabel]="pageNumber"></nz-option>
    </nz-select>

    <nz-select
      class="media-input"
      nzPlaceHolder="Online / Print"
      nzAllowClear
      *ngIf="!isMetropol && !isBors && !isRipost && !isVilaggazdasag && !isPestiSracok"
      [(ngModel)]="data.searchParams.mediaType"
    >
      <nz-option nzValue="online" nzLabel="Online"></nz-option>
      <nz-option nzValue="print" nzLabel="Print"></nz-option>
    </nz-select>

    <nz-select class="originality-input" nzPlaceHolder="Eredetiség" nzAllowClear [(ngModel)]="data.searchParams.originalityType">
      <nz-option nzValue="original" nzLabel="Eredeti"></nz-option>
      <nz-option nzValue="copied" nzLabel="Másolt"></nz-option>
      <nz-option *ngIf="isMandiner" nzValue="digitalCopy" nzLabel="Digitlási másolat"></nz-option>
    </nz-select>

    <nz-select class="importance-input" nzPlaceHolder="Fontosság" nzAllowClear [(ngModel)]="data.searchParams.importanceType">
      <nz-option nzValue="normal" nzLabel="Normál"></nz-option>
      <nz-option nzValue="important" nzLabel="Fontos"></nz-option>
      <nz-option nzValue="lead" nzLabel="Vezető"></nz-option>
    </nz-select>

    <nz-select *ngIf="!isPestiSracok" class="media-input" nzPlaceHolder="Hírlevélbe küldve" nzAllowClear [(ngModel)]="data.searchParams.sendInNewsletters">
      <nz-option nzValue="true" nzLabel="Küldve"></nz-option>
      <nz-option nzValue="false" nzLabel="Nincs küldve"></nz-option>
    </nz-select>

    <nz-select
      *ngIf="isFoundationContentEnable"
      class="tags-input"
      nzMode="multiple"
      nzPlaceHolder="Alapkő tartalom címke"
      nzAllowClear
      nzShowSearch
      nzServerSearch
      (nzOnSearch)="foundationContentTermSubject.next($event)"
      (nzScrollToBottom)="foundationContentScrollTermSubject.next()"
      [(ngModel)]="data.searchParams.foundationTagSelect_filter"
    >
      <ng-container *ngIf="foundationTags?.length">
        <nz-option *ngFor="let tag of foundationTags" [nzValue]="tag.id" [nzLabel]="tag.title"></nz-option>
      </ng-container>

      <nz-option *ngIf="isInitialInteraction['foundationTags'] || isSearchInProgress['foundationTags']" nzDisabled nzCustomContent>
        <i nz-icon nzType="loading" class="loading-icon"></i> Betöltés...
      </nz-option>
    </nz-select>

    <nz-select nzPlaceHolder="Anyagtípusok" nzAllowClear *ngIf="isArticleMaterialSearchEnabled" [(ngModel)]="data.searchParams.material_type">
      <nz-option nzValue="own_material" nzLabel="Saját anyag"></nz-option>
      <nz-option nzValue="news_service_material" nzLabel="Hírügyeleti anyag"></nz-option>
      <nz-option nzValue="other_material" nzLabel="Egyéb"></nz-option>
    </nz-select>
  </div>

  <ng-container *ngTemplateOutlet="buttonsBlock; context: { searchButtonText: 'Keresés', resetType: 'search' }"></ng-container>
</div>

<div class="data-column-filter-view" *ngIf="viewType === 'data-column-filter'">
  <div class="main-content">
    <div class="checkboxes-title">Oszlopok beállítása</div>

    <div class="checkboxes">
      <label nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.author">Szerző</label>

      <label nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.publicAuthor">Nyilvános szerző</label>

      <label nz-checkbox *ngIf="!isExternalContributors; else externalContributor" [(ngModel)]="data.dataColumnSelectionMap.contributor">
        Elszámolási szerző
      </label>

      <ng-template #externalContributor>
        <label nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.externalContributor"> Elszámolási szerző </label>
      </ng-template>

      <label nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.columnTitle">Rovat</label>

      <label nz-checkbox *ngIf="!isMetropol && !isBors && !isRipost && !isVilaggazdasag && !isPestiSracok" [(ngModel)]="data.dataColumnSelectionMap.isPrintShow"
        >Print / Online</label
      >

      <label nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.copyOrOriginal">Másolt / Eredeti</label>

      <label *ngIf="!isPestiSracok" nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.printPublishedDate">PN</label>

      <label *ngIf="!isPestiSracok" nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.printPageNumber">PO</label>

      <label *ngIf="!isPestiSracok" nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.flekk">Flekk</label>

      <label nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.characterCount">Karakterszám</label>

      <label nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.isActiveShow">Állapot</label>

      <label nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.tag">Címke</label>

      <label nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.publishDate">Publikálás dátuma</label>

      <label nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.printStatusShow">Munkafolyamat</label>

      <label nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.publicStateShow">Publikálási állapot</label>

      <label nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.priority">Fontosság</label>

      <label nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.views">Megtekintések</label>

      <label *ngIf="isWeightedViewsEnabled" nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.weightedViews">Súlyozott megtekintések</label>

      <label *ngIf="isGoogleRealtimePageViewsEnabled" nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.last10MinutesPageViews"
        >Legutóbbi 10 perc megtekintései</label
      >
      <label *ngIf="isGoogleRealtimePageViewsEnabled" nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.last20MinutesPageViews"
        >Legutóbbi 20 perc megtekintései</label
      >
      <label *ngIf="isGoogleRealtimePageViewsEnabled" nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.last60MinutesPageViews"
        >Legutóbbi 60 perc megtekintései</label
      >

      <label nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.createdAt">Létrehozás dátuma</label>

      <label nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.editorComment">Szerkesztői megjegyzés</label>

      <label *ngIf="!isPestiSracok" nz-checkbox [(ngModel)]="data.dataColumnSelectionMap.sendInNewsletters">Hírlevélbe küldve</label>

      <label nz-checkbox *ngIf="domainKey === 'metropol'" [(ngModel)]="data.dataColumnSelectionMap.regions">Régiók</label>

      <label nz-checkbox *ngIf="domainKey === 'magyarNemzet' || domainKey === 'pesti_sracok'" [(ngModel)]="data.dataColumnSelectionMap.rowCount"
        >Sorok száma</label
      >

      <label nz-checkbox *ngIf="isFoundationContentEnable" [(ngModel)]="data.dataColumnSelectionMap.foundationTagTitle">Alapkő tartalom címke</label>

      <label nz-checkbox *ngIf="isArticleMaterialSearchEnabled" [(ngModel)]="data.dataColumnSelectionMap.materialType"> Anyagtípus </label>

      <label nz-checkbox [(ngModel)]="$any(data.dataColumnSelectionMap).url"> URL </label>

      <label nz-checkbox [(ngModel)]="$any(data.dataColumnSelectionMap).id"> Azonosító </label>
    </div>
  </div>

  <ng-container *ngTemplateOutlet="buttonsBlock; context: { searchButtonText: 'Alkalmaz', resetType: 'column' }"></ng-container>
</div>

<div class="saved-search-view" [ngClass]="{ show: viewType === 'saved-searches' }">
  <div class="saved-button-list">
    <div *ngFor="let item of searchList" class="saved-button-wrapper">
      <button nz-button nzType="primary" class="first-save-button" (click)="loadSavedSearch(item.filter)">
        {{ item.title }}
      </button>
      <button nz-button nzType="primary" class="second-save-button" (click)="showSearchModal(item)">
        <i nz-icon nzType="edit"></i>
      </button>
      <button
        nz-button
        nzType="primary"
        class="third-save-button"
        nz-tooltip
        nzTooltipTitle="Törlés"
        nz-popconfirm
        nzPopconfirmPlacement="top"
        nzPopconfirmTitle="Biztos törölni akarod a keresést?"
        (nzOnConfirm)="deleteSearch(item.id)"
      >
        <i nz-icon nzType="delete"></i>
      </button>
    </div>
  </div>
</div>

<ng-template #buttonsBlock let-searchButtonText="searchButtonText" let-resetType="resetType">
  <div class="buttons">
    <div class="left-buttons">
      <button class="cancel-button" nz-button (click)="viewType = 'basic'">Mégse</button>
      <button nz-button nzType="primary" (click)="showSearchModal()" class="save-button">
        <span>Keresés és oszlopbeállítás mentése</span>
      </button>
      <button nz-button nzType="primary" (click)="reset(resetType)">
        <span *ngIf="resetType === 'column'">Oszlop beállítások törlése</span>
        <span *ngIf="resetType === 'search'">Keresési beállítások törlése</span>
      </button>
    </div>

    <button class="search-button" nz-button nzType="primary" (click)="emitSearchParams()">
      {{ searchButtonText }}
    </button>
  </div>
</ng-template>

<nz-modal [(nzVisible)]="isVisible" nzTitle="Keresési beállítások mentése" (nzOnCancel)="closeModal()" (nzOnOk)="CreateUpdateSearch()">
  <ng-container *nzModalContent>
    <div>
      <input class="print-page-count-input" nz-input min="0" placeholder="Keresés címe" [(ngModel)]="newSearchTitle" />
    </div>
    <br />
    <div *ngIf="!isPestiSracok" class="checkbox">
      <label nz-checkbox [(ngModel)]="saveAsDefault">Alapértelmezett</label>
    </div>
  </ng-container>
  <div *nzModalFooter>
    <button nz-button nzType="default" (click)="closeModal()">Mégse</button>
    <button nz-button nzType="primary" (click)="CreateUpdateSearch()" [disabled]="!newSearchTitle">Keresés mentése</button>
  </div>
</nz-modal>
