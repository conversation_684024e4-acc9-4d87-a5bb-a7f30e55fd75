import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  Output,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { fromEvent, merge, Observable } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DestroyService } from 'src/app/shared/services/destroy.service';

@Component({
  selector: 'app-text-form-control',
  templateUrl: 'text-form-control.component.html',
  styleUrls: ['./text-form-control.component.scss'],
  providers: [DestroyService],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class TextFormControlComponent implements AfterViewInit {
  @ViewChild('textarea', { static: true }) private textarea: ElementRef<HTMLTextAreaElement>;

  @Input() public textLengthOverride?: number;
  @Input() public forceLengthError: boolean = false;
  @Input() public maxLength: number = null;
  @Input() public softLimit: number = this.maxLength;
  @Input() public value: string = '';
  @Input() public label: string = '';
  @Input() public placeholder: string = '';
  @Input() public isRequired: boolean = false;
  @Input() public isDisabled: boolean = false;
  @Input() public errorMessage: string = '';
  @Input() public forbiddenKeyboardKeys: string[] = [];
  @Input() tooltipTitle?: string;

  @Input() public showTitleGenerator: boolean = false;

  @Output() public valueChange: EventEmitter<string> = new EventEmitter<string>();
  @Output() public inputChange: EventEmitter<string> = new EventEmitter<string>();

  constructor(
    private renderer: Renderer2,
    private destroy$: DestroyService,
    private cd: ChangeDetectorRef
  ) {}

  ngAfterViewInit() {
    this.autoResize();
    this.setupInputChangeListener();
  }

  get textLength() {
    if (typeof this.textLengthOverride === 'number') {
      return this.textLengthOverride;
    }
    return this.textarea?.nativeElement?.value?.length;
  }

  onChange(event: Event) {
    const value = (event.target as HTMLTextAreaElement).value;
    this.valueChange.emit(value);
  }

  onInput(event: Event) {
    const value = (event.target as HTMLTextAreaElement).value;
    this.inputChange.emit(value);
  }

  onKeyPress(event: KeyboardEvent) {
    this.handleForbiddenKeyboardKeys(event);
  }

  autoResize() {
    setTimeout(() => {
      this.renderer.setStyle(this.textarea.nativeElement, 'height', '40px');
      this.renderer.setStyle(this.textarea.nativeElement, 'height', `${this.textarea.nativeElement.scrollHeight}px`);
    }, 0);
  }

  private setupInputChangeListener() {
    this.getInputChangeObservable()
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.cd.detectChanges();
        this.autoResize();
      });
  }

  private getInputChangeObservable(): Observable<any> {
    return merge(
      fromEvent(this.textarea.nativeElement, 'cut'),
      fromEvent(this.textarea.nativeElement, 'paste'),
      fromEvent(this.textarea.nativeElement, 'input'),
      fromEvent(this.textarea.nativeElement, 'keydown')
    );
  }

  private handleForbiddenKeyboardKeys(event: KeyboardEvent) {
    const { key } = event;

    if (this.forbiddenKeyboardKeys.includes(key)) {
      event.preventDefault();
    }
  }
}
