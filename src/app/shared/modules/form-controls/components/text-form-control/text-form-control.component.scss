@use 'shared' as *;

.text-form-control-container {
  position: relative;
  min-height: 40px;
  margin-bottom: 10px;
  width: 100%;

  ::placeholder {
    color: $gray-medium;
  }

  .word-count {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    width: 30px;
    height: 48px;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: #7dcdbf;
    border-radius: 0 10px 10px 0;
    color: #fff;

    &.warning {
      background-color: #f7db8b;
    }

    &.invalid {
      background-color: #ce806f;
    }
  }

  textarea[class] {
    min-height: 48px;
    height: 48px;
    width: calc(100% - 30px);
    border: 1px solid #ebebeb;
    border-radius: 10px 0 0 10px;
    resize: none;
    padding: 11px 6px 5px;

    &.size-xs {
      height: 48px;
    }

    &.size-sm {
      height: 58px;
    }

    &.size-md {
      height: 82px;
    }

    &.invalid {
      border: 1px solid $error-color;
    }
  }
}

.row-flex {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  width: 100%;

  button {
    margin-left: 10px;
    margin-top: 10px;
  }
}
