<div class="row-flex">
  <div class="text-form-control-container form-control-container">
    <app-form-control-label class="form-control-label" [label]="label" [isRequired]="isRequired"
                            [tooltipTitle]="tooltipTitle"></app-form-control-label>
    <textarea
      #textarea
      [attr.test-key]="label"
      class="form-control"
      [ngClass]="{ invalid: !!errorMessage }"
      [attr.maxlength]="maxLength"
      [disabled]="isDisabled"
      [placeholder]="placeholder"
      [value]="value"
      (change)="onChange($event)"
      (input)="onInput($event)"
      (keypress)="onKeyPress($event)"
    >
    </textarea>
    <small
      class="word-count"
      [ngClass]="{
      warning: softLimit && textLength === softLimit,
      invalid: (softLimit && textLength > softLimit) || forceLengthError,
    }"
    >
      {{ textLength }}
    </small>
  </div>

  @if (showTitleGenerator) {
    <button
      [attr.test-key]="'openGenerateTitleModal'"
      nzType="primary"
      nz-button
      (click)="titleGeneratorModal.open(value)"
    >
      Generálás
    </button>
    <app-title-generator #titleGeneratorModal
                         (applyTitle)="value = $event; this.valueChange.emit($event)"></app-title-generator>
  }
</div>

<app-error-message class="error-message" [errorMessage]="errorMessage"></app-error-message>

