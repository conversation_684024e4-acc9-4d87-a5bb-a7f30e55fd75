<div class="select-highlighted-image-form-control-container form-control-container">
  <nz-form-control layout="horizontal">
    <div class="row-flex">
      <nz-select
        [(ngModel)]="selectedImage"
        (ngModelChange)="this.selectedImageChanged.emit($event)"
        [nzCustomTemplate]="selectedImageTemplate"
        [nzPlaceHolder]="label"
        [compareWith]="compareWithFn"
        [nzOptionHeightPx]="75"
        [nzOptionOverflowSize]="3"
        [nzServerSearch]="serverSideSearch"
        [attr.test-key]="label"
        (nzOnSearch)="searchEvent.next($event)"
        (nzScrollToBottom)="onScrolledToBottom()"
        nzShowSearch
        nzAllowClear
        class="image-select"
      >
        <nz-option *ngFor="let image of images; trackBy: trackByFn" [nzLabel]="image.title || image.caption" [nzValue]="image" [attr.test-key]="image.title || image.caption" nzCustomContent>
          <ng-container *ngIf="image" [ngTemplateOutlet]="itemTemplate" [ngTemplateOutletContext]="{ $implicit: image }"></ng-container>
        </nz-option>
      </nz-select>
      <label class="form-control-label"> {{ label }} <span *ngIf="isRequired" class="required-indicator">*</span> </label>
    </div>
  </nz-form-control>
</div>

<ng-template #selectedImageTemplate let-selected>
  <ng-container *ngIf="selectedImage" [ngTemplateOutlet]="itemTemplate" [ngTemplateOutletContext]="{ $implicit: selectedImage }"></ng-container>
</ng-template>

<ng-template #itemTemplate let-image>
  <img
    [src]="image?.[thumbnailProperty] || '/assets/images/layout-frames/mno/image-placeholder.png'"
    [alt]="image.title || image.caption"
    class="image-thumbnail"
    loading="lazy"
  />
  {{ image.title || image.caption || 'CMS.no-title' | translate }}
</ng-template>
