import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgZorroModule } from '../ng-zorro/ng-zorro.module';
import { MediaStoreModule } from 'src/app/media-store/media-store.module';
import { ReactiveFormsModule } from '@angular/forms';
import { ImageFormControlComponent } from './components/image-form-control/image-form-control.component';
import { ColumnSelectFormControlComponent } from './components/column-select-form-control/column-select-form-control.component';
import { TextFormControlComponent } from './components/text-form-control/text-form-control.component';
import { SharedModule } from '../../shared.module';
import { SelectFormControlComponent } from './components/selects/select-form-control/select-form-control.component';
import { TreeSelectFormControlComponent } from './components/selects/tree-select-form-control/tree-select-form-control.component';
import { FormModule } from '@external/form';
import { NumberFormControlComponent } from './components/number-form-control/number-form-control.component';
import { RadioGroupFormControlComponent } from './components/radio-group-form-control/radio-group-form-control.component';
import { CheckboxGroupFormControlComponent } from './components/checkbox-group-form-control/checkbox-group-form-control.component';
import { ErrorMessageComponent } from './helpers/error-message/error-message.component';
import { FormControlLabelComponent } from './helpers/form-control-label/form-control-label.component';
import { DatetimeFormControlComponent } from './components/datetime-form-control/datetime-form-control.component';
import { HtmlFormControlComponent } from './components/html-form-control/html-form-control.component';
import { CheckboxFormControlComponent } from './components/checkbox-form-control/checkbox-form-control.component';
import { ColorPickerFormControlComponent } from './components/colorpicker-form-control/colorpicker-form-control.component';
import { MapFormControlComponent } from './components/map-form-control/map-form-control.component';
import { GoogleMapsModule } from '@angular/google-maps';
import { TableFormControlComponent } from './components/table-form-control/table-form-control.component';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { PermissionFormControlComponent } from '../form-generator/form-controls/permission-form-control/permission-form-control.component';
import { CheckboxTreeModule } from '../checkbox-tree/checkbox-tree.module';
import { UserPermissionsFormControlComponent } from '../form-generator/form-controls/user-permissions-form-control/user-permissions-form-control.component';
import { OpeningHoursFormControlComponent } from './components/opening-hours-form-control/opening-hours-form-control.component';
import { OpeningHoursDayComponent } from './components/opening-hours-form-control/opening-hours-day/opening-hours-day.component';
import { OpeningHoursInputComponent } from './components/opening-hours-form-control/opening-hours-input/opening-hours-input.component';
import { AddressFormControlComponent } from './components/address-form-control/address-form-control.component';
import { OrderAddressFieldsPipe } from './components/address-form-control/order-address-fields.pipe';
import { TagGeneratorModule } from '../../../modules/tag-generator/tag-generator.module';
import { AuthorSelectFormControlComponent } from '@shared/modules/form-controls/components/author-select-form-control/author-select-form-control.component';
import { OtherTimesFormControlComponent } from './components/other-times-form-control/other-times-form-control.component';
import { SelectHighlightedImageComponent } from './components/selects/select-highlighted-image/select-highlighted-image.component';
import { PasswordFormControlComponent } from './components/password-form-control/password-form-control.component';
import { DatetimeRangeFormControlComponent } from './components/datetime-range-form-control/datetime-range-form-control.component';
import { TitleGeneratorComponent } from '@shared/modules/title-generator/title-generator.component';

@NgModule({
  declarations: [
    ImageFormControlComponent,
    ColumnSelectFormControlComponent,
    TextFormControlComponent,
    SelectFormControlComponent,
    TreeSelectFormControlComponent,
    NumberFormControlComponent,
    RadioGroupFormControlComponent,
    CheckboxGroupFormControlComponent,
    ErrorMessageComponent,
    FormControlLabelComponent,
    DatetimeFormControlComponent,
    HtmlFormControlComponent,
    CheckboxFormControlComponent,
    ColorPickerFormControlComponent,
    MapFormControlComponent,
    TableFormControlComponent,
    PermissionFormControlComponent,
    UserPermissionsFormControlComponent,
    OpeningHoursFormControlComponent,
    OpeningHoursDayComponent,
    OpeningHoursInputComponent,
    AddressFormControlComponent,
    OrderAddressFieldsPipe,
    AuthorSelectFormControlComponent,
    OtherTimesFormControlComponent,
    SelectHighlightedImageComponent,
    PasswordFormControlComponent,
    DatetimeRangeFormControlComponent,
  ],
  imports: [
    CommonModule,
    NgZorroModule,
    ReactiveFormsModule,
    MediaStoreModule,
    SharedModule,
    FormModule,
    GoogleMapsModule,
    DragDropModule,
    CheckboxTreeModule,
    TagGeneratorModule,
    TitleGeneratorComponent,
  ],
  exports: [
    ImageFormControlComponent,
    ColumnSelectFormControlComponent,
    TextFormControlComponent,
    SelectFormControlComponent,
    TreeSelectFormControlComponent,
    NumberFormControlComponent,
    RadioGroupFormControlComponent,
    CheckboxGroupFormControlComponent,
    DatetimeFormControlComponent,
    HtmlFormControlComponent,
    CheckboxFormControlComponent,
    ColorPickerFormControlComponent,
    MapFormControlComponent,
    TableFormControlComponent,
    OpeningHoursFormControlComponent,
    AddressFormControlComponent,
    AuthorSelectFormControlComponent,
    FormControlLabelComponent,
    OtherTimesFormControlComponent,
    SelectHighlightedImageComponent,
    PasswordFormControlComponent,
    DatetimeRangeFormControlComponent,
  ],
})
export class FormControlsModule {}
