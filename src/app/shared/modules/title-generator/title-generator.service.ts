import { inject, Injectable } from '@angular/core';
import { ReqService } from '@external/http';
import { TitleGeneratorInputData, TitleGeneratorResponse } from '@shared/modules/title-generator/title-generator.definitions';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class TitleGeneratorService {
  private readonly reqService: ReqService = inject(ReqService);

  generateArticleTitles(articleId: string, data: TitleGeneratorInputData): Observable<TitleGeneratorResponse> {
    return this.reqService.post(`api/hu/hu/content-page/ai/generate-article-titles/${articleId}`, { data });
  }

  selectGeneratedArticleTitle(articleId: string, responseId: string, titleId: string): Observable<void> {
    return this.reqService.patch(`/api/hu/hu/content-page/ai/response-title-selection/${responseId}/article/${articleId}/title/${titleId}`, {});
  }
}
