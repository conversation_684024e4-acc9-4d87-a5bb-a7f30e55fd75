@use 'shared' as *;

.note {
  margin-bottom: 30px;
}

.title-generator-form {
  margin-bottom: 30px;

  nz-form-label {
    display: block;
    margin-bottom: 10px;
    font-weight: bold;
  }

  nz-form-item + nz-form-item {
    display: block;
    margin-top: 20px;
  }

  ::ng-deep {
    nz-select-top-control {
      min-height: 40px;
    }
  }
}

.generated-titles {
  nz-radio-group {
    display: flex;
    flex-direction: column;
    width: 100%;

    label {
      margin-top: 10px;
    }
  }
}

.error {
  color: red;
}
