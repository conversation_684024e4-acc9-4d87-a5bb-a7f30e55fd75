export interface TitleGeneratorInputData {
  mood?: string[];
  examples?: string[];
}

export interface TitleGeneratorResponseDataTitle {
  id: string;
  title: string;
}

export interface TitleGeneratorResponseData {
  responseId: string;
  titles: TitleGeneratorResponseDataTitle[];
}

export interface TitleGeneratorResponse {
  data: TitleGeneratorResponseData;
  meta: {
    estimated_cost_usd: number;
    estimated_output_tokens: number;
    input_tokens: number;
    remainingTokens: number;
  };
}
