import { ChangeDetectionStrategy, Component, EventEmitter, inject, OnDestroy, Output, signal } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzColDirective } from 'ng-zorro-antd/grid';
import { NzFormControlComponent, NzFormItemComponent } from 'ng-zorro-antd/form';
import { NzSelectComponent } from 'ng-zorro-antd/select';
import { NzModalComponent, NzModalContentDirective } from 'ng-zorro-antd/modal';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { TitleGeneratorService } from '@shared/modules/title-generator/title-generator.service';
import { ActivatedRoute } from '@angular/router';
import { TitleGeneratorResponse, TitleGeneratorResponseDataTitle } from '@shared/modules/title-generator/title-generator.definitions';
import { NzRadioComponent, NzRadioGroupComponent } from 'ng-zorro-antd/radio';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-title-generator',
  templateUrl: './title-generator.component.html',
  styleUrls: ['./title-generator.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NzColDirective,
    NzFormControlComponent,
    NzSelectComponent,
    ReactiveFormsModule,
    NzFormItemComponent,
    NzModalComponent,
    NzButtonComponent,
    NzModalContentDirective,
    NzRadioGroupComponent,
    NzRadioComponent,
    FormsModule,
  ],
})
export class TitleGeneratorComponent implements OnDestroy {
  private readonly titleGeneratorService: TitleGeneratorService = inject(TitleGeneratorService);
  private readonly route: ActivatedRoute = inject(ActivatedRoute);

  protected showModal = signal<boolean>(false);
  protected isLoading = signal<boolean>(false);
  protected isModalLoading = signal<boolean>(false);
  protected error = signal<string | undefined>(undefined);

  articleId: string = this.route.snapshot.paramMap.get('id');

  form = new FormGroup({
    mood: new FormControl<string[]>([]),
    examples: new FormControl<string[]>([]),
  });

  generatedResponseId: string | undefined = undefined;
  generatedTitles: TitleGeneratorResponseDataTitle[] | undefined = undefined;
  selectedTitle: TitleGeneratorResponseDataTitle | undefined = undefined;

  destroy$: Subject<void> | undefined;

  @Output() applyTitle = new EventEmitter<string>();

  open(initialExample?: string): void {
    this.destroy$ = new Subject<void>();
    this.showModal.set(true);

    if (initialExample) {
      this.form.get('examples').patchValue([initialExample]);
    }
  }

  close(): void {
    this.reset();
    this.showModal.set(false);
    this.destroy$.next();
    this.destroy$.complete();
  }

  reset(): void {
    this.isLoading.set(false);
    this.isModalLoading.set(false);
    this.error.set(undefined);
    this.form.reset();
    this.generatedTitles = undefined;
    this.selectedTitle = undefined;
  }

  generate(): void {
    this.isLoading.set(true);
    this.error.set(undefined);

    this.titleGeneratorService
      .generateArticleTitles(this.articleId, this.form.value)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: TitleGeneratorResponse) => {
          this.isLoading.set(false);
          this.generatedResponseId = response?.data?.responseId;
          this.generatedTitles = response?.data?.titles ?? [];
        },
        error: () => {
          this.isLoading.set(false);
          this.error.set(
            'Hiba történt, próbáld újra! Ellenőrizd, hogy a cikk törzse tartalmaz-e szöveget vagy próbálj módosítani a kulcsszavakon és példákon!'
          );
        },
      });
  }

  apply(): void {
    if (this.generatedResponseId && this.selectedTitle) {
      this.isModalLoading.set(true);

      this.titleGeneratorService
        .selectGeneratedArticleTitle(this.articleId, this.generatedResponseId, this.selectedTitle.id)
        .pipe(takeUntil(this.destroy$))
        .subscribe(() => {
          this.applyTitle.emit(this.selectedTitle.title);
          this.isModalLoading.set(false);
          this.close();
        });
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
