<nz-modal [(nzVisible)]="showModal" nzTitle="Cikk cím generálás" nzCancelText="Mégsem" nzOkText="Alkalmazás"
          [nzOkDisabled]="!generatedResponseId || !selectedTitle"
          [nzOkLoading]="isModalLoading()"
          (nzOnCancel)="close()"
          (nzOnOk)="apply()">
  <ng-container *nzModalContent>
    <div class="note">A generálás a cikk szövege alapján történik, de a pontosabb eredmény érdekében opcionálisan
      megadhatsz hangulatra vonatkozó
      kulcszavakat és cikk cím példákat.
    </div>
    <form class="title-generator-form" [formGroup]="form">
      <nz-form-item>
        <nz-form-label>Hangulat</nz-form-label>
        <nz-form-control>
          <nz-select
            id="mood"
            formControlName="mood"
            nzMode="tags"
            [nzTokenSeparators]="[',']"
            nzPlaceHolder="Például: Vidám, figyelemfelkeltő"
            [nzNotFoundContent]="'Írd le a hangulatot kulcszavakkal'"
          ></nz-select>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label>Példák</nz-form-label>
        <nz-form-control>
          <nz-select
            id="examples"
            formControlName="examples"
            nzMode="tags"
            [nzTokenSeparators]="[',']"
            nzPlaceHolder="Adj meg cikk cím példákat"
            [nzNotFoundContent]="'Adj meg példákat'"
          ></nz-select>
        </nz-form-control>
      </nz-form-item>
      <button
        class="generate-btn"
        [attr.test-key]="'generateTitle'"
        nzType="primary"
        nz-button
        [nzLoading]="isLoading()"
        (click)="generate()"
      >
        Generálás
      </button>
    </form>

    @if (generatedResponseId && generatedTitles) {
      <div class="generated-titles">
        @if (generatedTitles.length > 0) {
          <div><strong>Válassz az alábbi generált címek közül:</strong></div>
          <nz-radio-group id="generated-title" [(ngModel)]="selectedTitle">
            @for (item of generatedTitles; track item.id) {
              <label for="generated-title" nz-radio-button [nzValue]="item">{{ item.title }}</label>
            }
          </nz-radio-group>
        } @else {
          <div class="error">Nincs eredmény, próbáld újra más kulcsszavakkal!</div>
        }
      </div>
    }

    @if (error()) {
      <div class="error">{{ error() }}</div>
    }
  </ng-container>
</nz-modal>
