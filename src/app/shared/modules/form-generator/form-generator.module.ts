import { GoogleMapsModule } from '@angular/google-maps';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { EditorModule } from '@tinymce/tinymce-angular';
import { FormModule } from '@external/form';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzTableModule } from 'ng-zorro-antd/table';
import { SharedModule } from 'src/app/shared/shared.module';
import { SelectSettingsService } from '../../services/select-settings.service';
import { FormControlsModule } from '../form-controls/form-controls.module';
import { ImageFormControlAdapterComponent } from './form-control-adapters/image-form-control-adapter/image-form-control-adapter.component';
import { SelectFormControlAdapterComponent } from './form-control-adapters/select-form-control-adapter/select-form-control-adapter.component';
import { TreeSelectFormControlAdapterComponent } from './form-control-adapters/tree-select-form-control-adapter/tree-select-form-control-adapter.component';
import { FormControlGroupComponent } from './form-control-group/form-control-group.component';
import { FormControlContextMenuComponent } from './form-control-menu/form-control-context-menu/form-control-context-menu.component';
import { FormControlMenuDirective } from './form-control-menu/form-control-menu.directive';
import { FormControlToolbarMenuComponent } from './form-control-menu/form-control-toolbar-menu/form-control-toolbar-menu.component';
import { CheckboxFormControlComponent } from './form-controls/checkbox-form-control/checkbox-form-control.component';
import { CollectionFormControlComponent } from './form-controls/collection-form-control/collection-form-control.component';
import { ColorPickerFormControlComponent } from './form-controls/colorpicker-form-control/colorpicker-form-control.component';
/*import { ImageFormControlComponent } from './form-controls/image-form-control/image-form-control.component';
import { FileUploadFormControlComponent } from './form-controls/file-upload-form-control/file-upload-form-control.component';
import { ToggleFormControlComponent } from './form-controls/toggle-form-control/toggle-form-control.component';*/
import { DatetimeFormControlComponent } from './form-controls/datetime-form-control/datetime-form-control.component';
import { DefaultInteractions } from './form-controls/form-control.interactions';
import { HiddenFormControlComponent } from './form-controls/hidden-form-control/hidden-form-control.component';
import { HtmlFormControlComponent } from './form-controls/html-form-control/html-form-control.component';
import { MapFormControlComponent } from './form-controls/map-form-control/map-form-control.component';
import { MenuTreeComponent } from './form-controls/menu-tree/menu-tree.component';
import { NumberFormControlComponent } from './form-controls/number-form-control/number-form-control.component';
import { PasswordFormControlComponent } from './form-controls/password-form-control/password-form-control.component';
import { RadioButtonFormControlComponent } from './form-controls/radio-button-form-control/radio-button-form-control.component';
import { RadioGroupFormControlComponent } from './form-controls/radio-group-form-control/radio-group-form-control.component';
import { SelectFormControlComponent } from './form-controls/select-form-control/select-form-control.component';
import { SelectWithImageFormControlComponent } from './form-controls/select-with-image-form-control/select-with-image-form-control.component';
import { TextFormControlComponent } from './form-controls/text-form-control/text-form-control.component';
import { FormGeneratorComponent } from './form-generator.component';
import { FormControlMapService } from './services/form-control-map.service';
import { FormControlService } from './services/form-control.service';
import { FormGeneratorService } from './services/form-generator.service';
import { EadvertFormControlComponent } from './form-controls/eadvert-form-control/eadvert-form-control.component';

const FORM_CONTROLS = [
  TextFormControlComponent,
  HtmlFormControlComponent,
  SelectFormControlComponent,
  CheckboxFormControlComponent,
  SelectWithImageFormControlComponent,
  NumberFormControlComponent,
  DatetimeFormControlComponent,
  ImageFormControlAdapterComponent,
  RadioGroupFormControlComponent,
  RadioButtonFormControlComponent,
  ColorPickerFormControlComponent,
  PasswordFormControlComponent,
  CollectionFormControlComponent,
  MapFormControlComponent,
  EadvertFormControlComponent,
  SelectFormControlAdapterComponent,
  TreeSelectFormControlAdapterComponent,
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    EditorModule,
    FormModule,
    SharedModule,
    DragDropModule,
    FormControlsModule,
    NzTableModule,
    GoogleMapsModule,
    NzPopoverModule,
  ],
  exports: [
    FormGeneratorComponent,
    MenuTreeComponent,
    MapFormControlComponent,
    TextFormControlComponent,
    NumberFormControlComponent,
    ColorPickerFormControlComponent,
  ],
  declarations: [
    FormGeneratorComponent,
    ...FORM_CONTROLS,
    HiddenFormControlComponent,
    MenuTreeComponent,
    FormControlContextMenuComponent,
    FormControlToolbarMenuComponent,
    FormControlMenuDirective,
    FormControlGroupComponent,
  ],
  providers: [FormGeneratorService, FormControlService, FormControlMapService, SelectSettingsService, DefaultInteractions],
})
export class FormGeneratorModule {}
