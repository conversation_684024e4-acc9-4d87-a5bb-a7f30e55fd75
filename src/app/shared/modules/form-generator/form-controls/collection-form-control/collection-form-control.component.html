<div class="collection-container" [hidden]="state.data.isHidden || isHidden">
  <label class="collection-label">
    {{ collectionTitle | translate }}
  </label>

  <button
    nz-button
    nzType="primary"
    nzSize="small"
    class="add-me-button"
    [attr.test-key]="(collectionTitle | translate) + ' gyűj<PERSON><PERSON>y elem hozz<PERSON>'"
    [disabled]="state.data.isDisabled"
    (click)="triggerModal('create', null)"
  >
    {{ addNewButtonTitle | translate }}
  </button>

  <nz-table [nzData]="collection" class="item-table" *ngIf="collection?.length > 0" [nzShowPagination]="false">
    <thead>
      <tr class="item-row">
        <th *ngFor="let hreader of tableHeaders" class="item-header">
          {{ 'CMS.' + hreader | translate }}
        </th>
        <th class="item-header">Műveletek</th>
      </tr>
    </thead>
    <tbody cdkDropList (cdkDropListDropped)="drop($event)">
      <tr *ngFor="let item of collection; let i = index" cdkDrag class="item-row">
        <td *ngFor="let header of tableHeaders" class="item-data {{ header }}">
          <span *ngIf="item[header] !== true && item[header] !== false">
            <ng-container [ngSwitch]="header">
              <ng-container *ngSwitchCase="'answers'">
                <div *ngFor="let answer of item[header]">
                  {{ answer?.title }}
                </div>
              </ng-container>
              <ng-container *ngSwitchCase="'image'">
                <img *ngIf="item[header]?.thumbnailUrl" [src]="item[header].thumbnailUrl" class="collection-image" />
              </ng-container>
              <ng-container *ngSwitchCase="'startDate'">
                {{ item[header] | dateFormat }}
              </ng-container>
              <ng-container *ngSwitchCase="'endDate'">
                {{ item[header] | dateFormat }}
              </ng-container>
              <ng-container *ngSwitchCase="'url'">
                <div class="url-wrapper">
                  <a [href]="item[header]" target="_blank"> {{ item[header] }}</a>
                </div>
              </ng-container>
              <ng-container *ngSwitchDefault>
                {{ item[header] }}
              </ng-container>
            </ng-container>
          </span>
          <i *ngIf="item[header] === true" nz-icon nzType="check-circle" nzTheme="twotone" nzTwotoneColor="#52c41a" style="font-size: 24px"></i>
          <i *ngIf="item[header] === false" nz-icon nzType="close-circle" nzTheme="twotone" nzTwotoneColor="#FF0000" style="font-size: 24px"></i>
        </td>
        <td class="item-data">
          <button
            nz-button
            nzType="primary"
            nzSize="small"
            class="item-button"
            [disabled]="state.data.isDisabled"
            nz-popconfirm
            [attr.test-key]="i + ' gyűjtemény elem törlése'"
            nzPopconfirmTitle="Biztos törölni akarja az elemet"
            (nzOnConfirm)="removeItemModal(item)"
          >
            <i nz-icon nzType="delete" nzTheme="outline"></i>
          </button>
          <button
            nz-button
            nzType="primary"
            nzSize="small"
            class="item-button"
            [attr.test-key]="i + ' gyűjtemény elem módosítása'"
            [disabled]="state.data.isDisabled"
            (click)="triggerModal('edit', item)"
          >
            <i nz-icon nzType="edit" nzTheme="outline"></i>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>
</div>
