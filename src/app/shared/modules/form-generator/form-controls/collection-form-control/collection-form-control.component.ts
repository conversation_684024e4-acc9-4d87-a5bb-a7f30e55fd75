import { Component, ElementRef, Injector, OnInit, Renderer2 } from '@angular/core';
import { BaseFormControlComponent } from '../base-form-control.component';
import { UntypedFormControl } from '@angular/forms';
import { pipe, Subject } from 'rxjs';
import { FormControlService } from '../../services/form-control.service';
import { debounceTime, filter, map, takeUntil } from 'rxjs/operators';
import { ApiService } from 'src/app/core/services/api.service';
import { NzModalService } from 'ng-zorro-antd/modal';
import { ContentData } from '../../form-generator.definitions';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { ReqService } from '@external/http';
import { SharedService } from '@shared/services/shared.service';

@Component({
  selector: 'app-collection-form-control',
  templateUrl: 'collection-form-control.component.html',
  styleUrls: ['./collection-form-control.component.scss'],
  standalone: false,
})
export class CollectionFormControlComponent extends BaseFormControlComponent implements OnInit {
  public number: UntypedFormControl;
  public destroy$: Subject<boolean> = new Subject<boolean>();
  public title: string;

  public collection: any[] = [];
  public addButtonTitle: string;
  public removeButtonTitle: string;
  public contentData: ContentData;
  public tableHeaders: string[];
  private formUrl: string;
  public collectionTitle: string;
  public addNewButtonTitle: string;
  isHidden: boolean;

  private formRequestSubject = new Subject<{ state: 'edit' | 'create'; data: any }>();

  itemIndex = 0;

  protected formControlValueChangedPipe = pipe(map((value) => value || null));

  public parserFn = (value: string) => value.trim().replace(/[^\d-]+/g, '');

  constructor(
    protected formControlService: FormControlService,
    public elementRef: ElementRef,
    protected injector: Injector,
    protected renderer: Renderer2,
    public apiService: ApiService,
    public modalService: NzModalService,
    private reqService: ReqService,
    private sharedService: SharedService
  ) {
    super(formControlService, elementRef, injector, renderer);
  }

  ngOnInit() {
    this.collection = this.inputField.value;
    this.collectionTitle = 'CMS.' + this.inputField.inputLabel;
    this.addNewButtonTitle = 'CMS.add' + this.inputField.inputLabel;
    this.formUrl = this.inputField.inputInfo.formUrl;
    this.isHidden = this.inputField.inputInfo?.isHidden ?? false;
    if (this.collection?.length > 0) {
      this.tableHeaders = Object.keys(this.collection[0]);
      if (this.tableHeaders && this.tableHeaders.length > 0) {
        this.tableHeaders = this.tableHeaders.filter((header) => header !== 'id');
      }
    }
    this.state$
      .pipe(
        takeUntil(this.componentDestroyed$),
        filter((formControlState) => this.formControlService.isInTriggeredBy(['value'], formControlState.meta.lastTriggeredBy))
      )
      .subscribe((formControlState) => {
        this.collection = formControlState.data.value;
        if (this.collection.length > 0) {
          // When an interaction changed the headers, update them:
          this.tableHeaders = Object.keys(this.collection[0]);
          if (this.tableHeaders && this.tableHeaders.length > 0) {
            this.tableHeaders = this.tableHeaders.filter((header) => header !== 'id');
          }
        }
      });

    this.formRequestSubject.pipe(takeUntil(this.componentDestroyed$), debounceTime(200)).subscribe(({ state, data }) => {
      switch (state) {
        case 'edit':
          this.editItemModal(data);
          return;
        case 'create':
          this.openAdditemModal();
          return;
      }
    });
  }

  triggerModal(state: 'edit' | 'create', data: any): void {
    this.formRequestSubject.next({
      state,
      data,
    });
  }

  openAdditemModal() {
    this.reqService.get(this.formUrl).subscribe((res) => {
      this.contentData = res;
      this.setRequest({
        triggeredBy: 'openFormModal',
        formControlState: this.state,
        data: this.contentData,
        response: (contentData: ContentData) => {
          const item = {};
          contentData.data.forEach((element) => {
            item[element.key] = element.value;
            if (element.value === null && element?.asserts?.Range) {
              item[element.key] = element.asserts.Range.min;
            }
          });
          this.collection.push(item);
          this.tableHeaders = Object.keys(item);
          if (this.tableHeaders && this.tableHeaders.length > 0) {
            this.tableHeaders = this.tableHeaders.filter((header) => header !== 'id');
          }
          this.setState({ value: this.collection });
        },
      });
    });
  }

  drop(event: CdkDragDrop<string[]>): void {
    moveItemInArray(this.collection, event.previousIndex, event.currentIndex);
    const newArray = this.collection;
    this.setState({ value: [] });
    this.setState({ value: newArray });
  }

  editItemModal(modedItem: any) {
    this.reqService.get(this.formUrl).subscribe(({ data: formInfo, meta }) => {
      Object.keys(modedItem).forEach((key) => {
        const field = formInfo.find((field) => field.key === key);
        if (field) {
          field.value = modedItem[key];
        }
      });
      this.contentData = {
        data: formInfo,
        meta,
      };
      this.setRequest({
        triggeredBy: 'openFormModal',
        formControlState: this.state,
        data: this.contentData,
        response: (contentData: ContentData) => {
          const item = {};
          Object.keys(modedItem).forEach((key) => {
            const field = contentData.data.find((field) => field.key === key);
            if (field) {
              item[key] = field.value;
            }
          });
          this.removeItemModal(modedItem, true);
          this.collection.splice(this.itemIndex, 0, item);
          this.itemIndex = 0;
          this.tableHeaders = Object.keys(modedItem);
          if (this.tableHeaders && this.tableHeaders.length > 0) {
            this.tableHeaders = this.tableHeaders.filter((header) => header !== 'id');
          }
          this.setState({ value: this.collection });
        },
      });
    });
  }

  removeItemModal(item: any, replace?: boolean) {
    if (replace) {
      this.itemIndex = this.collection.findIndex((e) => e === item);
    }
    const newCollection = this.collection.filter((e) => e !== item);
    this.collection = newCollection;
    this.inputField.value = this.collection;
    this.setState({ value: this.inputField.value });
  }
}
