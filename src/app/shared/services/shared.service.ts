import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { BaseContentVariant, ComponentContent } from 'src/app/core/api.definitons';
import { MenuItemsService } from 'src/app/core/services/menu-items.service';
import { ICMSMenuItem } from '../definitions/shared.definitions';
import { ContentData } from '../modules/form-generator/form-generator.definitions';

@Injectable({ providedIn: 'root' })
export class SharedService {
  constructor(
    private translate: TranslateService,
    private message: NzMessageService,
    private menuItemsService: MenuItemsService
  ) {}

  public showNotification(type: 'info' | 'success' | 'warning' | 'error', message: string | string[], duration: number = 3000) {
    // const type = ['', 'info', 'success', 'warning', 'error'];
    const translatedMessage = this.translate.instant(message);

    this.message.create(type, translatedMessage, { nzDuration: duration });
  }

  public getMenuItem(type: string, menu: ICMSMenuItem[] = this.menuItemsService.items): ICMSMenuItem {
    for (let i = 0; i < menu.length; ++i) {
      if (menu[i].type === type) {
        return menu[i];
      }
      if (menu[i].children) {
        const result = this.getMenuItem(type, menu[i].children);
        if (result) {
          return result;
        }
      }
    }
  }

  public getComponentAsObjectByType(componentDatas: ComponentContent[], componentType: string) {
    const component = componentDatas.find((c) => c.type === componentType);
    const result = this.getComponentAsObject(component);
    return result;
  }

  public copyJSONObject<T>(value: T): T {
    try {
      const copy: T = JSON.parse(JSON.stringify(value));
      return copy;
    } catch (err) {
      return value;
    }
  }

  public generateErrorMessageFromHttpResponse(error: HttpErrorResponse, contentData: ContentData, moduleName: string = '') {
    switch (error.status) {
      case 400:
      case 404:
      case 405:
      case 500:
        {
          const errorObject = error.error.data;
          const errorMessage = errorObject?.message || 'CMS.unknownError';
          this.showNotification('error', errorMessage);
        }
        break;
      case 422:
        {
          const errorArray = error.error.data;
          errorArray.forEach((invalidInputFieldError) => {
            const { propertyPath: key, message } = invalidInputFieldError;

            let inputLabel = this.getErrorousInputLabel(key, contentData.data);
            inputLabel = inputLabel.includes('day') ? 'nap' : inputLabel;
            let errorMessage = `${moduleName} ${inputLabel}: ${message}`;
            if (message?.includes('unavailable_publish_period')) {
              errorMessage = 'notifier.form.unavailable_publish_period';
            }
            if (errorMessage?.includes('PromotionContent')) {
              errorMessage = `ERROR.${inputLabel}`;
            }
            this.showNotification('error', errorMessage);
          });
        }
        break;
      case 413:
        {
          this.showNotification('error', 'CMS.imageSizeTooLarge');
        }
        break;
      default:
        {
          if (error.error && error.error.errors) {
            error.error.errors.forEach((serverError) => {
              const message = serverError.message;
              this.showNotification('error', message);
            });
          } else {
            this.showNotification('error', 'CMS.unknownError');
          }
        }
        break;
    }
  }

  private getComponentAsObject(component: ComponentContent): any {
    const result: any = {};

    if (component) {
      component.details.forEach((field) => {
        /*const value = field.properties || field.value;
        if (field.valueDetails) {
          const detailKeys = Object.keys(field.valueDetails);
          detailKeys.forEach(key => {
            value[key] = field.valueDetails[key];
          });
        }*/
        result[field.key] = field.value;
      });
      if (component.subComponents.length) {
        result.subComponents = [];
        component.subComponents.forEach((subComponent) => {
          const subElem = this.getComponentAsObject(subComponent);
          result.subComponents.push(subElem);
        });
      }
    }

    return result;
  }

  private getErrorousInputLabel(key: string, content: BaseContentVariant[]): string {
    let componentIndex = '';
    let labelToTranslate = '';
    let addition = '';

    if (key.includes('[value]')) {
      let propertyPathChain: string[] = key
        .split('[')
        .map((part) => part.replace(']', ''))
        .filter((part) => part !== '');

      const valueIndex = propertyPathChain.findIndex((part) => part === 'value');
      if (valueIndex !== -1) {
        propertyPathChain = propertyPathChain.slice(0, valueIndex + 2);
        const componentInValueIndex = +propertyPathChain[propertyPathChain.length - 1];
        componentIndex = `${componentInValueIndex + 1}. `;
      }

      const inputField = propertyPathChain.reduce((obj, property) => {
        if (!obj) return undefined;
        if (Array.isArray(obj)) {
          return obj[property] || obj.find((item) => item.key === property);
        }
        return obj['key'] === property ? obj : obj[property];
      }, content);

      labelToTranslate = inputField && inputField.type;
    } else {
      const inputField = content.find((item) => item.key === key);
      labelToTranslate = inputField && inputField.inputLabel;
    }

    const regex = /\[(\d+)\]/g;
    if (!labelToTranslate && key.match(regex) !== null) {
      const firstPart = key.split('[')[0];
      const arrayNumber = parseInt(key.split('[')[1].split(']')[0], 10) + 1;
      addition = ' ' + arrayNumber;
      const inputField = content.find((item) => item.key === firstPart);
      labelToTranslate = inputField && inputField.inputLabel;
    }

    if (key.includes('experience.studyAmount')) {
      const experienceRegex = /^experience\.(.+)$/;
      labelToTranslate = key.match(experienceRegex)[1];
    }
    if (key.includes('PromotionContent')) {
      return key;
    }

    const inputLabel = labelToTranslate ? this.translate.instant(labelToTranslate) : key;

    return `${componentIndex}${inputLabel} ${addition}`;
  }
}
