import { FormLayout } from '../../shared/modules/form-generator/form-generator.definitions';

export const articleEditorMetaLayout = (
  dynamicData: Record<string, string> = {
    publicAuthorType: 'publicAuthor',
  }
): FormLayout => {
  return {
    rows: [
      {
        type: 'row',
        columns: [
          {
            type: 'column',
            title: 'Rovatok',
            width: '40%',
            paddingRight: '60px',
            content: [
              {
                type: 'row',
                columns: [
                  {
                    type: 'column',
                    width: '50%',
                    content: [
                      {
                        type: 'content',
                        key: 'primaryColumn',
                      },
                    ],
                  },
                  {
                    type: 'column',
                    width: '50%',
                    content: [
                      {
                        type: 'content',
                        key: 'secondaryColumns',
                      },
                    ],
                  },
                ],
              },
              {
                type: 'row',
                columns: [
                  {
                    type: 'column',
                    width: '50%',
                    content: [
                      {
                        type: 'content',
                        key: 'status.journalIssue',
                      },
                    ],
                    showOnlyIf: {
                      domains: ['mandiner'],
                    },
                  },
                ],
              },
            ],
          },
          {
            type: 'column',
            title: 'Címkék',
            width: '40%',
            content: [
              {
                type: 'content',
                key: 'tags',
              },
            ],
          },
          {
            type: 'column',
            title: 'Válogatások',
            width: '100%',
            showOnlyIf: {
              domains: ['mindmegette'],
            },
            content: [
              {
                type: 'content',
                key: 'selections',
              },
            ],
          },
        ],
      },
      {
        type: 'row',
        columns: [
          {
            type: 'column',
            title: 'Tulajdonságok',
            width: '100%',
            content: [
              {
                type: 'row',
                columns: [
                  {
                    type: 'column',
                    width: '40%',
                    paddingRight: '60px',
                    content: [
                      {
                        type: 'content',
                        key: 'status.articleSource',
                      },
                      {
                        type: 'content',
                        key: 'status.materialType',
                      },
                      {
                        type: 'content',
                        key: 'status.tourGuide',
                      },
                      {
                        type: 'content',
                        key: 'isProtected',
                      },
                      {
                        type: 'content',
                        key: 'protectedEditor',
                      },

                      {
                        type: 'content',
                        key: 'sponsorship',
                      },
                      {
                        type: 'content',
                        key: 'status.embedPrAdvert',
                      },
                      {
                        type: 'content',
                        key: 'editedVersion.dataSecondary.readingLength',
                      },
                      {
                        type: 'content',
                        key: 'editedVersion.dataSecondary.settlement',
                      },
                      {
                        type: 'content',
                        key: 'isPaywalled',
                      },
                      {
                        type: 'content',
                        key: 'status.isProtectedContent',
                      },
                      {
                        type: 'content',
                        key: 'status.isRecommendedToHomepage',
                      },
                      {
                        type: 'content',
                        key: 'editedVersion.dataSecondary.showInRss',
                      },
                      {
                        type: 'content',
                        key: 'status.isPrioritySortRss',
                      },
                      {
                        type: 'content',
                        key: 'status.showInNewsAggregators',
                      },
                      {
                        type: 'content',
                        key: 'editedVersion.dataSecondary.sendInNewsletters',
                      },
                      {
                        type: 'content',
                        key: 'editedVersion.dataSecondary.withoutAds',
                      },
                      {
                        type: 'content',
                        key: 'editedVersion.dataSecondary.isExceptionAdvertEnabled',
                      },
                      {
                        type: 'content',
                        key: 'ArticleProperty.AlternativeView',
                      },
                      {
                        type: 'content',
                        key: 'status.isAdultsOnly',
                      },
                      {
                        type: 'content',
                        key: 'status.isOlympic',
                      },
                      {
                        type: 'content',
                        key: 'status.isEditorMessageable',
                      },
                      {
                        type: 'content',
                        key: 'dbCache.commentsDisabled',
                      },
                      {
                        type: 'content',
                        key: 'dbCache.likesAndDislikesDisabled',
                      },
                      {
                        type: 'content',
                        key: 'status.isShowInStatistics',
                      },
                      {
                        // Required portal configuration: content_article_show_guarantee_type_field
                        type: 'content',
                        key: 'editedVersion.dataSecondary.isGuaranteeType',
                      },
                      {
                        type: 'content',
                        key: 'editedVersion.dataSecondary.hideThumbnailFromBody',
                      },
                      {
                        type: 'content',
                        key: 'editedVersion.dataSecondary.thumbnail',
                      },
                      {
                        type: 'content',
                        key: 'editedVersion.dataSecondary.secondaryThumbnail',
                      },
                      {
                        type: 'content',
                        key: 'editedVersion.dataSecondary.magazineCover',
                      },
                    ],
                  },
                  {
                    type: 'column',
                    width: '40%',
                    content: [
                      {
                        type: 'content',
                        key: 'status.articleMedium',
                      },
                      {
                        type: 'content',
                        key: 'priority',
                      },
                      {
                        type: 'content',
                        key: 'publishDate',
                      },
                      {
                        type: 'content',
                        key: 'status.contentType',
                      },
                      {
                        type: 'content',
                        key: 'editedVersion.dataSecondary.editorComment',
                      },
                      {
                        type: 'content',
                        key: 'status.messageToHomepageEditor',
                      },
                      {
                        type: 'content',
                        key: 'dossier',
                      },
                      {
                        type: 'content',
                        key: 'dossiers',
                      },
                      {
                        type: 'content',
                        key: 'status.isHiddenOnLayoutDossier',
                      },
                      {
                        type: 'content',
                        key: 'status.isAheadInLayoutDossier',
                      },
                      {
                        type: 'content',
                        key: 'games',
                      },
                      {
                        type: 'content',
                        key: 'status.aniCode',
                      },
                      {
                        type: 'content',
                        key: 'articleStatsTypes',
                      },
                      {
                        type: 'row',
                        columns: [
                          {
                            type: 'column',
                            width: '100%',
                            showOnlyIf: {
                              printOrOnline: 'online',
                              domains: ['nso'],
                            },
                            content: [
                              {
                                type: 'content',
                                key: 'editedVersion.dataSecondary.leadVideo',
                              },
                              {
                                type: 'content',
                                key: 'editedVersion.dataSecondary.leadVideoPosition',
                              },
                            ],
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        type: 'row',
        columns: [
          {
            type: 'column',
            title: 'Szerzők',
            width: '80%',
            content: [
              {
                type: 'row',
                columns: [
                  {
                    type: 'column',
                    width: '50%',
                    content: [
                      {
                        type: 'content',
                        key: 'status.user',
                      },
                    ],
                  },
                ],
              },
              {
                type: 'row',
                columns: [
                  {
                    type: 'column',
                    width: '100%',
                    content: [
                      {
                        type: 'content',
                        key: 'editedVersion.dataSecondary.avatar',
                      },
                    ],
                  },
                ],
              },
              {
                type: 'row',
                columns: [
                  {
                    type: 'column',
                    width: '50%',
                    content: [
                      {
                        type: 'content',
                        key: `editedVersion.dataSecondary.${dynamicData.publicAuthorType}`,
                      },
                      {
                        type: 'content',
                        key: 'editedVersion.dataSecondary.publicAuthorEmail',
                      },
                      {
                        type: 'content',
                        key: `${dynamicData.publicAuthorType}`,
                      },
                      {
                        type: 'content',
                        key: 'editedVersion.dataSecondary.publicAuthorLocalization',
                      },
                    ],
                  },
                  {
                    type: 'column',
                    width: '50%',
                    content: [
                      {
                        type: 'content',
                        key: 'contributors',
                      },
                      {
                        type: 'content',
                        key: 'externalContributors',
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        type: 'row',
        columns: [
          {
            type: 'column',
            title: 'Cikk típusa',
            width: '30%',
            content: [
              {
                type: 'content',
                key: 'status.opinionType',
              },
              {
                type: 'content',
                key: 'editedVersion.dataSecondary.interviewType',
              },
              {
                type: 'content',
                key: 'status.isFastNewsType',
              },
              {
                type: 'content',
                key: 'editedVersion.dataSecondary.videoType',
              },
              {
                type: 'content',
                key: 'editedVersion.dataSecondary.hasGallery',
              },
              {
                type: 'content',
                key: 'status.notebook',
              },
              {
                type: 'content',
                key: 'status.isShortNewsType',
              },
              {
                type: 'row',
                columns: [
                  {
                    type: 'column',
                    width: '100%',
                    showOnlyIf: {
                      printOrOnline: 'online',
                      domains: ['magyarNemzet'],
                    },
                    content: [
                      {
                        type: 'content',
                        key: 'editedVersion.dataSecondary.podcastType',
                      },
                      {
                        type: 'content',
                        key: 'isReviewable',
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            type: 'column',
            title: 'Nyelvesítés',
            width: '30%',
            showOnlyIf: {
              printOrOnline: 'online',
              domains: ['magyarNemzet'],
            },
            content: [
              {
                type: 'content',
                key: 'isTranslated',
              },
            ],
          },
          {
            type: 'column',
            width: '30%',
            paddingTop: '38px',
            showOnlyIf: {
              printOrOnline: 'online',
              domains: ['magyarNemzet'],
            },
            content: [
              {
                type: 'content',
                key: 'minuteToMinute',
              },
              {
                type: 'content',
                key: 'status.minuteToMinuteTimeHidden',
              },
              {
                type: 'content',
                key: 'isFoundationContent',
              },
              {
                type: 'content',
                key: 'status.foundationTagSelect',
              },
            ],
          },
        ],
      },
      {
        type: 'row',
        columns: [
          {
            type: 'column',
            title: 'Podcast tartalom adatai',
            width: '80%',
            content: [
              {
                type: 'content',
                key: 'editedVersion.dataSecondary.podcastGuestAvatar',
              },
              {
                type: 'content',
                key: 'editedVersion.dataSecondary.podcastGuestName',
              },
              {
                type: 'content',
                key: 'editedVersion.dataSecondary.podcastGuestNameTitle',
              },
            ],
          },
        ],
      },
      {
        type: 'row',
        columns: [
          {
            type: 'column',
            title: 'Automata linkelés beállításai',
            width: '30%',
            content: [
              {
                type: 'content',
                key: 'status.foundationTagAutoLink',
              },
              {
                type: 'content',
                key: 'status.glossaryAutoLink',
              },
            ],
          },
        ],
      },
      {
        type: 'row',
        columns: [
          {
            type: 'column',
            title: 'AV kódok',
            width: '80%',
            showOnlyIf: {
              printOrOnline: 'online',
              domains: ['magyarNemzet'],
            },
            content: [
              {
                type: 'content',
                key: 'editedVersion.dataSecondary.avCodes',
              },
              {
                type: 'content',
                key: 'editedVersion.dataSecondary.recommendedArticlesAvCodes',
              },
              {
                type: 'content',
                key: 'editedVersion.dataSecondary.ctLinkCodes',
              },
            ],
          },
        ],
      },
      {
        type: 'row',
        columns: [
          {
            type: 'column',
            title: 'Pr kiemelés beállítások',
            width: '80%',
            content: [
              {
                type: 'content',
                key: 'prTag',
              },
              {
                type: 'content',
                key: 'prActiveFromDate',
              },
              {
                type: 'content',
                key: 'prActiveToDate',
              },
            ],
          },
        ],
      },
      {
        type: 'row',
        columns: [
          {
            type: 'column',
            title: 'Rendkívüli cikk beállítások',
            width: '80%',
            showOnlyIf: { printOrOnline: 'online' },
            content: [
              {
                type: 'content',
                key: 'status.isBreakingNews',
              },
              {
                type: 'content',
                key: 'status.breakingNewsFromDate',
              },
              {
                type: 'content',
                key: 'status.breakingNewsToDate',
              },
            ],
          },
        ],
      },
      {
        type: 'row',
        columns: [
          {
            type: 'column',
            title: 'Kiemelés a szalagba',
            width: '80%',
            showOnlyIf: { printOrOnline: 'online' },
            content: [
              {
                type: 'content',
                key: 'status.isHighlightInTape',
              },
              {
                type: 'content',
                key: 'status.highlightInTapeFromDate',
              },
              {
                type: 'content',
                key: 'status.highlightInTapeToDate',
              },
            ],
          },
        ],
      },
      {
        type: 'row',
        columns: [
          {
            type: 'column',
            title: 'Cikk elrejtése a főoldalon és rovat főoldalakon',
            width: '80%',
            showOnlyIf: { printOrOnline: 'online' },
            content: [
              {
                type: 'content',
                key: 'status.isHiddenOnLayout',
              },
              {
                type: 'content',
                key: 'status.hiddenOnLayoutFrom',
              },
              {
                type: 'content',
                key: 'status.hiddenOnLayoutUntil',
              },
            ],
          },
        ],
      },
      {
        type: 'row',
        columns: [
          {
            type: 'column',
            width: '40%',
            content: [
              {
                type: 'content',
                key: 'regions',
              },
            ],
          },
          {
            type: 'column',
            width: '40%',
            content: [
              {
                type: 'content',
                key: 'sports',
              },
            ],
          },
        ],
      },
      {
        type: 'row',
        columns: [
          {
            type: 'column',
            title: 'Print információk',
            width: '70%',
            showOnlyIf: { printOrOnline: 'print' },
            content: [
              {
                type: 'content',
                key: 'status.printPublishedDate',
              },
              {
                type: 'content',
                key: 'status.printPageNumber',
              },
            ],
          },
        ],
      },
      {
        type: 'row',
        columns: [
          {
            type: 'column',
            title: 'Munkafolyamat',
            width: '70%',
            showOnlyIf: { printOrOnline: 'print' },
            content: [
              {
                type: 'content',
                key: 'status.printStatusReady0',
              },
              {
                type: 'content',
                key: 'status.printStatusEdited1',
              },
              {
                type: 'content',
                key: 'status.printStatusRead2',
              },
              {
                type: 'content',
                key: 'status.printStatusAccepted3',
              },
              {
                type: 'content',
                key: 'status.printStatusFrackedUp4',
              },
              {
                type: 'content',
                key: 'status.printStatusReloaded5',
              },
            ],
          },
        ],
      },
      {
        type: 'row',
        columns: [
          {
            type: 'column',
            title: 'Hálózati küldés',
            width: '100%',
            content: [
              {
                type: 'content',
                key: 'editedVersion.centralSendingPortals',
              },
            ],
          },
        ],
      },
      {
        type: 'row',
        columns: [
          {
            type: 'column',
            title: 'Push értesítések',
            width: '40%',
            showOnlyIf: {
              printOrOnline: 'online',
              domains: ['magyarNemzet'],
            },
            content: [
              {
                type: 'content',
                key: 'fbIsPushNotificationOn',
              },
              {
                type: 'content',
                key: 'fbNotificationTitle',
              },
              {
                type: 'content',
                key: 'fbNotificationBody',
              },
              {
                type: 'content',
                key: 'fbTopic',
              },
              {
                type: 'content',
                key: 'fbIsCustomExpirationOn',
              },
              {
                type: 'content',
                key: 'fbExpiration',
              },
            ],
          },
        ],
      },
      {
        type: 'row',
        columns: [
          {
            type: 'column',
            title: 'Facebook megosztás adatai',
            width: '100%',
            content: [
              {
                type: 'content',
                key: 'editedVersion.dataSecondary.facebookTitle',
              },
              {
                type: 'content',
                key: 'editedVersion.dataSecondary.seoImage',
              },
            ],
          },
        ],
      },
    ],
  };
};
