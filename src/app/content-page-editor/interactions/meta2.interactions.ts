import { Injectable } from '@angular/core';
import { DomainService } from 'src/app/core/modules/admin/services/domain.service';
import { ApiService } from 'src/app/core/services/api.service';
import {
  FormControlInteraction,
  FormControlInteractionDescription,
} from 'src/app/shared/modules/form-generator2/definitions/form-control-interaction.definitions';
import { CheckboxFormControl, GeneratedFormControl } from 'src/app/shared/modules/form-generator2/definitions/form-generator-adapters.definitions';
import { NzModalService } from 'ng-zorro-antd/modal';

const PRINT_FORM_CONTROL_KEYS = [
  'status.printStatusReady0',
  'status.printStatusEdited1',
  'status.printStatusRead2',
  'status.printStatusAccepted3',
  'status.printStatusFrackedUp4',
  'status.printStatusReloaded5',
];

enum MaterialType {
  OWN_MATERIAL = 'own_material',
  NEWS_SERVICE_MATERIAL = 'news_service_material',
  OTHER_MATERIAL = 'other_material',
}

@Injectable()
export class Meta2Interactions implements FormControlInteraction {
  constructor(
    private apiService: ApiService,
    private domainService: DomainService,
    private readonly modal: NzModalService
  ) {}

  isPaywallCheckboxInteractions: FormControlInteractionDescription = {
    formControlNames: 'isPaywalled',
    listeners: [
      {
        dependantFormControlNames: 'isPaywalled',
        triggeredBy: ['value'],
        action: (formControl: GeneratedFormControl, dependantFormControl: GeneratedFormControl, updateFormControl) => {
          const value: boolean = formControl.value as boolean;

          // If the user wants to uncheck the box, we set it back to true and show a confirmation modal.
          if (!value) {
            /**
             * Why there is 3 update on the formControl?
             * The first is done in sync, in order to send the previous value for the autosave.
             * However the checkbox would not react to this change,
             * so we need to set the checkbox to false and then back to true with a small delay,
             * in order to actually make it stay checked also in the view.
             */

            updateFormControl({
              ...dependantFormControl,
              value: true,
            } as GeneratedFormControl);
            setTimeout(() => {
              updateFormControl({
                ...dependantFormControl,
                value: false,
              } as GeneratedFormControl);
            });
            setTimeout(() => {
              updateFormControl({
                ...dependantFormControl,
                value: true,
              } as GeneratedFormControl);
            });
            // Needs to be done with setTimeout in order for the checkbox to be refreshed to it's reverted state.

            this.modal.confirm({
              nzTitle: 'Megerősítés szükséges!',
              nzContent:
                'Biztos, hogy ki szeretné törölni az előfizetéses tartalom jelzést? Ez azt fogja eredményezni, hogy a cikkből is eltűnik a korábban elhelyezett előfizetési határoló.',
              nzOkText: 'Igen',
              nzCancelText: 'Nem',
              nzMaskClosable: true,
              nzOkDanger: true,
              nzOnOk: () =>
                updateFormControl({
                  ...dependantFormControl,
                  value: false,
                } as GeneratedFormControl),
            });
          }
        },
      },
    ],
  };

  authorInteractions: FormControlInteractionDescription = {
    formControlNames: 'status.user',
    listeners: [
      {
        dependantFormControlNames: 'editedVersion.dataSecondary.avatar',
        triggeredBy: ['value'],
        action: (formControl: GeneratedFormControl, dependantFormControl: GeneratedFormControl, updateFormControl) => {
          const value: any = formControl.value;
          this.apiService.getBasicItemFormInfo('user', value.id).subscribe((res) => {
            const userDataArray = res.data as Array<any>;
            const avatarImageIndex = userDataArray.findIndex((item) => item.key === 'avatar');
            if (avatarImageIndex !== -1) {
              updateFormControl({
                ...dependantFormControl,
                value: userDataArray[avatarImageIndex].value,
              });
            }
          });
        },
      },
    ],
  };

  authorPublicInteractions: FormControlInteractionDescription = {
    formControlNames: 'status.user',
    listeners: [
      {
        dependantFormControlNames: 'editedVersion.dataSecondary.publicAuthor',
        triggeredBy: ['value'],
        action: (formControl: GeneratedFormControl, dependantFormControl: GeneratedFormControl, updateFormControl) => {
          const value: any = formControl.value;
          const isMegyei = this.domainService.isCurrentDomainMegyeiLap();
          if (!isMegyei && (dependantFormControl.value === null || dependantFormControl.value === '')) {
            updateFormControl({
              ...dependantFormControl,
              value: value.publicAuthorName,
            });
          }
        },
      },
    ],
  };

  pushNotificationOnInteractions: FormControlInteractionDescription = {
    formControlNames: 'fbIsPushNotificationOn',
    listeners: [
      {
        dependantFormControlNames: ['fbNotificationTitle', 'fbNotificationBody', 'fbTopic'],
        triggeredBy: ['init', 'value'],
        action: (formControl: GeneratedFormControl, dependantFormControl: GeneratedFormControl, updateFormControl) => {
          if (formControl.value === true) {
            updateFormControl({
              ...dependantFormControl,
              asserts: { NotBlank: { allowNull: false, message: 'Ez az érték nem lehet üres' } },
            });
          } else {
            updateFormControl({
              ...dependantFormControl,
              asserts: {},
            });
          }
        },
      },
    ],
  };

  pushNotificationExpirationInteractions: FormControlInteractionDescription = {
    formControlNames: 'fbIsCustomExpirationOn',
    listeners: [
      {
        dependantFormControlNames: 'fbExpiration',
        triggeredBy: ['value'],
        action: (formControl: GeneratedFormControl, dependantFormControl: GeneratedFormControl, updateFormControl) => {
          if (formControl.value === true) {
            updateFormControl({
              ...dependantFormControl,
              asserts: { NotBlank: { allowNull: false, message: 'Ez az érték nem lehet üres' } },
            });
          } else {
            updateFormControl({
              ...dependantFormControl,
              asserts: {},
            });
          }
        },
      },
    ],
  };

  materialTypeInteractions: FormControlInteractionDescription = {
    formControlNames: 'status.materialType',
    listeners: [
      {
        dependantFormControlNames: 'status.tourGuide',
        triggeredBy: ['init', 'value'],
        action: (formControl: GeneratedFormControl, dependantFormControl: GeneratedFormControl, updateFormControl) => {
          if (formControl.value?.['id'] === MaterialType.NEWS_SERVICE_MATERIAL) {
            updateFormControl({
              ...dependantFormControl,
              asserts: { NotBlank: { allowNull: false, message: 'Ez az érték nem lehet üres' } },
              isHidden: false,
            });
          } else {
            updateFormControl({
              ...dependantFormControl,
              asserts: {},
              isHidden: true,
              value: null,
            });
          }
        },
      },
      {
        dependantFormControlNames: 'contributors',
        triggeredBy: ['init', 'value'],
        action: (formControl: GeneratedFormControl, dependantFormControl: GeneratedFormControl, updateFormControl) => {
          if (formControl.value?.['id'] === MaterialType.NEWS_SERVICE_MATERIAL || formControl.value?.['id'] === MaterialType.OWN_MATERIAL) {
            updateFormControl({
              ...dependantFormControl,
              asserts: { NotBlank: { allowNull: false, message: 'Ez az érték nem lehet üres' } },
            });
          } else {
            updateFormControl({
              ...dependantFormControl,
              asserts: {},
            });
          }
        },
      },
      {
        dependantFormControlNames: 'contributors',
        triggeredBy: ['value'],
        action: (formControl: GeneratedFormControl, dependantFormControl: GeneratedFormControl, updateFormControl) => {
          if (formControl.value?.['id'] !== MaterialType.NEWS_SERVICE_MATERIAL && formControl.value?.['id'] !== MaterialType.OWN_MATERIAL) {
            updateFormControl({
              ...dependantFormControl,
              value: [],
            } as GeneratedFormControl);
          }
        },
      },
      {
        dependantFormControlNames: 'editedVersion.dataSecondary.publicAuthor',
        triggeredBy: ['init', 'value'],
        action: (formControl: GeneratedFormControl, dependantFormControl: GeneratedFormControl, updateFormControl) => {
          if (formControl.value?.['id'] === MaterialType.OWN_MATERIAL) {
            updateFormControl({
              ...dependantFormControl,
              asserts: { NotBlank: { allowNull: false, message: 'Ez az érték nem lehet üres' } },
            });
          } else {
            updateFormControl({
              ...dependantFormControl,
              asserts: {},
            });
          }
        },
      },
    ],
  };

  protectedEditorInteractions: FormControlInteractionDescription = {
    formControlNames: 'isProtected',
    listeners: [
      {
        dependantFormControlNames: 'protectedEditor',
        triggeredBy: ['init', 'value'],
        action: (formControl: GeneratedFormControl, dependantFormControl: GeneratedFormControl, updateFormControl) => {
          updateFormControl({
            ...dependantFormControl,
            isHidden: !formControl.value,
          });
        },
      },
    ],
  };

  leadVideoInteractions: FormControlInteractionDescription = {
    formControlNames: 'editedVersion.dataSecondary.leadVideo',
    listeners: [
      {
        dependantFormControlNames: 'editedVersion.dataSecondary.leadVideoPosition',
        triggeredBy: ['init', 'value'],
        action: (formControl: GeneratedFormControl, dependantFormControl: GeneratedFormControl, updateFormControl) => {
          updateFormControl({
            ...dependantFormControl,
            asserts: formControl.value ? { NotBlank: { allowNull: false, message: 'Ez az érték nem lehet üres' } } : {},
            isHidden: dependantFormControl.isHidden || !formControl.value,
            value: dependantFormControl.value.value ? dependantFormControl.value : { label: 'Cím felett', value: 'above_title' },
          });
        },
      },
    ],
  };

  podcastGuestInteractions: FormControlInteractionDescription = {
    formControlNames: 'editedVersion.dataSecondary.podcastType',
    listeners: [
      {
        dependantFormControlNames: [
          'editedVersion.dataSecondary.podcastGuestName',
          'editedVersion.dataSecondary.podcastGuestNameTitle',
          'editedVersion.dataSecondary.podcastGuestAvatar',
        ],
        triggeredBy: ['init', 'value'],
        action: (formControl: GeneratedFormControl, dependantFormControl: GeneratedFormControl, updateFormControl) => {
          updateFormControl({
            ...dependantFormControl,
            isHidden: !formControl.value,
            asserts: {
              ...dependantFormControl.asserts,
              ...(dependantFormControl.key !== 'editedVersion.dataSecondary.podcastGuestAvatar'
                ? { NotBlank: { allowNull: false, message: 'Ez az érték nem lehet üres' } }
                : {}),
            },
          });
        },
      },
    ],
  };

  podcastArticleInteractions: FormControlInteractionDescription = {
    formControlNames: 'editedVersion.dataSecondary.podcastType',
    listeners: [
      {
        dependantFormControlNames: 'editedVersion.dataSecondary.podcastGuestName',
        triggeredBy: ['init', 'value'],
        action: (formControl: GeneratedFormControl, dependantFormControl: GeneratedFormControl, updateFormControl) => {
          updateFormControl({
            ...dependantFormControl,
            asserts: formControl.value ? { NotBlank: { allowNull: false, message: 'Ez az érték nem lehet üres' } } : {},
            isHidden: formControl.value ? false : true,
          });
        },
      },
      {
        dependantFormControlNames: 'editedVersion.dataSecondary.podcastGuestNameTitle',
        triggeredBy: ['init', 'value'],
        action: (formControl: GeneratedFormControl, dependantFormControl: GeneratedFormControl, updateFormControl) => {
          updateFormControl({
            ...dependantFormControl,
            asserts: formControl.value ? { NotBlank: { allowNull: false, message: 'Ez az érték nem lehet üres' } } : {},
            isHidden: formControl.value ? false : true,
          });
        },
      },
      {
        dependantFormControlNames: 'editedVersion.dataSecondary.podcastGuestAvatar',
        triggeredBy: ['init', 'value'],
        action: (formControl: GeneratedFormControl, dependantFormControl: GeneratedFormControl, updateFormControl) => {
          updateFormControl({
            ...dependantFormControl,
            isHidden: !formControl.value,
          });
        },
      },
    ],
  };

  withoutAdsInteractions: FormControlInteractionDescription = {
    formControlNames: 'editedVersion.dataSecondary.withoutAds',
    listeners: [
      {
        dependantFormControlNames: 'editedVersion.dataSecondary.isExceptionAdvertEnabled',
        triggeredBy: ['init', 'value'],
        action: (formControl: GeneratedFormControl, dependantFormControl: GeneratedFormControl, updateFormControl) => {
          updateFormControl({
            ...dependantFormControl,
            isDisabled: !formControl.value,
            value: !formControl.value ? formControl.value : dependantFormControl.value,
          });
        },
      },
    ],
  };

  interactions: FormControlInteractionDescription[] = [
    this.isPaywallCheckboxInteractions,
    this.authorInteractions,
    this.authorPublicInteractions,
    this.pushNotificationOnInteractions,
    this.pushNotificationExpirationInteractions,
    this.materialTypeInteractions,
    this.protectedEditorInteractions,
    this.leadVideoInteractions,
    this.podcastGuestInteractions,
    this.podcastArticleInteractions,
    this.withoutAdsInteractions,
    ...this.generatePrintStatusInteractions(),
  ];

  private generatePrintStatusInteractions(): FormControlInteractionDescription[] {
    const printStatusInteractions: FormControlInteractionDescription[] = PRINT_FORM_CONTROL_KEYS.map((formControlName, index) => ({
      formControlNames: formControlName,
      listeners: [
        {
          dependantFormControlNames: this.getPrecedingPrintStatusFormControlNames(index),
          triggeredBy: ['init'],
          action: (formControl: CheckboxFormControl, dependantFormControl: CheckboxFormControl, updateFormControl) => {
            const isChecked = formControl.value;
            const isDisabled = formControl.isDisabled;
            if (isChecked && isDisabled) {
              updateFormControl({
                ...dependantFormControl,
                isDisabled: true,
              });
            }
          },
        },
        {
          dependantFormControlNames: this.getPrecedingPrintStatusFormControlNames(index),
          triggeredBy: ['value'],
          action: (formControl: CheckboxFormControl, dependantFormControl: CheckboxFormControl, updateFormControl) => {
            const isChecked = formControl.value;
            if (isChecked) {
              updateFormControl({
                ...dependantFormControl,
                value: true,
              });
            }
          },
        },
        {
          dependantFormControlNames: this.getFollowingPrintStatusFormControlNames(index),
          triggeredBy: ['value'],
          action: (formControl: CheckboxFormControl, dependantFormControl: CheckboxFormControl, updateFormControl) => {
            const isChecked = formControl.value;
            if (!isChecked) {
              updateFormControl({
                ...dependantFormControl,
                value: false,
                isDisabled: true,
              });
            } else {
              const nextFormControlName = index < PRINT_FORM_CONTROL_KEYS.length - 1 ? PRINT_FORM_CONTROL_KEYS[index + 1] : null;
              if (nextFormControlName && nextFormControlName === dependantFormControl.key && !dependantFormControl.isDisabled) {
                updateFormControl({
                  ...dependantFormControl,
                  isDisabled: false,
                });
              } else {
                updateFormControl({
                  ...dependantFormControl,
                  isDisabled: true,
                });
              }
            }
          },
        },
      ],
    }));

    return printStatusInteractions;
  }

  private getPrecedingPrintStatusFormControlNames(index: number): string[] {
    return PRINT_FORM_CONTROL_KEYS.filter((_, i) => i < index);
  }

  private getFollowingPrintStatusFormControlNames(index: number): string[] {
    return PRINT_FORM_CONTROL_KEYS.filter((_, i) => i > index);
  }
}
