import { HttpErrorResponse } from '@angular/common/http';
import { AfterViewInit, ChangeDetectorRef, Component, DestroyRef, inject, OnDestroy, OnInit, ViewChild, ViewContainerRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FullscreenLoaderService } from '@external/loading';
import { StorageService, UtilsService } from '@external/utils';
import { TranslateService } from '@ngx-translate/core';
import { DateFormatPipe } from '@shared/pipes/date-format.pipe';
import { UserAwayObserverService } from '@shared/services/user-away-observer.service';
import { ContentType } from '@trendency/kesma-ui';
import { find } from 'lodash';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalRef, NzModalService, NzModalState } from 'ng-zorro-antd/modal';
import { NzTabSetComponent } from 'ng-zorro-antd/tabs';
import { BehaviorSubject, fromEvent, interval, Observable, of, partition, Subject, take, throwError, timer } from 'rxjs';
import {
  catchError,
  concatMap,
  debounce,
  debounceTime,
  distinctUntilChanged,
  filter,
  finalize,
  map,
  startWith,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs/operators';
import { AvailableComponentContent, BaseContentVariant, BodyComponentContent } from 'src/app/core/api.definitons';
import { ArticleMediaType, MinuteToMinuteState } from 'src/app/core/definitions/article.definitions';
import { DomainKey } from 'src/app/core/modules/admin/admin.definitions';
import { DomainService } from 'src/app/core/modules/admin/services/domain.service';
import { LogoutService } from 'src/app/core/modules/admin/services/logout.service';
import { ApiService } from 'src/app/core/services/api.service';
import { ISeoScoreResult } from 'src/app/modules/seo-score/definitions/seo-score-result';
import { SeoScoreService } from 'src/app/modules/seo-score/services/seo-score.service';
import { TagGeneratorTag } from 'src/app/modules/tag-generator/definitions/tag-generator-definitions';
import { ContentData, FormLayout } from 'src/app/shared/modules/form-generator/form-generator.definitions';
import { FormGeneratorBodySingleComponent } from 'src/app/shared/modules/form-generator2/components/form-generator-body-single/form-generator-body-single.component';
import { FormGeneratorBodyComponent } from 'src/app/shared/modules/form-generator2/components/form-generator-body/form-generator-body.component';
import { FormGeneratorComponent } from 'src/app/shared/modules/form-generator2/components/form-generator/form-generator.component';
import { FormControlMapOptions, FormGeneratorBodyAutoSave } from 'src/app/shared/modules/form-generator2/definitions/form-generator.definitions';
import { DestroyService } from 'src/app/shared/services/destroy.service';
import { ForceUnlockService } from 'src/app/shared/services/force-unlock.service';
import { PortalConfigService } from 'src/app/shared/services/portal-config.service';
import { SharedService } from 'src/app/shared/services/shared.service';
import { TagGeneratorService } from '../../../modules/tag-generator/services/tag-generator.service';
import { Version } from '../../../modules/versions/versions.definitions';
import { PortalConfigSetting } from '../../../shared/definitions/portal-config';
import { List } from '../../../shared/definitions/shared.definitions';
import {
  BackendFormalizerBody,
  BackendFormalizerComponent,
  BackendFormalizerData,
} from '../../../shared/modules/form-generator2/definitions/backend.definitions';
import { CounterInfo, GeneratedFormControl, TextFormControl } from '../../../shared/modules/form-generator2/definitions/form-generator-adapters.definitions';
import { CHARACTER_COUNT_PER_ROW } from '../../../shared/modules/form-generator2/services/form-generator-body.service';
import { VersionListComponent } from '../../../shared/modules/version-list/version-list.component';
import { getCharacterCount, getWordCount } from '../../../shared/utils/text-utils';
import { ArticleContentDataFragments, SaveType } from '../../definitions/article-editor.definitions';
import { AutoSaveState, PreviewData } from '../../definitions/content-page-editor.definitions';
import { MinuteToMinuteForceUnlock } from '../../definitions/minute-to-minute.definitions';

import { FoundationTagService } from '@shared/services/foundation-tag.service';
import { SeoCardComponent } from '../../../modules/seo-score/components/seo-card/seo-card.component';
import { SeoTypeEnum } from '../../../modules/seo-score/enums/seo-type.enum';
import { articleEditorMetaLayout } from '../../layouts/article-editor-meta.layout';
import { titlesLayout } from '../../layouts/titles.layout';
import { ContentDataFragentsModel } from '../../models/content-data-fragments.model';
import { ArticleEditorService, getAutoSaveState } from '../../services/article-editor.service';
import { PageEditorService } from '../../services/page-editor.service';
import { AwayNoticeComponent } from '../away-notice/away-notice.component';
import { format } from 'date-fns';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

type BaseContentVariantWithComponents = BaseContentVariant & {
  availableComponents: AvailableComponentContent[];
};

const LOCK_EDIT_INTERVAL_MS = 5 * 60 * 1000;
const CONTENT_TYPE = 'article';
// field(s) to summarize char/word/row counts for passing down to article editor
const SUMMARY_FIELDS_ONLINE = ['editedVersion.dataSecondary.lead', 'editedVersion.dataPrimary.title'];
const SUMMARY_FIELDS_PRINT = ['editedVersion.dataSecondary.lead', 'editedVersion.dataSecondary.printTitle'];

const SUMMARY_TITLE_ONLINE = 'editedVersion.dataPrimary.title';
const SUMMARY_TITLE_PRINT = 'editedVersion.dataSecondary.printTitle';
const SUMMARY_LEAD = 'editedVersion.dataSecondary.lead';

@Component({
  selector: 'app-article-editor',
  templateUrl: 'article-editor.component.html',
  styleUrls: ['./article-editor.component.scss'],
  providers: [
    DestroyService,
    ContentDataFragentsModel,
    UserAwayObserverService,
    {
      // Specify the timeout for the away state.
      provide: 'AWAY_TIMEOUT',
      useValue: 900,
    },
  ],
  standalone: false,
})
export class ArticleEditorComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('tabSet') private tabSet: NzTabSetComponent;
  @ViewChild('titlesFormGenerator')
  private titlesFormGenerator: FormGeneratorComponent;
  @ViewChild('bodyFormGenerator')
  private bodyFormGenerator: FormGeneratorBodyComponent;
  @ViewChild('bodySingleFormGenerator')
  private bodySingleFormGenerator: FormGeneratorBodySingleComponent;
  @ViewChild('metaFormGenerator')
  private metaFormGenerator: FormGeneratorComponent;
  @ViewChild('seoCard')
  private seoCard: SeoCardComponent;
  @ViewChild('versionListComponent', { static: false })
  versionListComponent: VersionListComponent;

  private saveSubject: Subject<SaveType> = new Subject<SaveType>();
  private contentData: ContentData;
  private contentDataSubject: Subject<ContentData> = new Subject<ContentData>();

  public id: string;
  public saveLoading: boolean;
  public activateLoading: boolean;
  public inactivateLoading: boolean;
  public title: string;
  public editLockDescription: string;
  public autoSaveState: AutoSaveState;
  public previewData: PreviewData;
  public mediaType: ArticleMediaType;
  public hasUnsavedChanges: boolean;
  public isActive: boolean;
  public isFormDisabled: boolean;
  public isLockedByOtherUser: boolean;
  public minuteToMinuteState: MinuteToMinuteState = 'not';
  public isReviewable: boolean = false;
  public save$: Observable<SaveType> = this.saveSubject.asObservable();
  public contentData$: Observable<ContentData> = this.contentDataSubject.asObservable();
  public articleVersions: List<Version>;
  public availableComponents: AvailableComponentContent[];
  public metaLayout: FormLayout;
  public titlesLayout: FormLayout = titlesLayout;
  public showActionButtons: boolean;
  public canActivateArticle: boolean;
  public canActivateNewVersion: boolean;
  public canInactivateArticle: boolean;
  public canCreatePrint: boolean;
  public createPrintArticle: boolean;
  public canCreateOnline: boolean;
  public selectedArticleMediaType: ArticleMediaType;
  public updatedBy: string;
  public updatedAt: Date;
  public createdAt: Date;
  public domainKey: DomainKey;
  public isMegyeiLap: boolean;
  public isMandiner: boolean;
  private isMetropol: boolean;
  private isKoponyeg: boolean;
  private isNSO: boolean;
  private isBors: boolean;
  private isRipost: boolean;
  private isShe: boolean;
  private isLife: boolean;
  private isVilaggazdasag: boolean;
  private isMindmegette: boolean;
  private isOrigo: boolean;
  private isPestiSracok: boolean;
  public formControlMapOptions: FormControlMapOptions = {
    printOrOnline: 'online',
  };
  public leadCounterInfo: CounterInfo;
  public charCountLead: number; // Cikk leadje
  public charCountTitle: number; // Cikk címe
  public charCountUpTitle: number; // Felcím
  public charCountMainTitle: number; // Főcím
  public charCountPreTitle: number; // Elő cím
  public charCountBody: number; // Cikk törzs
  public charCountSubTitle: number; // Alcím
  public charCountShortLead: number; // Rövid lead
  public charCountRecommendedTitle: number; // Ajánlócím

  public isUnlockedAlready: boolean;
  public bodyContentData: ContentData;
  public saveButtonDisabled: boolean;

  public readonly seoCheckResult$ = new BehaviorSubject<ISeoScoreResult | null>(null);

  public seoScoreValue: number = 0;
  private isSeoFirstCalculation: boolean = true;

  public publicUrl: Observable<string>;
  public draftUrl: Observable<string>;

  public articlePublishDate$: Observable<Date>;
  public selectedEditorTabIndex: number = 0;
  public hasInvalidTags: boolean = false;

  public calculateFlekk?: number;

  private addLeadToStatistics = false;
  private addTitleToStatistics = false;
  private shouldNotSave = false;
  private lastAutoSavedArticleBody: BaseContentVariant;
  private isTagGeneratorLoading = false;
  private dynamicTagsEnabled = false;
  addImageSubToStatistics = false;
  public isPageRedirecting = false;
  private tagGeneratorError: HttpErrorResponse;
  private tagGeneratorData: TagGeneratorTag[] = [];
  private readonly dateFormatPipe = new DateFormatPipe();
  private seoTitle?: string;
  private falseLinkInContentData: string | null;

  private readonly destroyRef = inject(DestroyRef);

  constructor(
    private readonly forceUnlockService: ForceUnlockService,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly articleEditorService: ArticleEditorService,
    private readonly pageEditorService: PageEditorService,
    private readonly destroy$: DestroyService,
    private readonly apiService: ApiService,
    private readonly domainService: DomainService,
    private readonly portalConfigService: PortalConfigService,
    private readonly logoutService: LogoutService,
    private readonly storageService: StorageService,
    private readonly sharedService: SharedService,
    private readonly seoScoreService: SeoScoreService,
    private readonly message: NzMessageService,
    private readonly translate: TranslateService,
    private readonly awayObserverService: UserAwayObserverService,
    private readonly modal: NzModalService,
    private viewContainerRef: ViewContainerRef,
    public contentDataFragmentsModel: ContentDataFragentsModel<ArticleContentDataFragments>,
    private loaderService: FullscreenLoaderService,
    private tagGeneratorService: TagGeneratorService,
    private readonly foundationTagService: FoundationTagService,
    private readonly utils: UtilsService,
    private readonly cdr: ChangeDetectorRef
  ) {
    this.saveButtonDisabled = false;
  }

  awayModalRef?: NzModalRef<AwayNoticeComponent, any>;
  seoType: SeoTypeEnum = SeoTypeEnum.ARTICLE;

  ngOnInit() {
    fromEvent(window, 'beforeunload')
      .pipe(
        take(1),
        takeUntilDestroyed(this.destroyRef),
        switchMap(() => this.unlockContent())
      )
      .subscribe();
    this.foundationTagService.init();
    this.dynamicTagsEnabled = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_DYNAMIC_CONTENT_TAGGER);
    const isExternalAuthor = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_EXTERNAL_PUBLIC_AUTHOR_M2M);
    const dynamicLayoutMeta = isExternalAuthor
      ? {
          publicAuthorType: 'publicAuthorM2M',
        }
      : undefined;

    this.metaLayout = articleEditorMetaLayout(dynamicLayoutMeta);

    if (this.dynamicTagsEnabled) {
      this.tagGeneratorService.error.pipe(takeUntil(this.destroy$)).subscribe((value) => {
        this.tagGeneratorError = value;
        if (this.tagGeneratorError) {
          this.hasInvalidTags = false;
        }
      });
      this.tagGeneratorService
        .getData()
        .pipe(takeUntil(this.destroy$))
        .subscribe((data) => (this.tagGeneratorData = data || []));
    }
    this.loaderService.showLoader.pipe(takeUntil(this.destroy$)).subscribe((loaderState) => (this.isPageRedirecting = loaderState));
    this.loaderService.hide();

    // User away timeout => leave editor if the user is away for x minutes.
    /**
     * Leave the editor after the notice modal has been opened for x seconds.
     */
    const closeTimeout = 20;

    this.awayObserverService.isAway$.pipe(takeUntil(this.destroy$), distinctUntilChanged()).subscribe((isAway) => {
      // If the user is away we should display a modal with a countdown.
      // If the cooldown reaches 0, we unlock the article and leave the page.
      if (isAway) {
        this.awayModalRef = this.modal.create({
          nzModalType: 'default',
          nzClosable: false,
          nzContent: AwayNoticeComponent,
          nzViewContainerRef: this.viewContainerRef,
          nzData: { countdownTime: closeTimeout },
          nzFooter: [
            {
              label: 'Bezárás',
            },
          ],
        });

        // If the countdown is over and the modal is still open, the user is still away
        // We can unlock the article and leave the editor.
        setTimeout(() => {
          if (this.awayModalRef.state === NzModalState.OPEN) {
            this.closeForAwayState();
          }
        }, closeTimeout * 1000);
      } else {
        // When the user is not away, try to close the modal if it is still open.
        this.awayModalRef?.close();
      }
    });

    this.route.params.subscribe((params) => {
      this.domainKey = this.domainService.currentDomain.key;
      this.isMegyeiLap = this.domainService.isCurrentDomainMegyeiLap();
      this.isMetropol = this.domainService.isCurrent('metropol');
      this.isKoponyeg = this.domainService.isCurrent('koponyeg');
      this.isMandiner = this.domainService.isCurrent('mandiner');
      this.isBors = this.domainService.isCurrent('bors');
      this.isRipost = this.domainService.isCurrent('ripost');
      this.isShe = this.domainService.isCurrent('she');
      this.isLife = this.domainService.isCurrent('life');
      this.isVilaggazdasag = this.domainService.isCurrent('vilaggazdasag');
      this.isNSO = this.domainService.isCurrent('nso');
      this.isMindmegette = this.domainService.isCurrent('mindmegette');
      this.isOrigo = this.domainService.isCurrent('origo');
      this.isPestiSracok = this.domainService.isCurrent('pesti_sracok');
      const routeSnapshot = this.route.snapshot;
      const responseData = routeSnapshot.data.data;
      this.contentData = responseData.contentData;
      this.id = params.id !== 'new' ? params.id : null;
      const path = routeSnapshot.root.firstChild.firstChild.routeConfig.path;
      this.previewData = responseData.previewData?.data;
      this.articleVersions = responseData.articleVersions;

      this.updatePropertiesFromContentData(this.contentData);
      this.redirectToCreateArticlePage();

      this.title = this.articleEditorService.getTitle(path, !!this.id);
      this.autoSaveState = getAutoSaveState('success');
      this.seoTitle = this.contentData.data.find((e) => e.key === 'editedVersion.dataSecondary.seoTitle').value;
      this.availableComponents = this.articleEditorService.getAvailableComponents(this.contentData);

      this.pageEditorService.contentData = this.contentData;
      this.articleEditorService.scrollToPositionAfterInitialSave();
      this.setupSaveListener();
      this.addLeadToStatistics = this.portalConfigService.isConfigSet(PortalConfigSetting.CONTENT_ARTICLE_CHARACTER_STATISTICS_MUST_CONTAIN_LEAD);
      this.addTitleToStatistics = this.portalConfigService.isConfigSet(PortalConfigSetting.CONTENT_ARTICLE_CHARACTER_STATISTICS_MUST_CONTAIN_TITLE);
      this.addImageSubToStatistics = this.portalConfigService.isConfigSet(
        this.mediaType === 'online'
          ? PortalConfigSetting.CONTENT_ARTICLE_ONLINE_CHAR_COUNT_IMAGE_SUB
          : PortalConfigSetting.CONTENT_ARTICLE_PRINT_CHAR_COUNT_IMAGE_SUB
      );

      this.articlePublishDate$ = this.contentDataFragmentsModel.value$.pipe(
        map((object) => object.meta.data),
        map((data) => data.find((article) => article.key === 'publishDate').value)
      );

      this.calculateFlekk = this.contentData?.meta?.flekk?.flekk;

      this.publicUrl = this.contentData$.pipe(
        startWith(this.contentData),
        map((data) => this.getBackendPublicUrl(data))
      );

      if (this.dynamicTagsEnabled) {
        this.tagGeneratorService.isLoadingSubject.pipe(takeUntil(this.destroy$)).subscribe((isLoading) => {
          this.isTagGeneratorLoading = isLoading;
        });

        this.tagGeneratorService.updateRequest(
          this.getTitleContentValueString(this.contentData),
          this.getLeadContentValueString(this.contentData),
          this.getBodyContentValueString(this.contentData),
          ContentType.ARTICLE,
          this.contentData.meta.id
        );
      }

      this.draftUrl = this.contentDataSubject.pipe(
        startWith(this.contentData),
        map((data) => this.makePublicUrl(data))
      );
      this.cdr.detectChanges();
    });

    this.logoutService.loggingOutStarted$.pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.logoutService.startPreLogoutAction();
      this.unlockContent().subscribe(() => {
        this.logoutService.completePreLogoutAction();
        this.isUnlockedAlready = true;
      });
    });
  }

  ngAfterViewInit() {
    if (this.isPaywalled) {
      this.bodySingleFormGenerator.enablePaywall();
    }

    this.metaFormGenerator?.formControlsChanged
      .pipe(
        debounceTime(400),
        map((controls: GeneratedFormControl[]) => controls.find((control) => control.key === 'isPaywalled')?.value),
        filter((value) => value !== undefined && value !== null),
        map((value) => !!value),
        distinctUntilChanged()
      )
      .subscribe((isPaywalled: boolean) => {
        if (isPaywalled) {
          this.bodySingleFormGenerator.enablePaywall();
        } else {
          this.bodySingleFormGenerator.disablePaywall();
          this.bodySingleFormGenerator.removePaywall();
        }
      });

    if (this.domainKey === 'mandiner') {
      this.metaFormGenerator?.formControlsChanged
        .pipe(
          debounceTime(400),
          map((controls: GeneratedFormControl[]) => controls.find((control) => control.key === 'publishDate')?.value),
          filter((value) => value !== undefined && value !== null),
          distinctUntilChanged(),
          map((value) => new Date(value.toString()))
        )
        .subscribe((publishDate: Date) => {
          if (publishDate > new Date()) {
            this.sharedService.showNotification('warning', 'CMS.futurePublicDateWarning', 10000);
          }
        });
    }

    this.initSeoCalculation();
  }

  initSeoCalculation() {
    if (this.isSeoFirstCalculation) {
      //Needs to wait for CKEditor to refresh the ContentData in order to have correct body char count.
      setTimeout(() => {
        this.calculateSeoScore();
      });
    }
  }

  getBackendPublicUrl(contentData: ContentData): string | null {
    const isUndated = this.domainService.isMindmegette();
    let url = contentData.meta?.publicUrl;

    if (!url) {
      return null;
    }

    if (isUndated) {
      // [0] empty, [1] columnSlug, [2] year, [3] month, [4] slug
      const publicUrlArray = url?.split('/');
      url = `/${publicUrlArray?.[1]}/${publicUrlArray?.[4]}`;
    }

    const siteUrl = this.domainService.currentDomain?.info.previewUrl;
    return `${siteUrl}${url}`;
  }

  makePublicUrl(contentData: ContentData): string | null {
    const data = contentData.data;
    const dateValue = data?.find((item) => item.key === 'publishDate')?.value;
    if (!dateValue) {
      return null;
    }
    const publishDate = new Date(dateValue);
    const year = publishDate.getFullYear();
    const month = format(publishDate, 'MM');
    const slug = data?.find((item) => item.key === 'slug')?.value;
    const primaryColumn = data?.find((item) => item.key === 'primaryColumn')?.value?.slug;

    if (!Boolean(slug) || !Boolean(year) || !Boolean(month)) {
      return null;
    }

    const urlParts = [...(primaryColumn ? [primaryColumn] : []), ...(year ? [year] : []), ...(month ? [month] : []), ...(slug ? [slug] : [])];

    const siteUrl = this.domainService.currentDomain?.info.previewUrl;
    return `${siteUrl}/${urlParts.join('/')}`;
  }

  copyToClipboard(text: string): void {
    const el = document.createElement('textarea');
    el.value = text;
    document.body.appendChild(el);
    el.select();
    document.execCommand('copy');
    document.body.removeChild(el);
    this.message.info(this.translate.instant('CMS.copied'));
  }

  ngOnDestroy() {
    if (this.isUnlockedAlready) {
      return;
    }

    this.unlockContent().subscribe();
    this.seoCheckResult$.complete();
  }

  get isPaywalled() {
    return !!this.metaFormGenerator?.formControls.find((control) => control.key === 'isPaywalled')?.value;
  }

  onGalleryAdded(added: boolean) {
    if (!added) {
      return;
    }

    const galleryCheckboxControlIndex =
      this.metaFormGenerator.formControls.findIndex((control) => control.key === 'editedVersion.dataSecondary.hasGallery') || -1;

    const galleryCheckboxControl = this.metaFormGenerator.formControls[galleryCheckboxControlIndex];

    if (galleryCheckboxControl?.value === false) {
      this.sharedService.showNotification('info', 'CMS.gallery-content-turned-on');
      const metaControls = this.metaFormGenerator.formControls;
      const newGalleryCheckboxControl = {
        ...galleryCheckboxControl,
        ...{ value: true },
      };
      this.metaFormGenerator.formControls = [
        ...metaControls.slice(0, galleryCheckboxControlIndex),
        newGalleryCheckboxControl,
        ...metaControls.slice(galleryCheckboxControlIndex + 1),
      ] as TextFormControl<any>[];
    }
  }

  onVideoAdded(added: any) {
    if (!added) {
      return;
    }
    let videoCheckboxControl = null;
    let videoCheckboxControlIndex = -1;
    for (let i = 0; i < this.metaFormGenerator.formControls.length; i++) {
      if (this.metaFormGenerator.formControls[i].key === 'editedVersion.dataSecondary.videoType') {
        videoCheckboxControl = this.metaFormGenerator.formControls[i];
        videoCheckboxControlIndex = i;
      }
    }

    if (videoCheckboxControl?.value === false) {
      this.sharedService.showNotification('info', 'CMS.video-content-turned-on');
      const metaControls = this.metaFormGenerator.formControls;
      const newVideoCheckboxControl = {
        ...videoCheckboxControl,
        ...{ value: true },
      };
      this.metaFormGenerator.formControls = [
        ...metaControls.slice(0, videoCheckboxControlIndex),
        newVideoCheckboxControl,
        ...metaControls.slice(videoCheckboxControlIndex + 1),
      ];
    }
  }

  onCreateArticle() {
    this.saveLoading = true;
    this.contentData.data.find((e) => e.key === 'publishDate').value = this.dateFormatPipe.transform(new Date(), 'utc');
    const printOrOnlineBody = this.contentDataFragmentsModel.value.printOrOnline.data;
    const body: BodyComponentContent[] = this.contentDataFragmentsModel.value.body.data as BodyComponentContent[];
    const wysiwygComponent = this.articleEditorService.getWysiwygComponent(body);
    if (wysiwygComponent) {
      body[0].value = [wysiwygComponent];
    }
    printOrOnlineBody[0].value = this.selectedArticleMediaType === 'print';
    let contentData = this.articleEditorService.setContentDataFragment(printOrOnlineBody, this.contentData);
    contentData = this.articleEditorService.setContentDataFragment(body, contentData);
    this.createContent(contentData);
  }

  onActivate() {
    if (this.mediaType === 'online' && this.domainService.isMegyeiLap(this.domainService.currentDomain.key)) {
      const waitingForPublishWorkFlow = find(this.contentDataFragmentsModel.value.meta.data, { key: 'status.printStatusRead2' });
      if (!waitingForPublishWorkFlow?.value) {
        this.sharedService.showNotification('error', `Verzió aktiválásához a következő munkafolyamat szükséges: Publikálásra vár`);
        return;
      }
    }

    if (this.dynamicTagsEnabled && !this.isTagGeneratorLoading) {
      // TODO change this block to check for generated tags on the article when generated tag separation is implemented by backend
      this.contentDataFragmentsModel.value = this.updateContentDataFragments();
      const contentData = this.joinContentDataFragments();
      this.validateGeneratedTags(contentData);
      if (!this.hasInvalidTags) {
        this.toggleActivate(true);
      } else {
        this.sharedService.showNotification('error', 'Legalább 3 generált címkét használjon!');
      }
    } else {
      this.toggleActivate(true);
    }
  }

  onInactivate() {
    this.toggleActivate(false);
  }

  onSave() {
    if (this.portalConfigService.isConfigSet(PortalConfigSetting.SLUG_GENERATION_FROM_SEO_TITLE_FOR_ARTICLES)) {
      const currentSlug = this.contentDataFragmentsModel.value.titles.data.find((input) => input.key === 'slug')?.value;
      if (
        (!this.seoTitle || this.seoTitle?.length === 0) &&
        (!currentSlug || currentSlug.length === 0) &&
        this.formControlMapOptions.publicState === 'draft' &&
        this.mediaType === 'online'
      ) {
        this.message.error('SEO mező kitöltése szükséges az URL generáláshoz.');
      }
    }
    this.saveSubject.next('save');
  }

  onAutoSave(contentData: ContentData, contentDataFragmentKey: keyof ArticleContentDataFragments, shouldValidateTags = true) {
    this.minuteToMinuteState = this.articleEditorService.getMinuteToMinuteState(contentData.data, !!this.id) ?? this.minuteToMinuteState;

    this.isReviewable = this.articleEditorService.getIsReviewable(contentData.data, !!this.id);

    if (shouldValidateTags && this.dynamicTagsEnabled) {
      this.validateGeneratedTags(contentData);
    }

    if (this.autoSaveState.state === 'off') {
      return;
    }

    this.contentDataFragmentsModel.value = {
      ...this.contentDataFragmentsModel.value,
      [contentDataFragmentKey]: contentData,
    };
    if (this.domainService.isCurrentDomainMegyeiLap() || this.domainService.isOrigo()) {
      this.saveButtonDisabled = true;
    }
    this.saveSubject.next('autosave');
  }

  onBodyAutoSave(autoSaveEvent: FormGeneratorBodyAutoSave) {
    if (this.shouldNotSave) {
      this.shouldNotSave = false;
      return;
    }

    const { updatedContentData } = autoSaveEvent;
    if (isContentEmpty(autoSaveEvent.updatedContentData?.data)) {
      console.warn(' ❌ refusing autosave: empty content!');
      return;
    }
    this.contentDataFragmentsModel.value = {
      ...this.contentDataFragmentsModel.value,
      body: updatedContentData,
    };

    // this.autoSave(true);
    this.saveSubject.next('autosave');
  }

  public onBodyValueChange(event: boolean): void {
    this.initSeoCalculation();
    this.saveButtonDisabled = event;
  }

  onArticleVersionClick(versionId: string): void {
    this.isLockedByOtherUser = true;
    this.router.navigate(['versions', versionId, 'article'], {
      relativeTo: this.route,
    });
  }

  onToggleAutoSave(shouldAutoSave: boolean) {
    this.autoSaveState = getAutoSaveState(shouldAutoSave ? 'success' : 'off');
  }

  updateLeadAndTitleCharacterCount(formControls: GeneratedFormControl[], isPrint: boolean) {
    const summaryField = isPrint ? SUMMARY_TITLE_PRINT : SUMMARY_TITLE_ONLINE;

    const summaryTitleFormControl: GeneratedFormControl = formControls.find(({ key }) => key === summaryField);
    this.charCountTitle = getCharacterCount((summaryTitleFormControl as TextFormControl).value);

    const summaryLeadFormControl: GeneratedFormControl = formControls.find(({ key }) => key === SUMMARY_LEAD);
    this.charCountLead = getCharacterCount((summaryLeadFormControl as TextFormControl).value);

    const upTitleControl: GeneratedFormControl = find(formControls, {
      key: 'editedVersion.dataPrimary.upTitle',
    });
    this.charCountUpTitle = getCharacterCount((upTitleControl as TextFormControl)?.value);
    const mainTitleControl: GeneratedFormControl = find(formControls, {
      key: 'editedVersion.dataPrimary.title',
    });
    this.charCountMainTitle = getCharacterCount((mainTitleControl as TextFormControl)?.value);
    const preTitleControl: GeneratedFormControl = find(formControls, {
      key: 'editedVersion.dataPrimary.preTitle',
    });
    this.charCountPreTitle = getCharacterCount((preTitleControl as TextFormControl)?.value);
    this.charCountBody = this.bodySingleFormGenerator
      ? this.bodySingleFormGenerator?.counterInfo?.characterCount
      : this.contentDataFragmentsModel?.value?.body?.meta?.bodyCharacterCount;
    const subTitleControl: GeneratedFormControl = find(formControls, {
      key: isPrint ? 'editedVersion.dataSecondary.printSubTitle' : 'editedVersion.dataPrimary.subTitle',
    });
    this.charCountSubTitle = getCharacterCount((subTitleControl as TextFormControl)?.value);
    const shortLeadControl: GeneratedFormControl = find(formControls, {
      key: 'editedVersion.dataPrimary.excerpt',
    });
    this.charCountShortLead = getCharacterCount((shortLeadControl as TextFormControl)?.value);
    const recommendedTitleControl: GeneratedFormControl = find(formControls, {
      key: 'editedVersion.dataSecondary.recommendedTitle',
    });
    this.charCountRecommendedTitle = getCharacterCount((recommendedTitleControl as TextFormControl)?.value);
  }

  updateBodyCharCount() {
    this.charCountBody = this.bodySingleFormGenerator
      ? this.bodySingleFormGenerator?.counterInfo?.characterCount
        ? this.bodySingleFormGenerator?.counterInfo?.characterCount
        : this.bodySingleFormGenerator?.updatedContentData?.meta?.charCountBody
      : this.contentDataFragmentsModel?.value?.body?.meta?.bodyCharacterCount;
  }

  onBasicFormControlsChanged(formControls: GeneratedFormControl[]) {
    const isPrint = this.mediaType === 'print';
    this.updateLeadAndTitleCharacterCount(formControls, isPrint);
    if (!this.addLeadToStatistics && !this.addTitleToStatistics) {
      return;
    }

    const summaryFields = [];
    let summaryTitleField;
    if (this.addTitleToStatistics) {
      summaryTitleField = isPrint ? SUMMARY_TITLE_PRINT : SUMMARY_TITLE_ONLINE;
      summaryFields.push(summaryTitleField);
    }
    if (this.addLeadToStatistics) {
      summaryFields.push(SUMMARY_LEAD);
    }

    const summaryFormControls = formControls.filter((control) => summaryFields.includes(control.key));
    // only count extra lead info when specified control(s) are visible
    // otherwise pass down `null` to prevent displaying summary in editor
    this.leadCounterInfo = summaryFormControls.length
      ? summaryFormControls.reduce(
          ({ rowCount, characterCount, wordCount }: CounterInfo, control: TextFormControl) => {
            const charCount = getCharacterCount(control.value);
            return {
              rowCount: rowCount + Math.ceil(charCount / CHARACTER_COUNT_PER_ROW),
              characterCount: characterCount + charCount,
              wordCount: wordCount + getWordCount(control.value),
            };
          },
          { rowCount: 0, characterCount: 0, wordCount: 0 } as CounterInfo
        )
      : null;
  }

  private save() {
    const isPrint = this.mediaType === 'print';
    if (this.domainService.isMegyeiLap(this.domainKey) && isPrint) {
    } else {
      const contentData = this.joinContentDataFragments();
      this.validateGeneratedTags(contentData);
      if (this.hasInvalidTags) {
        this.sharedService.showNotification('warning', 'Verzió aktiváláshoz válasszon legalább három generált címkét!');
      }
    }
    if (!this.isActive) {
      this.contentDataFragmentsModel.value = {
        ...this.contentDataFragmentsModel.value,
        meta: {
          ...this.contentDataFragmentsModel.value.meta,
          data: this.contentDataFragmentsModel.value.meta.data.map((meta) => {
            if (meta.key === 'publishDate') {
              meta.value = this.dateFormatPipe.transform(new Date(), 'utc');
            }
            return meta;
          }),
        },
      };
    }
    this.bodySingleFormGenerator.disablePaywall();
    this.firstLockEdit();
    this.contentDataFragmentsModel.value = this.updateContentDataFragments();

    this.falseLinkInContentData = null;
    let contentData = this.joinContentDataFragments();
    contentData = this.foundationTagService.autoLinkFoundationTags(contentData);
    contentData = this.findWysiwygContents(contentData);
    if (this.falseLinkInContentData) {
      this.sharedService.showNotification('error', `Hibás URL a cikk törzsben: ${this.falseLinkInContentData}!`);
      return;
    }

    this.calculateSeoScore(contentData);
    /*const artBodyIndex = contentData.data.findIndex( ({ key }: BaseContentVariant) => key === 'ArticleBody');

    if (artBodyIndex > -1 && this.lastAutoSavedArticleBody) {
      contentData.data[artBodyIndex] = this.lastAutoSavedArticleBody;
    }*/
    this.saveLoading = true;

    if (this.dynamicTagsEnabled) {
      this.tagGeneratorService.updateRequest(
        this.getTitleContentValueString(contentData),
        this.getLeadContentValueString(contentData),
        this.getBodyContentValueString(contentData),
        ContentType.ARTICLE,
        this.contentData.meta.id
      );
    }

    let request: Observable<any>;

    let emptyContent = false;
    if (isContentEmpty(contentData.data as BackendFormalizerData[])) {
      console.warn(' ❌ A cikk nem menthető: üres tartalom!');
      request = throwError(new Error(' ❌ A cikk nem menthető: üres tartalom!'));
      emptyContent = true;
    }

    request =
      request ||
      (this.id
        ? this.apiService.updateContentPage(CONTENT_TYPE, this.id, contentData, true, true)
        : this.apiService.createContentPage(CONTENT_TYPE, contentData));

    request
      .pipe(
        catchError((error) => {
          this.saveLoading = false;
          if (this.autoSaveState.state !== 'off') {
            this.autoSaveState = getAutoSaveState('error');
          }

          if (error instanceof HttpErrorResponse) {
            const isMissingPaywall = error.error.data?.some((err) => err?.message?.includes('[FizetettTartalom]'));
            if (isMissingPaywall) {
              this.bodySingleFormGenerator.enablePaywall();
              this.sharedService.showNotification('error', 'A cikk fizetős, így kötelező az előfizetési határoló elhelyezése a cikk törzsben!');
              return;
            }

            this.sharedService.generateErrorMessageFromHttpResponse(error, contentData);
          }
          if (emptyContent) {
            this.sharedService.showNotification('error', 'Üres tartalom nem menthető!');
          }
          if (this.isPaywalled) {
            this.bodySingleFormGenerator.enablePaywall();
          }
          return of(null);
        }),
        filter((newContentData) => !!newContentData)
      )
      .subscribe((newContentData: ContentData) => {
        if (!this.id) {
          this.navigateToSavedContentPage(newContentData);
          return;
        }

        this.bodySingleFormGenerator.first = true;
        this.contentDataSubject.next(newContentData);
        this.saveLoading = false;
        this.hasUnsavedChanges = false;
        this.updatePropertiesFromContentData(newContentData);
        this.sharedService.showNotification('success', 'CMS.pageSaveSuccessful');
        // Get latest versions only if we currently on the tab that shows versions.
        if (this.selectedEditorTabIndex === 1) {
          this.versionListComponent?.loadData();
        }

        if (this.isPaywalled) {
          this.bodySingleFormGenerator.enablePaywall();
        }
      });
  }

  private autoSave(shouldReinitComponents: boolean = false) {
    this.bodySingleFormGenerator.disablePaywall();
    this.updateBodyCharCount();
    const contentData = this.joinContentDataFragments();

    this.calculateSeoScore(contentData);

    if (this.dynamicTagsEnabled) {
      this.tagGeneratorService.updateRequest(
        this.getTitleContentValueString(contentData),
        this.getLeadContentValueString(contentData),
        this.getBodyContentValueString(contentData),
        ContentType.ARTICLE,
        this.contentData.meta.id
      );
    }

    this.autoSaveState = getAutoSaveState('in-progress');
    this.firstLockEdit();

    this.apiService
      .updateContentPage(CONTENT_TYPE, this.id, contentData, false, shouldReinitComponents)
      .pipe(
        catchError((error) => {
          if (this.autoSaveState.state !== 'off') {
            this.autoSaveState = getAutoSaveState('error');
          }
          this.saveLoading = false;

          if (error instanceof HttpErrorResponse) {
            this.sharedService.generateErrorMessageFromHttpResponse(error, contentData);
          }
          if (this.isPaywalled) {
            this.bodySingleFormGenerator.enablePaywall();
          }
          return of({});
        }),
        filter((newContentData) => !!newContentData)
      )
      .subscribe((newContentData: ContentData) => {
        if (newContentData.meta?.needReloadPage) {
          location.reload();
        }

        if (!shouldReinitComponents) {
          this.contentDataFragmentsModel.value = {
            ...this.contentDataFragmentsModel.value,
            body: this.articleEditorService.getContentDataFragment(this.articleEditorService.bodyFilter, newContentData),
          };
        }
        this.contentDataSubject.next(newContentData);
        this.autoSaveState = getAutoSaveState('success');
        this.saveLoading = false;
        if (newContentData.data) {
          this.lastAutoSavedArticleBody = newContentData.data.find(({ key }) => key === 'ArticleBody');
        }
        // Get latest versions only if we currently on the tab that shows versions.
        if (this.selectedEditorTabIndex === 1) {
          this.versionListComponent?.loadData();
        }
        if (this.isPaywalled) {
          this.bodySingleFormGenerator.enablePaywall();
        }
      });
  }

  private firstLockEdit() {
    this.apiService
      .lockContentPage(this.id, CONTENT_TYPE)
      .pipe(
        catchError(() => {
          this.unlockContent().subscribe();
          return of(null);
        })
      )
      .subscribe((res: MinuteToMinuteForceUnlock) => {
        this.checkEditIsLocked(res);
      });
  }

  private setupLockEditSubscription() {
    const lockEditSubscription = interval(LOCK_EDIT_INTERVAL_MS)
      .pipe(
        takeUntil(this.destroy$),
        concatMap(() =>
          this.apiService.lockContentPage(this.id, CONTENT_TYPE).pipe(
            catchError(() => {
              lockEditSubscription.unsubscribe();
              return of(null);
            })
          )
        )
      )
      .subscribe((res: MinuteToMinuteForceUnlock) => {
        this.checkEditIsLocked(res);
      });
  }

  private checkEditIsLocked(res: MinuteToMinuteForceUnlock) {
    if (res.message) {
      const latestTtitle = res.latestVersionTitle.trim() ? res.latestVersionTitle : 'nincs cím';
      const message = res.message.replace('cikk', `(${latestTtitle}) nevű cikk`);
      this.forceUnlockService.message = message;
      this.router.navigateByUrl('admin/articles');
    }
  }

  private setupSaveListener() {
    const [save, autoSave] = partition(
      this.save$.pipe(
        takeUntil(this.destroy$)
        // debounceTime(500)
      ),
      (saveType: 'save' | 'autosave') => saveType === 'save'
    );
    const checkTimer = timer(0, 1500);

    autoSave
      .pipe(
        debounce(() => checkTimer.pipe(filter(() => !this.saveLoading && this.autoSaveState.state !== 'in-progress' && this.autoSaveState.state !== 'off'))),
        debounceTime(1500),
        tap(() => (this.saveButtonDisabled = false)),
        catchError((error) => {
          this.saveButtonDisabled = false;
          return throwError(() => error);
        })
      )
      .subscribe(() => this.autoSave(true));

    save
      .pipe(
        debounceTime(500),
        debounce(() => checkTimer.pipe(filter(() => !this.saveLoading)))
      )
      .subscribe(() => this.save());
  }

  private unlockContent(): Observable<any> {
    if (!this.isLockedByOtherUser && this.id) {
      return this.apiService.unlockContentPage(this.id, CONTENT_TYPE);
    }
    return of(null);
  }

  /**
   * By default, the API only returns a generic url to fetch columns.
   * Based on whether the currently edited article is a print or online article
   * we need to append query params to the url to filter the columns accordingly.
   * runWithPrintFilter=1 -> enables the filter to query only print or only online columns
   * printOnly=1 -> query only print columns or only online columns
   * @param url url to append the params.
   * @private
   */
  private appendPrintFilterToColumnTreeUrl(url: string): string {
    const columnTreeUrl = new URL(url);
    columnTreeUrl.searchParams.set('runWithPrintFilter', '1');
    columnTreeUrl.searchParams.set('printOnly', this.mediaType === 'print' ? '1' : '0');
    return columnTreeUrl.toString();
  }

  removeDoubleArticleRecommendationTitleLead(bodyContentData: ContentData): ContentData {
    // removes title and lead input from the double article body component popup
    const newData = bodyContentData.data.map((item) =>
      item.key === 'ArticleBody'
        ? {
            ...item,
            availableComponents: (item as BaseContentVariantWithComponents).availableComponents.map((components) =>
              components.type === 'DoubleArticleRecommendationOptional.Double'
                ? {
                    ...components,
                    details: components.details.filter((detail) => detail.key !== 'title' && detail.key !== 'lead'),
                  }
                : components
            ),
          }
        : item
    );

    return { ...bodyContentData, data: newData };
  }

  private updatePropertiesFromContentData(contentData: ContentData) {
    this.mediaType = this.articleEditorService.getArticleMediaType(contentData.data, !!this.id);

    if (this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_TO_SHOW_PRINT_ONLY_COLUMNS_FOR_PRINT_ARTICLES)) {
      const primaryColumnIndex: number = contentData.data.findIndex((item: BaseContentVariant) => item.key === 'primaryColumn');
      if (primaryColumnIndex > -1 && contentData.data[primaryColumnIndex]?.inputInfo?._tempNewSourceUrl) {
        const columnTreeUrl = this.appendPrintFilterToColumnTreeUrl(contentData.data[primaryColumnIndex].inputInfo._tempNewSourceUrl);
        contentData.data[primaryColumnIndex].inputInfo._tempNewSourceUrl = columnTreeUrl.toString();
      }

      const secondaryColumnIndex: number = contentData.data.findIndex((item: BaseContentVariant) => item.key === 'secondaryColumns');
      if (secondaryColumnIndex > -1 && contentData.data[secondaryColumnIndex]?.inputInfo?._tempNewSourceUrl) {
        const columnTreeUrl = this.appendPrintFilterToColumnTreeUrl(contentData.data[secondaryColumnIndex].inputInfo._tempNewSourceUrl);
        contentData.data[secondaryColumnIndex].inputInfo._tempNewSourceUrl = columnTreeUrl.toString();
      }
    }
    this.minuteToMinuteState = this.articleEditorService.getMinuteToMinuteState(contentData.data, !!this.id);

    this.isReviewable = this.articleEditorService.getIsReviewable(contentData.data, !!this.id);

    this.contentDataFragmentsModel.value = {
      titles: this.articleEditorService.getContentDataFragment(this.articleEditorService.titlesFilter, contentData),
      body: this.articleEditorService.getContentDataFragment(this.articleEditorService.bodyFilter, contentData),
      meta: this.articleEditorService.getContentDataFragment(this.articleEditorService.metaFilter, contentData),
      seoFields: this.articleEditorService.getContentDataFragment(this.articleEditorService.seoFieldsFilter, contentData),
      printOrOnline: this.articleEditorService.getContentDataFragment(this.articleEditorService.fieldFilterByKey('isPrint'), contentData),
    };
    this.bodyContentData = this.contentDataFragmentsModel.value.body;

    if (this.isNSO) {
      this.bodyContentData = this.removeDoubleArticleRecommendationTitleLead(this.bodyContentData);
    }

    const {
      isActive,
      publicState,
      canEdit,
      canActivate,
      editLocked,
      wasSaved,
      canCreatePrint,
      canCreateOnline,
      blame: { createdAt, updatedAt, updatedBy },
    } = contentData.meta;
    this.isActive = isActive;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
    this.updatedBy = updatedBy;
    this.canCreatePrint = canCreatePrint;
    this.createPrintArticle = canCreatePrint && !this.isMetropol && !this.isBors && !this.isRipost;
    this.canCreateOnline = canCreateOnline;
    const canActivateNewVersion = ['publishedAndModified', 'timedAndModified'].includes(publicState);
    this.canActivateArticle = wasSaved && canActivate && this.mediaType === 'online' && (!isActive || canActivateNewVersion);
    this.canActivateNewVersion = canActivateNewVersion;
    this.canInactivateArticle = canActivate && this.mediaType === 'online' && isActive;
    if (this.dynamicTagsEnabled) {
      this.validateGeneratedTags(contentData);
    }

    if (editLocked) {
      this.isLockedByOtherUser = true;
      this.editLockDescription = `A tartalmat jelenleg ${editLocked.user} szerkeszti, ezért nem lehet módosítani!`;
    } else if (this.id) {
      this.setupLockEditSubscription();
    }
    const isFormDisabled = contentData.meta.hasOwnProperty('canEdit') && !canEdit;
    this.showActionButtons = !isFormDisabled && !this.isLockedByOtherUser;
    this.formControlMapOptions = {
      printOrOnline: this.mediaType,
      isFormDisabled: isFormDisabled || this.isLockedByOtherUser,
      publicState: publicState,
    };
    this.isFormDisabled = isFormDisabled;
  }

  private redirectToCreateArticlePage(): void {
    // If domain has only online article media type
    if (
      (this.isBors ||
        this.isMetropol ||
        this.isRipost ||
        this.isVilaggazdasag ||
        this.isShe ||
        this.isLife ||
        this.isMindmegette ||
        this.isKoponyeg ||
        this.isOrigo ||
        this.isPestiSracok) &&
      !this.id
    ) {
      this.loaderService.show();
      this.selectedArticleMediaType = 'online';
      this.onCreateArticle();
      return;
    }
  }

  private joinContentDataFragments(): ContentData {
    const body = this.contentDataFragmentsModel.value.body;
    const characterCount = body?.meta?.characterCount + this.leadCounterInfo?.characterCount;
    const wordCount = body?.meta?.wordCount + this.leadCounterInfo?.wordCount ? this.leadCounterInfo?.wordCount : 0;
    return this.contentDataFragmentsModel.joinContentDataFragments(this.contentDataFragmentsModel.value, {
      ...body?.meta,
      wordCount: wordCount || 0,
      characterCount: characterCount || 0,
      bodyCharacterCount: body?.meta?.characterCount || 0,
      leadCharacterCount: this.charCountLead || body?.meta?.leadCharacterCount || 0,
      titleCharacterCount: this.charCountTitle || 0,
      charCountTitle: this.charCountTitle || 0,
      charCountUpTitle: this.charCountUpTitle || 0,
      charCountMainTitle: this.charCountMainTitle || 0,
      charCountPreTitle: this.charCountPreTitle || 0,
      charCountLead: this.charCountLead || 0,
      charCountBody: this.charCountBody || 0,
      charCountSubTitle: this.charCountSubTitle || 0,
      charCountShortLead: this.charCountShortLead || 0,
      charCountRecommendedTitle: this.charCountRecommendedTitle || 0,

      seoScore: this.seoScoreValue || undefined,
    });
  }

  private navigateToSavedContentPage(contentData: ContentData) {
    this.hasUnsavedChanges = false;
    this.articleEditorService.storeCurrentScrollPosition();
    this.router
      .navigate([contentData.meta.id], {
        relativeTo: this.route,
        queryParamsHandling: 'preserve',
      })
      .then(() => {
        this.saveLoading = false;
        this.sharedService.showNotification('success', 'CMS.pageCreateSuccessful');
      });
  }

  removeEmptyParagrpahs(): ContentData {
    const bodyContent = this.bodyFormGenerator?.contentData || this.contentDataFragmentsModel.value.body;
    bodyContent.data[0].value.forEach((value) => {
      if (value.details[0].type === 'Basic.Wysiwyg.WysiwygDetail') {
        if (value.details[0].value === '' || value.details[0].value === null) {
          value.deleted = true;
        } else {
          value.details[0].value = this.changeBreaksToParagraphEnd(value.details[0].value);
        }
      }
    });
    return bodyContent;
  }

  changeBreaksToParagraphEnd(text: string): string {
    return text.replace(/<br>/g, '</p><p>');
  }

  private updateContentDataFragments(): ArticleContentDataFragments {
    const currentValue = this.contentDataFragmentsModel.value;

    const val = {
      ...currentValue,
      body: this.bodySingleFormGenerator?.updatedContentData || currentValue.body,
      meta: this.metaFormGenerator?.contentData || currentValue.meta,
      titles: this.titlesFormGenerator?.contentData || currentValue.titles,
      seoFields: this.seoCard?.contentData || currentValue.seoFields,
    };
    return val;
  }

  private createContent(contentData: ContentData): void {
    this.apiService
      .createContentPage(CONTENT_TYPE, contentData)
      .pipe(
        catchError((error) => {
          if (error instanceof HttpErrorResponse) {
            this.saveLoading = false;
            this.sharedService.generateErrorMessageFromHttpResponse(error, contentData);
          }
          return of(null);
        })
      )
      .subscribe((newContentData: ContentData) => {
        this.navigateToSavedContentPage(newContentData);
      });
  }

  private toggleActivate(shouldActivate: boolean) {
    this.activateLoading = true;
    this.saveLoading = true;
    const request = shouldActivate ? this.apiService.activateContentPage(this.id, CONTENT_TYPE) : this.apiService.inactivateContentPage(this.id, CONTENT_TYPE);
    const succesMessage = shouldActivate ? 'Aktiválás sikeres!' : 'Inaktiválás sikeres!';

    return request
      .pipe(
        concatMap(() => this.apiService.getContentPage(CONTENT_TYPE, this.id)),
        finalize(() => (this.saveLoading = false))
      )
      .subscribe(
        (contentData: ContentData) => {
          this.activateLoading = false;
          this.shouldNotSave = true;
          this.updatePropertiesFromContentData(contentData);
          this.sharedService.showNotification('success', succesMessage);
          this.versionListComponent?.loadData();
          this.contentDataSubject.next(contentData);
        },
        (error: HttpErrorResponse) => {
          this.activateLoading = false;
          this.sharedService.generateErrorMessageFromHttpResponse(error, this.contentData);
        }
      );
  }

  private calculateSeoScore(contentData: ContentData = undefined) {
    if (this.mediaType !== 'online') {
      return;
    }
    if (!contentData) {
      this.contentDataFragmentsModel.value = this.updateContentDataFragments();
      contentData = this.joinContentDataFragments();
    }
    const result = this.seoScoreService.calculateSeoScore(contentData, { isFirstCalculation: this.isSeoFirstCalculation });
    this.isSeoFirstCalculation = false;
    this.seoScoreValue = result?.finalScore;
    this.seoCheckResult$.next(result);
  }

  selectedEditorTabIndexchanged(index: number): void {
    this.selectedEditorTabIndex = index;

    // Get latest versions only if we currently on the tab that shows versions.
    if (index === 1) {
      this.versionListComponent?.loadData();
    }
  }

  /**
   * This function is used to unlock the article and navigate back to the
   * article list page if the user went away for too long.
   */
  closeForAwayState(): void {
    this.autoSave();
    const checkAutoSaveAndExit = () => {
      if (this.autoSaveState.state === 'success') {
        this.unlockContent().subscribe(() => {
          this.awayModalRef?.close();
          this.router.navigate(['admin', 'articles']);
        });
      } else if (this.autoSaveState.state === 'in-progress') {
        console.log('Autosave is still in progress... waiting...');
        setTimeout(checkAutoSaveAndExit, 1000);
      }
    };
    setTimeout(checkAutoSaveAndExit, 1000);
  }

  private getBodyContentValueString(contentData: ContentData): string {
    const articleBody = this.articleEditorService
      .getContentDataFragment(this.articleEditorService.bodyFilter, contentData)
      .data.find(({ key }) => key === 'ArticleBody');
    const wysiwygContent = articleBody.value.filter(({ type }) => type === 'Basic.Wysiwyg.Wysiwyg');
    let articleValueString = '';
    wysiwygContent.map((content) => {
      if (content.details[0].value) {
        articleValueString = articleValueString + ' ' + content.details[0].value;
      }
    });
    return articleValueString;
  }

  private getTitleContentValueString(contentData: ContentData): string {
    return this.articleEditorService
      .getContentDataFragment(this.articleEditorService.titlesFilter, contentData)
      .data.find(({ key }) => key === 'editedVersion.dataPrimary.title').value;
  }

  private getLeadContentValueString(contentData: ContentData): string | null {
    const lead = this.articleEditorService.getContentDataFragment(this.articleEditorService.fieldFilterByKey('editedVersion.dataSecondary.lead'), contentData);
    const leadValue = lead.data[0]?.value;
    if (leadValue === '' || !leadValue) {
      return null;
    }
    return leadValue;
  }

  private validateGeneratedTags(contentData: ContentData): void {
    if (this.tagGeneratorError) {
      this.hasInvalidTags = false;
    } else {
      const tagValues = contentData.data.find(({ key }) => key === 'tags')?.value || [];
      this.hasInvalidTags = tagValues.length < 3;
    }
  }

  onSeoTitleChange(newTitle: string): void {
    this.seoTitle = newTitle;
    if (this.portalConfigService.isConfigSet(PortalConfigSetting.SLUG_GENERATION_FROM_SEO_TITLE_FOR_ARTICLES)) {
      if (
        this.portalConfigService.isConfigSet(PortalConfigSetting.SLUG_GENERATION_ON_CHANGE_FOR_ARTICLES) ||
        this.formControlMapOptions.publicState === 'draft'
      ) {
        const seoTitleSlug = this.utils.generateSlug(this.seoTitle);
        const control = this.titlesFormGenerator.formControls.find((control) => control.key === 'slug');
        const otherControls = this.titlesFormGenerator.formControls.filter((control) => control.key !== 'slug');
        const updatedFormControl = {
          ...control,
          value: seoTitleSlug,
        } as GeneratedFormControl;
        this.titlesFormGenerator.formControls = [...otherControls, updatedFormControl];
      }
    }
  }

  findWysiwygContents(contentData: ContentData): ContentData {
    const newData: BaseContentVariant[] = contentData.data.map(
      (item: BaseContentVariant): BaseContentVariant =>
        item.key === 'ArticleBody'
          ? {
              ...item,
              value: [
                ...item.value.map((value) =>
                  value.type === 'Basic.Wysiwyg.Wysiwyg'
                    ? {
                        ...value,
                        details: value.details.map((detail) => ({
                          ...detail,
                          value: this.trimAndValidateLink(detail.value),
                        })),
                      }
                    : value
                ),
              ],
            }
          : item
    );
    return { ...contentData, data: [...newData] };
  }

  trimAndValidateLink(htmlString: string = ''): string {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlString, 'text/html');

    // All <a> tags
    const links = doc.querySelectorAll('a');

    links.forEach((link) => {
      const linkIsKpi = link.getAttribute('class') === 'kpi-link';
      const isCrossword = link.getAttribute('href')?.includes('keresztrejtveny');
      const href = link.getAttribute('href')?.trim();
      if (href && !href.startsWith('/file') && !linkIsKpi && !isCrossword) {
        // URL check
        try {
          const url = new URL(href);
          link.setAttribute('href', url.href);
        } catch (e) {
          this.falseLinkInContentData = href;
        }
      }
    });

    return doc.body.innerHTML;
  }
}

const isContentEmpty = (data: BackendFormalizerData[]): boolean => {
  const articleBody: BackendFormalizerBody = data.find(({ key }) => key === 'ArticleBody') as BackendFormalizerBody;
  const articleComponent: BackendFormalizerComponent[] = articleBody?.value;
  return articleComponent?.filter(({ type }) => type !== 'Eadvert.Eadvert')?.length < 1;
};
