import { Component, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ApiService } from '../../services/api.service';
import { Router } from '@angular/router';
import { StorageService } from '@external/utils';
import { environment } from '../../../../environments/environment';
import { DomainService } from '../admin/services/domain.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  standalone: false,
})
export class LoginComponent implements OnInit {
  loginForm: UntypedFormGroup;
  loading: boolean;
  errorMessage: string;
  environment = environment;

  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router,
    private readonly domain: DomainService,
    private readonly storageService: StorageService
  ) {}

  ngOnInit(): void {
    this.domain.clearCurrentDomainCookie();
    // We need to remove the cookie for both the domain and without domain.
    // This ensures backwards compatibility as earlier versions of the CMS did not specify domain for the cookies.
    if (environment.baseHost) {
      this.storageService.setCookie('jwt_token', '', -1, environment.baseHost);
    }
    this.storageService.setCookie('jwt_token', '', -1);
    this.loginForm = new UntypedFormGroup({
      email: new UntypedFormControl(null, [Validators.required]),
      password: new UntypedFormControl(null, [Validators.required]),
      remember: new UntypedFormControl(false),
    });
  }

  onLogin(): void {
    if (this.loginForm.valid) {
      this.loading = true;
      this.apiService.login(this.loginForm.value).subscribe(
        ({ token }: { token: string }) => {
          if (token) {
            this.storageService.setCookie('jwt_token', token, null, environment.baseHost ? `.${environment.baseHost}` : null);
          }
          this.router.navigate(['/', 'admin']);
          this.loading = false;
        },
        (err) => {
          if (err.status === 401) {
            this.errorMessage = 'CMS.invalidCredentials';
          }
          this.loading = false;
        }
      );
    } else {
      Object.keys(this.loginForm.controls).forEach((formControlName) => {
        this.loginForm.controls[formControlName].markAsDirty();
        this.loginForm.controls[formControlName].updateValueAndValidity();
      });
    }
  }
}
