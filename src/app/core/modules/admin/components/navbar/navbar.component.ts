import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { StorageService } from '@external/utils';
import { concatMap, filter, takeUntil } from 'rxjs/operators';
import { ApiService } from 'src/app/core/services/api.service';
import { DestroyService } from 'src/app/shared/services/destroy.service';
import { environment } from 'src/environments/environment';
import { Domain } from '../../admin.definitions';
import { DomainService } from '../../services/domain.service';
import { LogoutService } from '../../services/logout.service';
import { IEnvironment } from '../../../../../../environments/environment.definitions';

@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.scss'],
  providers: [DestroyService],
  standalone: false,
})
export class NavbarComponent implements OnInit {
  @Input() domains: Domain[];
  @Input() userName: string;
  @Input() roleNames: string;
  @Input() avatar: string;
  @Input() apiVersion: string;

  @Output() domainChange: EventEmitter<Domain> = new EventEmitter<Domain>();

  environment: IEnvironment = environment;

  public projectName: string;
  public selectedDomain: Domain;
  public profileMenu = [
    {
      title: 'CMS.profile',
      action: this.onNavigateToProfile.bind(this),
    },
    {
      title: 'CMS.logout',
      action: this.onLogout.bind(this),
    },
  ];

  public portalPreviewUrl: string;
  get logo() {
    return this.selectedDomain.key === 'nso' ? 'assets/images/domain-logos/nso-logo-white.svg' : 'assets/images/default-logo-inverse.png';
  }

  constructor(
    private router: Router,
    private storageService: StorageService,
    private domainService: DomainService,
    private apiService: ApiService,
    private logoutService: LogoutService,
    private destroy$: DestroyService
  ) {
    this.portalPreviewUrl = this.domainService.currentDomain?.info.previewUrl;
  }

  ngOnInit(): void {
    this.projectName = environment.projectName;
    this.selectedDomain = this.domainService.currentDomain;

    /*if (!this.selectedLang && this.contentLangs.length) {
      this.onChangeLang(this.contentLangs[0].locale);
    }*/
  }

  public domainCompareFn(domain1: Domain, domain2: Domain) {
    return domain1 && domain2 && domain1.key === domain2.key;
  }

  onChangeDomain(domain: Domain) {
    this.storageService.removeSessionStorageData('searchParamsTitle');
    this.domainChange.emit(domain);
  }

  onLogout() {
    this.logoutService.setLoggingOutStarted();
    this.logoutService.pendingPreLogoutActions$
      .pipe(
        takeUntil(this.destroy$),
        filter((pendingActions) => pendingActions === 0),
        concatMap(() => this.apiService.logout())
      )
      .subscribe(() => {
        this.storageService.setCookie('jwt_token', null, -1);
        this.storageService.setCookie('mind_jwt_token', null, -1);
        this.domainService.domains.forEach(({ key }) => {
          this.storageService.removeSessionStorageData(`${key}-article-search-data`);
        });
        this.domainService.currentDomain = null;
        this.router.navigate(['/']);
      });
  }

  onNavigateToProfile() {
    this.router.navigate(['/', 'admin', 'profile']);
  }

  logCurrentEnv(): void {
    console.log({ environment });
  }
}
