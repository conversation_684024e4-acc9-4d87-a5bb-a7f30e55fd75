import { AfterViewInit, Component, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, NavigationStart, Router } from '@angular/router';
import { catchError, debounceTime, filter, map, takeUntil } from 'rxjs/operators';
import { IContentLang, InitData } from '../../core.definitions';
import { MenuItemsService } from '../../services/menu-items.service';
import { ICMSMenuItem } from 'src/app/shared/definitions/shared.definitions';
import { InitModelService } from '../../services/model/init-model/init-model.service';
import { Domain } from './admin.definitions';
import { DomainService } from './services/domain.service';
import { DestroyService } from 'src/app/shared/services/destroy.service';
import { ReqService } from '@external/http';
import { HttpClient } from '@angular/common/http';
import { UtilsService } from '@external/utils';
import { ApiService } from '../../services/api.service';
import { EMPTY, of } from 'rxjs';
import { ApiResult } from '@trendency/kesma-ui';
import { Author } from '@modules/contributors/contributors.definitions';
import { SeoDashboardApiService } from '@modules/seo-dashboard/api/services/seo-dashboard-api.service';
import { NotificationService } from '@core/services/api/notifications/notification.service';

@Component({
  selector: 'app-admin',
  templateUrl: './admin.component.html',
  styleUrls: ['./admin.component.scss'],
  providers: [DestroyService],
  standalone: false,
})
export class AdminComponent implements OnInit, AfterViewInit {
  public contentLangs: IContentLang[];
  public jwtExpireInMinutes: number;
  public userName: string;
  public roleNames: string;
  public avatar: string;
  public apiVersion: string;
  public menu: ICMSMenuItem[];
  public availableDomains: Domain[];
  public generalMenuItems = ['news-reception'];
  public newsWarning = false;
  public latestNewsId: string;
  public isCollapsed: boolean;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly initModel: InitModelService,
    private readonly domainService: DomainService,
    private readonly menuItemsService: MenuItemsService,
    private readonly router: Router,
    private readonly destroy$: DestroyService,
    private readonly reqService: ReqService,
    private readonly httpClient: HttpClient,
    private readonly utilsService: UtilsService,
    private readonly apiService: ApiService,
    private readonly seoDashboardApiService: SeoDashboardApiService,
    private readonly notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    const initData: InitData = this.route.snapshot.data?.init;
    this.initModel.data = { userId: initData.UID, userName: initData.username, loggedInUserFullName: initData.lastFirstName };

    this.userName = initData.lastFirstName;
    this.roleNames = initData.roleNames;
    this.avatar = initData.avatar;
    if (this.avatar == null) this.avatar = '/assets/images/user-profile-placeholder.jpg';
    this.apiVersion = initData.apiVersion;
    // const initLoginData = initData?.[1];
    // this.contentLangs = (initData && initData.data && initData.data.allowedLanguages) || [];
    // const jwtExpire = (initLoginData && initLoginData.data && initLoginData.data.jwtTokenExpire) || null;
    // this.jwtExpireInMinutes = jwtExpire && jwtExpire / 60;

    this.domainService.setDomainOnHTMLTag(this.domainService.currentDomain?.key);
    const currentDomainMenus = initData.menus ?? [];
    //.filter(header => header !== 'rolegroup');
    const initMenuData = this.checkNewStatistic(currentDomainMenus)?.concat(this.generalMenuItems);
    this.menu = this.filterMenu(initMenuData);
    const availablePortalNames: string[] = initData.portal;
    this.availableDomains = this.domainService.getAvailableDomains(availablePortalNames) ?? [];
    this.checkLatestNewsId();
    this.getCurrentExternalContributor();
    if (this.domainService.currentDomain?.key === 'koponyeg') {
      this.getDetections();
    }

    this.notificationService.initInstantNotifications();
  }

  ngAfterViewInit() {
    this.setUpnavigationListeners();
  }

  getDetections(): void {
    const defaultStatus = 'waiting';
    this.apiService.getDetections(defaultStatus).subscribe((res) => {
      res?.data.length > 0 ? localStorage.setItem('hasWaitingDetections', 'true') : localStorage.setItem('hasWaitingDetections', 'false');
    });
  }

  checkNewStatistic(menuData: string[]): string[] {
    if (menuData.includes('menu_user_statistics_columns_performance') || menuData.includes('menu_user_statistics_authors_performance')) {
      menuData.push('menu_user_statistics');
      const index = menuData.indexOf('users_statistics');
      if (index > -1) {
        menuData.splice(index, 1);
      }
    }
    return menuData;
  }

  setUpnavigationListeners() {
    this.router.events
      .pipe(
        takeUntil(this.destroy$),
        filter((event) => event instanceof NavigationEnd && this.router.url === '/admin/news-reception')
      )
      .subscribe((val) => {
        this.setMenuWarning(false);
        this.setLatestNewsId(this.latestNewsId);
      });

    this.router.events
      .pipe(
        takeUntil(this.destroy$),
        filter((event) => event instanceof NavigationStart && this.router.url !== '/admin/perceptions' && this.domainService.currentDomain?.key === 'koponyeg'),
        debounceTime(100)
      )
      .subscribe((val) => {
        this.getDetections();
      });

    this.router.events
      .pipe(
        takeUntil(this.destroy$),
        filter((event) => event instanceof NavigationStart && this.router.url !== '/admin/news-reception'),
        debounceTime(100)
      )
      .subscribe(() => {
        this.checkLatestNewsId();
      });
  }

  // TODO: implement recursive filter once going more than 2nd depth
  filterMenu(enabledMenus: string[]) {
    return (this.menuItemsService.items ?? []).filter((menuItem) => {
      if (!menuItem.children) {
        return enabledMenus.findIndex((key) => menuItem.key === key) > -1;
      } else {
        // check if parent menu is enabled
        if (enabledMenus.findIndex((key) => menuItem.key === key) > -1) {
          // filter children
          menuItem.children = menuItem.children.filter((childItem) => enabledMenus.findIndex((key) => childItem.key === key) > -1);
          return true;
        } else {
          return false;
        }
      }
    });
  }

  onDomainChange(domain: Domain) {
    const lastSegment = this.router.url.split('/').at(-1);
    const id = lastSegment.substring(0, lastSegment.indexOf('?'));

    if (this.router.url.includes('articles/page-editor/') && id !== 'new') {
      this.apiService.unlockContentPage(id, 'article').subscribe(() => {
        this.domainService.currentDomain = domain;
        this.reloadPage();
      });
    } else {
      this.domainService.currentDomain = domain;
      setTimeout(() => {
        this.reloadPage();
      }, 500);
    }
  }

  setMenuWarning(value: boolean) {
    this.newsWarning = value;
  }

  checkLatestNewsId() {
    const url = `https://zoe-static.mediaworks.hu/iframe_zoe_news_client/${this.getDomain()}/latest`;
    this.httpClient
      .get(url)
      .pipe(catchError(() => EMPTY))
      .subscribe((res) => {
        this.latestNewsId = res['id'];
        if (
          !localStorage.getItem('latestUserNewsId' + [this.getDomain()]) ||
          (localStorage.getItem('latestUserNewsId' + [this.getDomain()]) && localStorage.getItem('latestUserNewsId' + [this.getDomain()]) < this.latestNewsId)
        ) {
          this.setMenuWarning(true);
        }
      });
  }

  getDomain(): string {
    return this.domainService.currentDomain?.key;
  }

  getCurrentExternalContributor(): void {
    this.apiService
      .getCurrentExternalContributor()
      .pipe(
        map(({ data }: ApiResult<Author>) => data),
        catchError(() => of(null)),
        takeUntil(this.destroy$)
      )
      .subscribe((data) => {
        this.seoDashboardApiService.currentExternalContributor.set(data);
      });
  }

  setLatestNewsId(latestNewsId) {
    if (latestNewsId) {
      localStorage.setItem('latestUserNewsId' + [this.getDomain()], latestNewsId);
    }
  }

  reloadPage() {
    if (this.utilsService.isBrowser()) {
      setTimeout(() => {
        window.location.href = `${window.location.origin}/admin/dashboard`;
      }, 500);
    }
  }
}
