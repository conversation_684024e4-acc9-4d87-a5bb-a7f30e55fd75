import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { StorageService } from '@external/utils';
import { Observable, of, take } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { PortalConfigService } from '@shared/services/portal-config.service';
import { InitData } from '../core.definitions';
import { DomainService } from '../modules/admin/services/domain.service';
import { ApiService } from '../services/api.service';
import { SeoDashboardApiService } from '@modules/seo-dashboard/api/services/seo-dashboard-api.service';

@Injectable()
export class InitResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router,
    private readonly domainService: DomainService,
    private readonly storageService: StorageService,
    private readonly portalConfigService: PortalConfigService,
    private readonly seoDashboardApiService: SeoDashboardApiService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<InitData> {
    const initRequest$ = this.apiService.getInit().pipe(
      take(1),
      catchError(() => of(null)),
      switchMap((initResponse) => this.redirectToDomainSelect(initResponse.data, route.routeConfig.path))
    );
    const mindLoginRequest$ = this.seoDashboardApiService.login();
    return initRequest$.pipe(
      switchMap((result) => (result?.portalConfigs?.mind_login_is_enabled === '1' ? mindLoginRequest$.pipe(map(() => result)) : of(result)))
    );
  }

  private redirectToDomainSelect(initData: InitData, path: string): Observable<InitData> {
    this.storageService.setSessionStorageData('roles', initData.roles);
    this.portalConfigService.setConfig(initData.portalConfigs);
    const availablePortals: string[] = initData.portal;
    const currentDomain = this.domainService.currentDomain;
    const noSelectedDomain = !currentDomain || !availablePortals.includes(currentDomain.info.portalHeader);

    if (noSelectedDomain && path !== 'domain-select') {
      if (availablePortals.length > 1) {
        this.router.navigate(['/', 'domain-select']);
      } else {
        const availableDomains = this.domainService.getAvailableDomains(availablePortals);
        this.domainService.currentDomain = availableDomains[0];
        return this.apiService.isLoggedIn().pipe(
          switchMap(() =>
            this.apiService.getInit().pipe(
              catchError(() => of(null)),
              map((initResponse) => {
                this.portalConfigService.setConfig(initResponse.data.portalConfigs);
                return initResponse.data;
              })
            )
          )
        );
      }
    }

    return of(initData);
  }
}
