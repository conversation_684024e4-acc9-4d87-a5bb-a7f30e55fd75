import { inject, Injectable } from '@angular/core';
import { MediaStoreModalService } from 'src/app/shared/services/media-store-modal.service';
import { ICMSMenuItem } from 'src/app/shared/definitions/shared.definitions';
import { DomainService } from '../modules/admin/services/domain.service';
import { ImageListModal } from '@media/media-image/modals/image-list.modal';
import { PortalConfigService } from '@shared/services/portal-config.service';
import { PortalConfigSetting } from '@shared/definitions/portal-config';
import { FileListModal } from '@media/media-file/modals/file-list.modal';

@Injectable({
  providedIn: 'root',
})
export class MenuItemsService {
  private readonly imageModal = inject(ImageListModal);
  private readonly fileModal = inject(FileListModal);
  private readonly portalConfig = inject(PortalConfigService);

  public constructor(
    private readonly mediaStoreModal: MediaStoreModalService,
    private readonly domainService: DomainService
  ) {}

  private getGlossaryTitle(): string {
    if (this.domainService.isCurrent('magyarNemzet')) {
      return 'CMS.sportlexikon';
    }
    return 'CMS.glossary';
  }

  public get items(): ICMSMenuItem[] {
    return [
      {
        type: 'dashboard',
        title: 'CMS.dashboard',
        path: 'dashboard',
        icon: 'area-chart',
        key: 'dashboard',
      },
      {
        type: 'users',
        title: 'CMS.users',
        path: 'cms-users',
        icon: 'user',
        key: 'users_cms-users',
      },
      {
        type: 'portal-users',
        title: 'CMS.portal-users',
        path: 'portal-users',
        icon: 'user',
        key: 'portal_user',
      },
      {
        type: 'authors',
        title: 'CMS.authors',
        path: null,
        icon: 'user',
        key: 'authors',
        children: [
          {
            type: 'contributors',
            title: 'contributors',
            path: 'contributors',
            icon: 'user',
            key: 'authors_contributors',
          },
          {
            type: 'publicAuthors',
            title: 'publicAuthors',
            path: 'public-authors',
            icon: 'user',
            key: 'authors_public_authors',
          },
        ],
      },
      {
        type: 'forecast_external_users',
        title: 'CMS.partners',
        path: 'forecast_external_users',
        key: 'forecast_external_users',
        icon: 'usergroup-add',
        children: [
          {
            type: 'forecast_external_users',
            title: 'API',
            path: 'forecast_external_users',
            key: 'forecast_external_users',
          },
          {
            type: 'automail',
            title: 'CMS.automail',
            path: 'automail',
            key: 'custom_mails',
          },
        ],
      },
      {
        type: 'forecast_external_users',
        title: 'CMS.stations',
        path: 'stations',
        icon: 'apartment',
        key: 'forecast_external_users',
      },
      {
        type: 'users_statistics',
        title: 'CMS.users_statistics',
        path: 'users-statistics',
        icon: 'area-chart',
        key: 'users_statistics',
      },
      {
        type: 'user_statistics',
        title: 'CMS.users_statistics',
        path: 'null',
        icon: 'area-chart',
        key: 'menu_user_statistics',
        children: [
          {
            type: 'users_statistics',
            title: 'CMS.default_statistics',
            path: 'users-statistics',
            icon: 'area-chart',
            key: 'menu_user_statistics',
          },
          {
            type: 'authors_performance',
            title: 'CMS.authors_performance',
            path: 'authors-performance',
            icon: 'area-chart',
            key: 'menu_user_statistics_columns_performance',
          },
          {
            type: 'columns_performance',
            title: 'CMS.columns_performance',
            path: 'columns-performance',
            icon: 'area-chart',
            key: 'menu_user_statistics_columns_performance',
          },
        ],
      },
      {
        type: 'short_url',
        title: 'CMS.short_url',
        path: 'short-url',
        icon: 'paper-clip',
        key: 'short_url',
      },
      {
        type: 'menu',
        title: 'CMS.menu',
        path: 'menu',
        icon: 'menu',
        key: 'menu',
      },
      {
        type: 'layout',
        title: 'CMS.layout-title',
        path: 'null',
        icon: 'layout',
        key: 'layout',
        children: [
          {
            type: 'layout',
            title: 'CMS.base-layouts',
            path: 'layout/list/base',
            icon: 'layout',
            key: 'layout_base-layouts',
          },
          {
            type: 'layout',
            title: 'CMS.home-layouts',
            path: 'layout/list/homepage',
            icon: 'layout',
            key: 'layout_home-layouts',
          },
          {
            type: 'layout',
            title: 'CMS.column-layouts',
            path: 'layout/list/column',
            icon: 'layout',
            key: 'layout_column-layouts',
          },
          {
            type: 'layout',
            title: 'CMS.opinion-layouts',
            path: 'layout/list/opinion',
            icon: 'layout',
            key: 'layout_opinion-layouts',
          },
          {
            type: 'layout',
            title: 'CMS.sidebar-layouts',
            path: 'layout/list/sidebar',
            icon: 'layout',
            key: 'layout_sidebar-layouts',
          },
          {
            type: 'layout',
            title: 'CMS.columnsidebar-layouts',
            path: 'layout/list/columnsidebar',
            icon: 'layout',
            key: 'layout_column_sidebar-layouts',
          },
          {
            type: 'layout',
            title: 'CMS.custom-built-layouts',
            path: 'layout/list/custombuiltpage',
            icon: 'layout',
            key: 'layout_custom_built_page-layouts',
          },
        ],
      },
      {
        type: 'articles',
        title: 'CMS.articles',
        path: 'null',
        icon: 'file-text',
        key: 'articles',
        children: [
          {
            type: 'articles',
            title: 'CMS.articles',
            path: 'articles',
            key: 'articles_articles',
          },
          {
            type: 'articles',
            title: 'CMS.new-article',
            path: 'articles/page-editor/new',
            queryParams: { type: 'article' },
            key: 'articles_new-article',
          },
        ],
      },
      {
        type: 'comments',
        title: 'CMS.comments',
        path: 'null',
        icon: 'comment',
        key: 'comments',
        children: [
          {
            type: 'comments',
            title: 'CMS.comments',
            path: 'comments',
            key: 'comments',
          },
          {
            type: 'comment-reports',
            title: 'CMS.comment-reports',
            path: 'comment-reports',
            key: 'comment-reports',
          },
          {
            type: 'banned-words',
            title: 'CMS.commenting.banned-words',
            path: 'banned-words',
            key: 'banned-words',
          },
        ],
      },
      {
        type: 'subscriptions',
        title: 'CMS.subscriptions',
        path: null,
        icon: 'dollar',
        key: 'subscriptions',
        children: [
          {
            type: 'calendar',
            title: 'CMS.calendar',
            path: 'subscriptions-calendar',
            icon: 'calendar',
            key: 'calendar',
          },
          {
            type: 'subscription_product',
            title: 'CMS.product',
            path: 'subscriptions-product',
            key: 'subscription_product',
          },
          {
            type: 'marketing_campaign',
            title: 'CMS.marketingCampaign',
            path: 'marketing-campaign',
            key: 'marketing_campaign',
          },
          {
            type: 'subscriptions',
            title: 'CMS.subscriptions',
            path: 'subscriptions-subscriptions',
            key: 'subscriptions',
          },
          {
            type: 'subscriptions_export',
            title: 'CMS.exports',
            key: 'subscriptions_export',
            path: 'subscriptions-export',
          },
          {
            type: 'subscriptions_addressee',
            title: 'CMS.subscriptions_addressee',
            path: 'subscriptions_addressee',
            key: 'subscriptions_addressee',
          },
          {
            type: 'subscription_email_templates',
            title: 'CMS.subscriptionEmailTemplates',
            path: 'subscription-email-templates',
            key: 'subscription_email_templates',
          },
        ],
      },
      {
        type: 'perceptions',
        title: 'CMS.detections.perceptions',
        path: null,
        icon: 'cloud',
        key: 'perceptions',
        children: [
          {
            type: 'perceptions_perceptions',
            title: 'CMS.detections.perceptions',
            path: 'perceptions',
            key: 'perceptions_perceptions',
          },
          {
            type: 'perceptions_banned_words',
            title: 'CMS.detections.banned-words',
            path: 'perceptions-banned-words',
            key: 'perceptions_banned_words',
          },
        ],
      },
      {
        type: 'map',
        title: 'CMS.maps',
        path: 'maps',
        icon: 'read',
        key: 'map',
      },
      {
        type: 'prediction_hourly',
        title: 'CMS.WeatherPredictions.prediction_hourly',
        path: 'content-json/hourly-forecast',
        icon: 'clock-circle',
        key: 'prediction_hourly',
      },
      {
        type: 'prediction_daily',
        title: 'CMS.WeatherPredictions.prediction_daily',
        path: 'content-json/twelve-day-forecast',
        icon: 'calendar',
        key: 'prediction_daily',
      },
      {
        type: 'prediction_text',
        title: 'CMS.WeatherPredictions.prediction_text',
        path: 'content-json/forecast-text',
        icon: 'inbox',
        key: 'prediction_text',
      },
      {
        type: 'popup-notifier',
        title: 'CMS.popup-notifier',
        path: 'popup-notifier',
        icon: 'bell',
        key: 'popup-notifier',
      },
      {
        type: 'orvosmet',
        title: 'CMS.orvosmet',
        path: 'orvosmet',
        icon: 'snippets',
        key: 'orvosmet',
      },
      {
        type: 'news-reception',
        title: 'CMS.news-reception',
        path: 'news-reception',
        icon: 'read',
        key: 'news-reception',
      },
      {
        type: 'seo-meta-tool',
        title: 'CMS.SEOMetaTool.title',
        path: 'null',
        icon: 'file-search',
        key: 'seo-meta-tool',
        children: [
          {
            type: 'seo-meta-tool/urls',
            title: 'CMS.SEOMetaTool.urls',
            path: 'seo-meta-urls',
            key: 'seo-meta-tool',
          },
          {
            type: 'seo-meta-tool/robots',
            title: 'CMS.SEOMetaTool.robots',
            path: 'seo-meta-robots',
            key: 'seo-meta-tool',
          },
        ],
      },
      {
        type: 'gastro_module',
        title: 'Gastro',
        path: 'null',
        icon: 'tags',
        key: 'gastro_module',
        children: [
          {
            type: 'gastro-experience-categories',
            title: 'CMS.gastro-experience-categories',
            path: 'gastro-experience-categories',
            key: 'gastro_experience_category',
          },
          {
            type: 'gastro-experience',
            title: 'CMS.gastro-experience',
            path: 'gastro-experience',
            key: 'gastro_experience',
          },
          {
            type: 'gastro-experience-occasion',
            title: 'CMS.gastro-experience-occasion',
            path: 'gastro-experience-occasion',
            key: 'gastro_experience_occasion',
          },
          {
            type: 'gastro-purchases',
            title: 'CMS.gastro-purchases',
            path: 'gastro-purchases',
            key: 'orders',
          },
        ],
      },
      {
        type: 'branding-boxes',
        title: 'CMS.branding-boxes',
        path: 'null',
        icon: 'appstore',
        key: 'branding-box',
        children: [
          {
            type: 'branding-boxes',
            title: 'CMS.branding-boxes',
            path: 'branding-boxes',
            key: 'branding-box_branding-box',
          },
          {
            type: 'branding-boxes',
            title: 'CMS.new-branding-box',
            path: 'branding-boxes/page-editor',
            queryParams: { type: 'branding-box' },
            key: 'branding-box_new-branding-box',
          },
        ],
      },
      {
        type: 'import-print-xml',
        title: 'CMS.import-print-xml',
        path: 'null',
        icon: 'file-text',
        key: 'import-xml',
        children: [
          {
            type: 'import-print-xml',
            title: 'CMS.import-xml',
            path: 'import-xml',
            key: 'import-xml_import_xml',
          },
          {
            type: 'import-print-xml',
            title: 'CMS.import-approve',
            path: 'import-xml-approve',
            key: 'import-xml_approve',
          },
          {
            type: 'import-print-xml',
            title: 'CMS.import-settings',
            path: 'import-xml/settings',
            key: 'import-xml_settings',
          },
        ],
      },
      {
        type: 'static-pages',
        title: 'CMS.static-pages',
        path: 'static-pages',
        icon: 'file-text',
        key: 'static-page',
      },
      {
        type: 'categories',
        title: 'CMS.categories',
        path: 'categories',
        icon: 'tags',
        key: 'categories',
      },
      {
        type: 'tags',
        title: 'CMS.tags',
        path: 'tags',
        icon: 'tags',
        key: 'tags',
      },
      {
        type: 'pr-tags',
        title: 'CMS.pr-tags',
        path: 'pr-tags',
        icon: 'tags',
        key: 'pr_tags',
      },
      {
        type: 'regions',
        title: 'CMS.regions',
        path: 'regions',
        icon: 'tags',
        key: 'regions',
      },
      {
        type: 'top_ranking_glossary',
        title: 'CMS.top-ranking-glossary',
        path: 'top-ranking-glossary',
        icon: 'star',
        key: 'top_ranking_glossary',
      },
      {
        type: 'sports',
        title: 'CMS.sports',
        path: 'sports',
        icon: 'tags',
        key: 'sports',
      },
      {
        type: 'sport-module',
        title: 'CMS.sport-module',
        path: 'null',
        icon: 'tags',
        key: 'sport_module',
        children: [
          {
            type: 'facilities',
            title: 'CMS.facilities',
            path: 'facilities',
            key: 'sport_facility',
          },
          {
            type: 'liveSports',
            title: 'CMS.liveSports',
            path: 'live-sports',
            key: 'sport_live',
          },
          {
            type: 'scheduleEvents',
            title: 'CMS.scheduleEvents',
            path: 'schedule-events',
            key: 'schedule_events',
          },
          {
            type: 'staff',
            title: 'CMS.staff',
            path: 'staff',
            key: 'sport_staff',
          },
          {
            type: 'staffPositions',
            title: 'CMS.staffPositions',
            path: 'staff-positions',
            key: 'sport_staff_position',
          },
          {
            type: 'playerPositions',
            title: 'CMS.playerPositions',
            path: 'player-positions',
            key: 'sport_player_position',
          },
          {
            type: 'players',
            title: 'CMS.players',
            path: 'players',
            key: 'sport_player',
          },
          {
            type: 'teams',
            title: 'CMS.teams',
            path: 'teams',
            key: 'sport_team',
          },
          {
            type: 'seasons',
            title: 'CMS.seasons',
            path: 'seasons',
            key: 'sport_season',
          },
          {
            type: 'tvStations',
            title: 'CMS.tvStations',
            path: 'tv-stations',
            key: 'sport_tv_station',
          },
          {
            type: 'competitions',
            title: 'CMS.competitions',
            path: 'competitions',
            key: 'sport_competition',
          },
          {
            type: 'phases',
            title: 'CMS.phases',
            path: 'phases',
            key: 'sport_phase',
          },
          {
            type: 'schedules',
            title: 'CMS.schedules',
            path: 'schedules',
            key: 'sport_schedule',
          },
          {
            type: 'athletes',
            title: 'CMS.olympicsAthletes',
            path: 'athletes',
            key: 'sport_olympics_participant',
          },
          {
            type: 'olympics-medals',
            title: 'CMS.olympics-medal',
            path: 'olympics-medals',
            key: 'sport_olympics_participant',
          },
        ],
      },
      {
        type: 'highlighted_items',
        title: 'CMS.highlighted-items',
        path: 'highlighted-items',
        icon: 'unordered-list',
        key: 'highlighted_items',
      },
      {
        type: 'recipes',
        title: 'CMS.recipes',
        path: 'recipes',
        icon: 'file-text',
        key: 'recipe',
      },
      {
        type: 'recipeCategories',
        title: 'CMS.recipeCategories',
        path: 'recipe-categories',
        icon: 'folder',
        key: 'recipe_category',
      },
      {
        type: 'weeklyMenus',
        title: 'CMS.weeklyMenus',
        path: 'weekly-menus',
        icon: 'file-text',
        key: 'weekly_menu',
      },
      {
        type: 'selections',
        title: 'CMS.selections',
        path: 'selections',
        icon: 'folder',
        key: 'selection',
      },
      {
        type: 'ingredients',
        title: 'CMS.ingredients',
        path: 'ingredients',
        icon: 'unordered-list',
        key: 'recipe_ingredient',
      },
      {
        type: 'bestPractices',
        title: 'CMS.bestPractices',
        path: 'best-practices',
        icon: 'unordered-list',
        key: 'bestpractice',
      },
      {
        type: 'allergens',
        title: 'CMS.allergens',
        path: 'allergens',
        icon: 'unordered-list',
        key: 'recipe_allergic',
      },
      // MME maestro-k nem lesznek külön kezelve (lásd: KESMA-11665), ezért kikerült a menüpont
      // {
      //   type: "maestros",
      //   title: "CMS.maestros",
      //   path: "maestros",
      //   icon: "unordered-list",
      //   key: "maestro",
      // },
      {
        type: 'dossiers',
        title: 'CMS.dossiers',
        path: 'dossiers',
        icon: 'folder',
        key: 'dossiers',
      },
      {
        type: 'sponsorships',
        title: 'CMS.sponsorships',
        path: 'sponsorships',
        icon: 'alert',
        key: 'sponsorships',
        children: [
          {
            type: 'sponsorships',
            title: 'CMS.sponsorships',
            path: 'sponsorships',
            key: 'sponsorships',
          },
          {
            type: 'did_you_know',
            title: 'CMS.did-you-know',
            path: 'did-you-know',
            key: 'did_you_know',
          },
        ],
      },
      {
        type: 'videos',
        title: 'CMS.videos',
        path: 'videos',
        icon: 'play-square',
        key: 'videos',
      },
      {
        type: 'podcasts',
        title: 'CMS.podcasts',
        path: 'podcasts',
        icon: 'notification',
        key: 'podcasts',
      },
      {
        type: 'publications',
        title: 'CMS.publications',
        path: 'publications',
        icon: 'snippets',
        key: 'publications',
      },
      {
        type: 'advertisements',
        title: 'CMS.advertisements',
        path: 'advertisements',
        icon: 'notification',
        key: 'commercial',
      },
      {
        type: 'programs',
        title: 'CMS.programs',
        path: 'null',
        icon: 'calendar',
        key: 'programs',
        children: [
          {
            type: 'program-types',
            title: 'CMS.types',
            path: 'program-types',
            key: 'program_program-types',
          },
          {
            type: 'program-locations',
            title: 'CMS.locations',
            path: 'program-locations',
            key: 'program_program-locations',
          },
          {
            type: 'program-recommendations',
            title: 'CMS.program-recommendations',
            path: 'program-recommendations',
            key: 'program_program-recommendations',
          },
        ],
      },
      {
        type: 'journal_issue',
        title: 'CMS.journal_issue',
        path: 'journal_issue',
        icon: 'read',
        key: 'journal_issue',
        // TODO: Children?
      },
      {
        type: 'institutions',
        title: 'CMS.institutions',
        path: 'null',
        icon: 'apartment',
        key: 'institutions',
        children: [
          {
            type: 'institutions',
            title: 'CMS.institutions',
            path: 'institutions',
            key: 'institutions',
          },
          {
            type: 'institution_categories',
            title: 'CMS.institution-categories',
            path: 'institution-categories',
            key: 'institution_categories',
          },
        ],
      },

      {
        type: 'voting',
        title: 'CMS.voting',
        path: 'voting',
        icon: 'ordered-list',
        key: 'voting',
      },
      {
        type: 'multi_vote',
        title: 'CMS.multi_vote',
        path: 'multi-voting',
        key: 'multi-vote',
        icon: 'ordered-list',
      },
      {
        type: 'extraordinary-notification',
        title: 'CMS.extraordinary-notification',
        path: 'extraordinary-notification',
        key: 'extraordinary_notification',
        icon: 'ordered-list',
      },
      {
        type: 'glossary',
        title: this.getGlossaryTitle(),
        path: 'glossary',
        icon: 'ordered-list',
        key: 'glossary',
      },
      {
        type: 'star_dictionary_module',
        key: 'star_dictionary_module',
        path: null,
        title: 'CMS.star-dictionary-module',
        icon: 'star',
        children: [
          {
            type: 'star-stars',
            key: 'star_dictionary_star',
            title: 'CMS.star_dictionary_star',
            path: 'star-stars',
          },
          {
            type: 'star_dictionary_images',
            key: 'star_dictionary_images',
            title: 'CMS.star_dictionary_image',
            icon: 'picture',
            onClick: () => {
              this.imageModal.open$({ imageType: 'starDictionary' });
            },
          },
          {
            type: 'star_dictionary_gallery',
            title: 'CMS.star_dictionary_gallery',
            path: 'star-gallery',
            key: 'star_dictionary_gallery',
          },
          {
            type: 'star-occupations',
            key: 'star_dictionary_occupation',
            title: 'CMS.star_dictionary_occupation',
            path: 'star-occupations',
          },
          {
            type: 'star-birthplaces',
            key: 'star_dictionary_birthplace',
            title: 'CMS.star_dictionary_birthplace',
            path: 'star-birthplaces',
          },
          {
            type: 'star-awards',
            key: 'star_dictionary_award',
            title: 'CMS.star_dictionary_award',
            path: 'star-awards',
          },
        ],
      },
      {
        type: 'media',
        title: 'CMS.mediaStoreImages',
        onClick: () => {
          if (this.portalConfig.isConfigSet(PortalConfigSetting.ENABLE_REFACTORED_GALLERY_FUNCTIONALITY)) {
            this.imageModal.open$();
          } else {
            this.mediaStoreModal.open({ contentType: 'image' });
          }
        },
        icon: 'picture',
        key: 'media',
      },
      {
        type: 'gallery',
        title: 'CMS.mediaStoreGalleries',
        icon: 'picture',
        key: 'gallery',
        ...(() => {
          if (this.portalConfig.isConfigSet(PortalConfigSetting.ENABLE_REFACTORED_GALLERY_FUNCTIONALITY)) {
            return {
              path: 'gallery',
            };
          }
          return {
            onClick: () => this.mediaStoreModal.open({ contentType: 'gallery' }),
          };
        })(),
      },
      {
        type: 'file_upload',
        title: this.domainService.isNemzetiSport() ? 'CMS.files' : 'CMS.mediaStoreFiles',
        onClick: () => this.fileModal.open$(),
        icon: 'file',
        key: 'file_upload',
      },
      {
        type: 'search-static',
        title: 'CMS.search-static',
        path: 'search-static',
        icon: 'area-chart',
        key: 'search-static',
      },
      {
        type: 'settings',
        title: 'CMS.settings',
        path: 'settings',
        icon: 'setting',
        key: 'settings',
      },
      {
        type: 'datastudio',
        title: 'Datastudio',
        path: 'datastudio',
        icon: 'bar-chart',
        key: 'datastudio',
      },
      {
        type: 'log',
        title: 'CMS.entity-log',
        path: 'entity-log',
        icon: 'unordered-list',
        key: 'log',
      },
      {
        type: 'marketplace-main',
        title: 'CMS.marketplace-ads',
        path: 'null',
        icon: 'shopping',
        key: 'marketplace',
        children: [
          {
            type: 'marketplace',
            title: 'CMS.categories-real',
            path: 'marketplace',
            icon: 'shopping',
            key: 'marketplace',
          },
          {
            type: 'marketplace-item',
            title: 'CMS.marketplace-items',
            path: 'marketplace-item',
            icon: 'shopping',
            key: 'marketplace-item',
          },
        ],
      },
      {
        type: 'rolegroup',
        title: 'CMS.role-groups',
        path: 'role-groups',
        icon: 'team',
        key: 'rolegroup',
      },
      {
        type: 'quizzes',
        title: 'CMS.quizzes',
        path: 'quizzes',
        icon: 'unordered-list',
        key: 'quizzes',
      },
      {
        type: 'quiz-categories',
        title: 'CMS.quiz-categories',
        path: 'quiz-categories',
        icon: 'unordered-list',
        key: 'quiz-categories',
      },
      {
        type: 'games',
        title: 'CMS.games',
        path: 'games',
        icon: 'save',
        key: 'games',
      },
      {
        type: 'kpi-article-slot',
        title: 'CMS.kpi-article-slot',
        path: 'kpi-article-slot',
        icon: 'file-text',
        key: 'kpi-article-slot',
      },
      {
        type: 'central-positions',
        title: 'CMS.centralPositions',
        path: 'null',
        icon: 'apartment',
        key: 'central_sending',
        children: [
          {
            type: 'article-network-slot',
            title: 'CMS.articleNetworkSlots',
            path: 'article-network-slots',
            key: 'article_network_slots',
          },
          {
            type: 'voting-network-slot',
            title: 'CMS.votingNetworkSlots',
            path: 'voting-network-slots',
            key: 'voting_network_slots',
          },
          {
            type: 'dossier-network-slot',
            title: 'CMS.dossierNetworkSlots',
            path: 'dossier-network-slots',
            key: 'dossier_network_slots',
          },
        ],
      },
      {
        type: 'content-block-images',
        title: 'CMS.contentBlockImages',
        onClick: () => {
          if (this.portalConfig.isConfigSet(PortalConfigSetting.ENABLE_REFACTORED_GALLERY_FUNCTIONALITY)) {
            this.imageModal.open$({ imageType: 'contentBlockImg' });
          } else {
            this.mediaStoreModal.open({ contentType: 'contentBlockImg', imageType: 'contentBlockImg' });
          }
        },
        icon: 'picture',
        key: 'content_block_images',
      },
      {
        type: 'promotions',
        title: 'CMS.promotions',
        path: 'promotions',
        icon: 'tags',
        key: 'promotion',
      },
      {
        type: 'doctor-answer',
        title: 'CMS.doctorAnswer',
        path: 'doctor-answer',
        icon: 'reconciliation',
        key: 'doctor_answer',
      },
      {
        type: 'secretDaysCalendar',
        title: 'CMS.secretDaysCalendars',
        path: 'calendars',
        icon: 'calendar',
        key: 'secret_days_calendar',
      },
    ];
  }
}
