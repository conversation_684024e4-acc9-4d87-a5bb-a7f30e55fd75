/* eslint-disable @typescript-eslint/explicit-function-return-type */
import { HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { IHttpOptions, ReqService } from '@external/http';
import { StorageService } from '@external/utils';
import { TranslateService } from '@ngx-translate/core';
import { Championship } from '@shared/definitions/championship.definitions';
import { ArticleNetworkSlot } from '@shared/definitions/content-type.definitions';
import { IHttpParams } from '@shared/definitions/shared.definitions';
import { dateToFilterString } from '@shared/utils/date-utils';
import { forceTranslate } from '@shared/utils/form-input.utils';
import {
  ApiListResult,
  ApiResponseMetaList,
  ApiResult,
  backendDateToDate,
  BackendExternalFeedData,
  BackendMultiVoteData,
  BackendPublicAuthor,
  BackendVoteData,
  RecipeCardData,
  Tag,
} from '@trendency/kesma-ui';
import { Observable, of } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { CustomHeaderConfig, IMenuNodeMoveData, MNOSettings } from 'src/app/modules/menu/menu.definitions';
import { ScheduleItem } from 'src/app/modules/schedule/components/schedule.definitions';
import { ContentData } from 'src/app/shared/modules/form-generator/form-generator.definitions';
import { environment } from '../../../environments/environment';
import { PreviewData } from '../../content-page-editor/definitions/content-page-editor.definitions';
import { GoogleRealtimeRecord } from '../../modules/dashboard/dashboard.definitions';
import { DetectionStatus } from '../../modules/detections/detections.definitions';
import { EntityDetail, EntityListItem } from '../../modules/entity-log/entity-log.definitions';
import {
  ContentSelectOption,
  ContentSelectOptionWithPublishDate,
} from '../../modules/layout/components/layout-configurator/definitions/modal-configurator.definitions';
import { Automail, Partner } from '../../modules/partners/partners.definitions';
import { RecipeType } from '../../modules/recipe/definitions/recipes.definitions';
import { TagGeneratorRequest } from '../../modules/tag-generator/definitions/tag-generator-definitions';
import { ColumnData, ColumnWithIcon, ContentResponse, ILoginRequestData, KpiContentType } from '../api.definitons';
import { Article, ArticleReviews, Priority } from '../definitions/article.definitions';
import { DomainService } from '../modules/admin/services/domain.service';
import { UserNotification } from '@core/services/api/notifications/notification.definitions';

const portalRoutes = require('../../../assets/locales/routes.json');

@Injectable({ providedIn: 'root' })
export class ApiService {
  private cache: {
    articleNetworkSlots: ArticleNetworkSlot[];
  } = {
    articleNetworkSlots: [],
  };

  constructor(
    private reqService: ReqService,
    private storageService: StorageService,
    private translate: TranslateService,
    private sanitizer: DomSanitizer,
    private domainService: DomainService
  ) {}

  public get contentLang() {
    let contentLang = this.storageService.getSessionStorageData('contentLang');
    contentLang = contentLang === null ? 'hu' : contentLang;
    return contentLang;
  }

  public get cmsLang() {
    return this.translate.currentLang;
  }

  // Init

  public getInit() {
    return this.reqService.get('api/init');
  }

  public getLoginInit() {
    return this.reqService.get('api/login-init');
  }

  public get isMegyeiLap() {
    return this.domainService.isMegyeiLap(this.domainService.currentDomain.key);
  }

  // Auth
  public login(data: ILoginRequestData) {
    return this.reqService.post<{ token: string }>('api/login_check', data).pipe(
      tap((res) => {
        this.storageService.setCookie('jwt_token', res.token, null, environment.baseHost ? `.${environment.baseHost}` : null);
      })
    );
  }

  public isLoggedIn() {
    return this.reqService.get(`api/current-user/is-logged-in`);
  }

  public logout() {
    return this.reqService.post('api/logout', {});
  }

  // Password recovery

  requestPasswordRecovery(email: string): Observable<{ message: string }> {
    return this.reqService.post('password-recovery', { email });
  }

  confirmPasswordRecovery(
    token: string,
    plainPassword: string,
    plainPasswordRepeated: string
  ): Observable<{
    email: string;
  }> {
    return this.reqService.post(`password-recovery/${token}`, {
      plainPassword,
      plainPasswordRepeated,
    });
  }

  // Content page
  public getContentPageList(pageType: string, options?: IHttpOptions) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/${pageType}?dev`, options);
  }

  public getContentPageFormInfo(pageType: string, id: string) {
    if (id) {
      return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/${pageType}/${id}`);
    } else {
      return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/${pageType}`);
    }
  }

  public getContentPage(pageType: string, id: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/${pageType}/${id}`);
    // return of(mockContentPage);
  }

  public initContentPage(pageType: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/${pageType}/create`);
  }

  public getContentFormInfo(pageType: string, id: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/${pageType}/${id}`);
    } else {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/${pageType}`);
    }
  }

  public createCalendar(calendarType: string, contentData: ContentResponse) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/content-page/calendar/create?type=${calendarType}`, contentData);
  }

  public getCalendar(id: string, calendarType?: string) {
    if (!id && calendarType) {
      return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/calendar/create?type=${calendarType}`);
    }

    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/calendar/${id}/show`);
  }

  public updateCalendar(id: string, contentData: ContentResponse) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/content-page/calendar/${id}/update?destroy=1`, contentData);
  }

  public deleteCalendar(id: string) {
    return this.reqService.delete(`api/${this.cmsLang}/${this.contentLang}/content-page/calendar/${id}/delete`);
  }

  public restoreCalendar(id: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/content-page/calendar/${id}/restore`, {});
  }

  public createContentPageFromBasiItem(pageType: string, contentData: ContentResponse) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/content-page/${pageType}`, contentData);
  }

  public createContentPage(pageType: string, contentData: ContentResponse) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/content-page/${pageType}/create`, contentData);
  }

  public updateContentPage(pageType: string, id: string, contentData: ContentResponse, isFinal: boolean = false, shouldReinitComponents: boolean = false) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/content-page/${pageType}/${id}`, contentData, {
      params: {
        ...(isFinal ? { saveFinal: '1' } : null),
        ...(shouldReinitComponents ? { destroy: '1' } : null),
      },
    });
  }

  public activateContentPage(id: string, pageType: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/content-page/${pageType}/${id}/to-active`, {});
  }

  public inactivateContentPage(id: string, pageType: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/content-page/${pageType}/${id}/to-inactive`, {});
  }

  public activateTemplatePage(id: string, pageType: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/layout-editor/${pageType}/layout/${id}/to-active`, {});
  }

  public inactivateTemplatePage(id: string, pageType: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/layout-editor/${pageType}/layout/${id}/to-inactive`, {});
  }

  public getJournalIssueSource() {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/content-group/journal-issue`);
  }

  public getJournalIssue(id: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-group/journal-issue/${id}/view`);
  }

  public activateJournal(id: string, pageType: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/content-group/${pageType}/${id}/to-active`, {});
  }

  public inactivateJournal(id: string, pageType: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/content-group/${pageType}/${id}/to-inactive`, {});
  }

  public deleteContentPage(id: string, pageType: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/content-page/${pageType}/${id}/delete`, {});
  }

  public deleteLayoutPage(id: string, pageType: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/layout-editor/layout/${id}/delete`, {});
  }

  public deleteLayoutListPage(id: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/layout-editor/template/${id}/delete`, {});
  }

  public restoreLayoutPage(id: string, pageType: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/layout-editor/layout/${id}/restore`, {});
  }

  public restoreLayoutListPage(id: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/layout-editor/template/${id}/restore`, {});
  }

  public copyLayoutTemplate(id: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/layout-editor/template/${id}/copy`, {});
  }

  public copyAsOnlineContentPage(id: string, itemType: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/content-page/${itemType}/${id}/copy-as-online`, {});
  }

  public copyAsPrintContentPage(id: string, itemType: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/content-page/${itemType}/${id}/copy-as-print`, {});
  }

  public copyAsTranslationContentPage(id: string, itemType: string, language: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/content-page/${itemType}/${id}/copy-as-translation/${language}`, {});
  }

  public copyToClipboardContentPage(id: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/copy-article/${id}`, {});
  }

  public copyToClipboardAndStatusContentPage(id: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/copy-article-and-set-print-status/${id}`, {});
  }

  public restoreContentPage(id: string, itemType: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/content-page/${itemType}/${id}/restore`, {});
  }

  public lockContentPage(id: string, itemType: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/content-page/${itemType}/${id}/lock`, {});
  }

  public unlockContentPage(id: string, itemType: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/content-page/${itemType}/${id}/unlock`, {});
  }

  public forceUnlockContentPage(id: string, itemType: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/content-page/${itemType}/${id}/force-unlock`, {});
  }

  public forceUnlockLayoutPage(id: string, layoutType: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/layout-editor/${layoutType}/${id}/force-unlock`, {});
  }

  // Minute to minute

  public getMinuteToMinuteBlocks(contentId: string, itemType: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/${itemType}/${contentId}/minute-to-minute-blocks`);
  }

  public getMinuteToMinuteBlockScheme(contentId: string, itemType: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/${itemType}/${contentId}/minute-to-minute-block`);
  }

  public getMinuteToMinuteBlock(contentId: string, itemType: string, blockId: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/${itemType}/${contentId}/minute-to-minute-block/${blockId}`);
  }

  public updateMinuteToMinuteBlock(contentId: string, itemType: string, blockId: string, data: any) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/content-page/${itemType}/${contentId}/minute-to-minute-block/${blockId}`, data, {
      params: {
        destroy: '1',
      },
    });
  }

  public createMinuteToMinuteBlock(contentId: string, itemType: string, data: any) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/content-page/${itemType}/${contentId}/minute-to-minute-block`, data);
  }

  public deleteMinuteToMinuteBlock(contentId: string, itemType: string, blockId: string) {
    return this.reqService.delete(`api/${this.cmsLang}/${this.contentLang}/content-page/${itemType}/${contentId}/minute-to-minute-block/${blockId}`);
  }

  // Reviewable

  public getReviews(reviewedArticleId: string): Observable<ApiListResult<ArticleReviews>> {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/content-page/reviewed-article/${reviewedArticleId}/reviews`, {
      params: {
        rowCount_limit: '100',
      },
    });
  }

  public getReviewArticleForm(articleId: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/reviewed-article/${articleId}/review-article/create`);
  }

  public getReviewArticle(reviewArticleId: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/review-article/${reviewArticleId}/update`);
  }

  public createReviewedArticle(reviewedArticleId: string, data: any) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/content-page/reviewed-article/${reviewedArticleId}/review-article/create`, data);
  }

  public updateReviewArticle(reviewArticleId: string, data: any) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/content-page/review-article/${reviewArticleId}/update?destroy=1`, data);
  }

  public orderReviewArticle(reviewArticleId: string, targetReviewOrder: number) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/content-page/review-article/${reviewArticleId}/move-to/${targetReviewOrder}`, {});
  }

  // Basic Item
  public getBasicItemList(itemType: string, options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/${itemType}`, options);
  }

  public getBasicItem(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/${itemType}/${id}`);
  }

  public getBasicItemView(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/${itemType}/${id}/view`);
  }

  public getBasicItemFormInfo(itemType: string, id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/${itemType}/${id}`);
    } else {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/${itemType}`);
    }
  }

  public createBasicItem(itemType: string, formData: any) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/${itemType}`, formData);
  }

  public updateBasicItem(itemType: string, id: string, formData: any) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/${itemType}/${id}`, formData);
  }

  public deleteBasicItem(itemType: string, id: string) {
    return this.reqService.delete(`/api/${this.cmsLang}/${this.contentLang}/${itemType}/${id}`);
  }

  public restoreBasicItem(itemType: string, id: string) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/${itemType}/${id}/restore`, {});
  }

  public sortBasicItem(itemType: string, id: string, target: number) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/${itemType}/${id}/sort`, { target });
  }

  public activateBasicItem(itemType: string, id: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/${itemType}/${id}/to-active`, {});
  }

  public inactivateBasicItem(itemType: string, id: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/${itemType}/${id}/to-inactive`, {});
  }

  public activateCmsUser(itemType: string, id: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/${itemType}/${id}/activate`, {});
  }

  public inactivateCmsUser(itemType: string, id: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/${itemType}/${id}/inactivate`, {});
  }

  // ContentGroup

  public getContentGroupList(itemType: string, options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}`, options);
  }

  public getContentGroupListSuffixed(itemType: string, options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/list`, options);
  }

  public getContentGroup(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/${id}`);
  }

  public getContentGroupView(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/${id}/view`);
  }

  public getContentGroupFormInfo(itemType: string, id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/${id}`);
    } else {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}`);
    }
  }

  public getContentGroupFormInfoSuffixed(itemType: string, id?: string, translateKeys?: string | string[]) {
    const mainUrl = `/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}`;
    const url = id ? `${mainUrl}/${id}/show` : `${mainUrl}/create`;
    const req = this.reqService.get(url);

    if (translateKeys) {
      return req.pipe(forceTranslate(translateKeys));
    }

    return req;
  }

  public createContentGroup(itemType: string, formData: any) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}`, formData);
  }

  public createContentGroupSuffixed(itemType: string, formData: any) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/create`, formData);
  }

  public updateContentGroup(itemType: string, id: string, formData: any) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/${id}?destroy=1`, formData);
  }

  public updateContentGroupSuffixed(itemType: string, id: string, formData: any) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/${id}/update`, formData);
  }

  public deleteContentGroup(itemType: string, id: string, method: 'delete' | 'patch' = 'patch') {
    return this.reqService[method](`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/${id}/delete`, {});
  }

  public restoreContentGroup(itemType: string, id: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/${id}/restore`, {});
  }

  public sortContentGroup(itemType: string, id: string, target: number) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/${id}/sort`, { target });
  }

  public activateContentGroup(itemType: string, id: string, method: 'post' | 'patch' = 'post') {
    return this.reqService[method](`api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/${id}/to-active`, {});
  }

  public inactivateContentGroup(itemType: string, id: string, method: 'post' | 'patch' = 'post') {
    return this.reqService[method](`api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/${id}/to-inactive`, {});
  }

  public publishContentGroup(itemType: string, id: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/${id}/public`, {});
  }

  public unpublishContentGroup(itemType: string, id: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/${id}/nonpublic`, {});
  }

  public getVotingList(itemType: string, options?: IHttpOptions, isMulti = false) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/${isMulti ? 'multi-vote/votes' : 'voting/' + itemType}/`, options);
  }

  public createVoting(itemType: string, formData: any, isMulti = false) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/${isMulti ? 'multi-vote' : 'voting'}/${itemType}`, formData);
  }

  public updateVoting(itemType: string, id: string, formData: any, isMulti = false) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/${isMulti ? 'multi-vote' : 'voting'}/${itemType}/${id}`, formData);
  }

  public getVotingFormInfo(itemType: string, id = '', isMulti = false) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/${isMulti ? 'multi-vote' : 'voting'}/${itemType}/${id ?? ''}`);
  }

  public activateVoting(itemType: string, id: string, isMulti = false) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/${itemType}/${isMulti ? 'multi-vote/' : ''}${id}/to-active`, {});
  }

  public restoreVoting(itemType: string, id: string, isMulti = false) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/${itemType}/${isMulti ? 'multi-vote/' : ''}${id}/restore`, {});
  }

  public inactivateVoting(itemType: string, id: string, isMulti = false) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/${itemType}/${isMulti ? 'multi-vote/' : ''}${id}/to-inactive`, {});
  }

  public deleteVoting(itemType: string, id: string, isMulti = false) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/${itemType}/${isMulti ? 'multi-vote/' : ''}${id}/delete`, {});
  }

  public getAnswerFormInfo(isMulti = false) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/${isMulti ? 'multi-vote' : 'voting'}/answer`);
  }

  // media

  public getMediaList(itemType: string, options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/media/${itemType}`, options);
  }

  public getMedia(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/media/${itemType}/${id}`);
  }

  public getMediaView(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/media/${itemType}/${id}/view`);
  }

  public getMediaFormInfo(itemType: string, id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/media/${itemType}/${id}`);
    } else {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/media/${itemType}`);
    }
  }

  public createMedia(itemType: string, formData: any) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/media/${itemType}`, formData);
  }

  public updateMedia(itemType: string, id: string, formData: any) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/media/${itemType}/${id}`, formData);
  }

  public deleteMedia(itemType: string, id: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/media/${itemType}/${id}/delete`, {});
  }

  public restoreMedia(itemType: string, id: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/media/${itemType}/${id}/restore`, {});
  }

  public sortMedia(itemType: string, id: string, target: number) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/media/${itemType}/${id}/sort`, { target });
  }

  public activateMedia(itemType: string, id: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/media/${itemType}/${id}/to-active`, {});
  }

  public publishMedia(itemType: string, id: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/media/${itemType}/${id}/public`);
  }

  public unPublishMedia(itemType: string, id: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/media/${itemType}/${id}/nonpublic`);
  }

  public inactivateMedia(itemType: string, id: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/media/${itemType}/${id}/to-inactive`, {});
  }

  // program

  public getProgramList(itemType: string, options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/program/${itemType}`, options);
  }

  public getProgram(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/program/${itemType}/${id}`);
  }

  public getProgramView(id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/program/program-recommendation/${id}/view`);
  }

  public getProgramFormInfo(itemType: string, id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/program/${itemType}/${id}`);
    } else {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/program/${itemType}`);
    }
  }

  public createProgram(itemType: string, formData: any) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/program/${itemType}`, formData);
  }

  public updateProgram(itemType: string, id: string, formData: any) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/program/${itemType}/${id}?destroy=1`, formData);
  }

  public deleteProgram(itemType: string, id: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/program/${itemType}/${id}/delete`, {});
  }

  public restoreProgram(itemType: string, id: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/program/${itemType}/${id}/restore`, {});
  }

  public sortProgram(itemType: string, id: string, target: number) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/program/${itemType}/${id}/sort`, { target });
  }

  public activateProgram(itemType: string, id: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/program/${itemType}/${id}/to-active`, {});
  }

  public inactivateProgram(itemType: string, id: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/program/${itemType}/${id}/to-inactive`, {});
  }

  public highlightProgram(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/program/${itemType}/${id}/highlight`, {});
  }

  public unhighlightProgram(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/program/${itemType}/${id}/unhighlight`, {});
  }

  // portal

  public getPortalList(itemType: string, options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/portal/${itemType}`, options);
  }

  public getPortal(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/portal/${itemType}/${id}`);
  }

  public getPortalView(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/portal/${itemType}/${id}/view`);
  }

  public getPortalFormInfo(itemType: string, id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/portal/${itemType}/${id}`);
    } else {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/portal/${itemType}`);
    }
  }

  public createPortal(itemType: string, formData: any) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/portal/${itemType}`, formData);
  }

  public updatePortal(itemType: string, id: string, formData: any) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/portal/${itemType}/${id}`, formData);
  }

  public deletePortal(itemType: string, id: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/portal/${itemType}/${id}/delete`, {});
  }

  public sortPortal(itemType: string, id: string, target: number) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/portal/${itemType}/${id}/sort`, { target });
  }

  public activatePortal(itemType: string, id: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/portal/${itemType}/${id}/to-active`, {});
  }

  public inactivatePortal(itemType: string, id: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/portal/${itemType}/${id}/to-inactive`, {});
  }

  public restorePortal(itemType: string, id: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/portal/${itemType}/${id}/restore`, {});
  }

  // Marketplace Items
  public getMarketplaceItemsList(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/marketplace-items`, options);
  }

  public getMarketplaceItem(id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/marketplace-item/${id}`);
  }

  public getMarketplaceItemView(id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/marketplace-item/${id}/view`);
  }

  public getMarketplaceItemFormInfo(id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/marketplace-item/${id}`);
    } else {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/marketplace-item`);
    }
  }

  public createMarketplaceItem(formData: any) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/marketplace-item`, formData);
  }

  public updateMarketplaceItem(id: string, formData: any) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/marketplace-item/${id}?destroy=1`, formData);
  }

  public deleteMarketplaceItem(id: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/marketplace-item/${id}/delete`, {});
  }

  public sortMarketplaceItem(id: string, target: number) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/marketplace-item/${id}/sort`, { target });
  }

  public restoreMarketplaceItem(id: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/marketplace-item/${id}/restore`, {});
  }

  // Marketplace

  public getMarketplaceCategoriesList(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/marketplace-categories`, options);
  }

  public getMarketplaceCategory(id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/marketplace-category/${id}`);
  }

  public getMarketplaceCategoryView(id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/marketplace-category/${id}/view`);
  }

  public getMarketplaceCategoryFormInfo(id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/marketplace-category/${id}`);
    } else {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/marketplace-category`);
    }
  }

  public createMarketplaceCategory(formData: any) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/marketplace-category`, formData);
  }

  public updateMarketplaceCategory(id: string, formData: any) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/marketplace-category/${id}`, formData);
  }

  public deleteMarketplaceCategory(id: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/marketplace-category/${id}/delete`, {});
  }

  public sortMarketplaceCategory(id: string, target: number) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/marketplace-category/${id}/sort`, { target });
  }

  public restoreMarketplaceCategory(id: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/marketplace-category/${id}/restore`, {});
  }

  public customMarketplaceCategory(id: string, options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/marketplace-items/category-list/${id}`, options);
  }

  // Instituions

  public getInstitutionCategoryFormInfo(itemType: string, id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/${id}`);
    } else {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/create`);
    }
  }

  public getInstitutionCategoriesList(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/content-group/institution-categories`, options);
  }

  public createInstitutionCategory(itemType: string, formData: any) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/create`, formData);
  }

  public updateInstitutionCategory(itemType: string, id: string, formData: any) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/${id}`, formData);
  }

  public deleteInstitutionCategory(itemType: string, id: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/${id}/delete`, {});
  }

  public restoreInstitutionCategory(itemType: string, id: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/${id}/restore`, {});
  }

  public getPublicAuthors(options?: IHttpOptions, includeAvatar: boolean = true) {
    return this.reqService.get<ApiListResult<BackendPublicAuthor>>(`/api/${this.cmsLang}/${this.contentLang}/source/public-authors`, {
      params: {
        ...options.params,
        includeAvatar: includeAvatar ? '1' : '0',
      },
    });
  }

  // Setting

  public getSettingsList(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/settings`, options);
  }

  public getSetting(id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/setting/${id}`);
  }

  public getSettingView(id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/setting/${id}/view`);
  }

  public getSettingFormInfo(id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/setting/${id}`);
    } else {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/setting`);
    }
  }

  public createSetting(contentData: ContentData) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/setting`, contentData);
  }

  public updateSetting(id: string, contentData: ContentData) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/setting/${id}`, contentData);
  }

  public getSettingContentGroupFormInfo(id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/setting/${id}`);
    } else {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/setting`);
    }
  }

  public deleteSetting(id: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/setting/${id}/delete`, {});
  }

  public sortSettings(id: string, target: number) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/settings/${id}/sort`, { target });
  }

  public restoreSetting(id: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/setting/${id}/restore`, {});
  }

  // article-network-slot

  public getNetworkSlotList(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/network-slot/article-network-slots`, options);
  }

  public getNetworkSlot(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}/${id}`);
  }

  public getNetworkSlotView(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}/${id}/view`);
  }

  public getNetworkSlotFormInfo(itemType: string, id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}/${id}`);
    } else {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}`);
    }
  }

  public createNetworkSlot(itemType: string, contentData: ContentData) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}`, contentData);
  }

  public updateNetworkSlot(itemType: string, id: string, contentData: ContentData) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}/${id}`, contentData);
  }

  public sortNetworkSlot(itemType: string, id: string, target: number) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}/${id}/sort`, { target });
  }

  // dossier-network-slot

  public getDossierNetworkSlotList(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/network-slot/dossier-network-slots`, options);
  }

  public getDossierNetworkSlot(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}/${id}`);
  }

  public getDossierNetworkSlotView(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}/${id}/view`);
  }

  public getDossierNetworkSlotFormInfo(itemType: string, id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}/${id}`);
    } else {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}`);
    }
  }

  public createDossierNetworkSlot(itemType: string, contentData: ContentData) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}`, contentData);
  }

  public updateDossierNetworkSlot(itemType: string, id: string, contentData: ContentData) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}/${id}`, contentData);
  }

  public sortDossierNetworkSlot(itemType: string, id: string, target: number) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}/${id}/sort`, { target });
  }

  // voting-network-slot

  public getVotingSlotList(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/network-slot/voting-network-slots`, options);
  }

  public getVotingSlot(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}/${id}`);
  }

  public getVotingSlotView(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}/${id}/view`);
  }

  public getVotingSlotFormInfo(itemType: string, id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}/${id}`);
    } else {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}`);
    }
  }

  public createVotingSlot(itemType: string, contentData: ContentData) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}`, contentData);
  }

  public updateVotingSlot(itemType: string, id: string, contentData: ContentData) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}/${id}`, contentData);
  }

  public sortVotingSlot(itemType: string, id: string, target: number) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/network-slot/${itemType}/${id}/sort`, { target });
  }

  public getDatastudioLink() {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/datastudio`, {});
  }

  public createArticleByParams(params: any) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/content-page/create-article-by-params`, params);
  }

  // Cms Felhasználó
  public getCmsUserList(options?: IHttpOptions) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/users?dev`, options);
  }

  public deleteCmsUser(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/${itemType}/${id}/delete`, {});
  }

  public restoreCmsUser(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/${itemType}/${id}/restore`, {});
  }

  public getCmsUserPermissions(id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/user/${id}/permission`, {});
  }

  public updateCmsUserPermissions(id: string, formData: any) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/user/${id}/permission`, formData);
  }

  // Global recommendation (Erről se maradj le)
  public getGlobalRecommendationList(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/recommendation/global/items`, options);
  }

  public getGlobalRecommendation(id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/recommendation/global/item/${id}`);
  }

  public getGlobalRecommendationFormInfo(id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/recommendation/global/item/${id}`);
    } else {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/recommendation/global/item`);
    }
  }

  public createGlobalRecommendation(formData: any) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/recommendation/global/item`, formData);
  }

  public updateGlobalRecommendation(id: string, formData: any) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/recommendation/global/item/${id}`, formData);
  }

  public deleteGlobalRecommendation(id: string) {
    return this.reqService.delete(`/api/${this.cmsLang}/${this.contentLang}/recommendation/global/item/${id}`);
  }

  public sortGlobalRecommendation(id: string, target: number) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/recommendation/global/item/${id}/sort`, { target });
  }

  // By column recommendation (Lap végi ajánló)
  public getRecommendationByColumnList(columnId: number, options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/recommendation/column/${columnId}/items`, options);
  }

  public getRecommendationByColumn(columnId: number, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/recommendation/column/${columnId}/item/${id}`);
  }

  public getRecommendationByColumnFormInfo(columnId: number, id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/recommendation/column/${columnId}/item/${id}`);
    } else {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/recommendation/column/${columnId}/item`);
    }
  }

  public createRecommendationByColumn(columnId: number, formData: any) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/recommendation/column/${columnId}/item`, formData);
  }

  public updateRecommendationByColumn(columnId: number, id: string, formData: any) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/recommendation/column/${columnId}/item/${id}`, formData);
  }

  public deleteRecommendationByColumn(columnId: number, id: string) {
    return this.reqService.delete(`/api/${this.cmsLang}/${this.contentLang}/recommendation/column/${columnId}/item/${id}`);
  }

  public sortRecommendationByColumn(columnId: number, id: string, target: number) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/recommendation/column/${columnId}/item/${id}/sort`, { target });
  }

  // Preview
  public generatePreviewHash(id: string, type: string) {
    // 24h-ig érvényes
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/${type}/${id}/preview/generate`);
  }

  public generatePreviewHashNew(id: string, type: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/${type}/${id}/preview/generate`);
  }

  public getPublicPageRoute(publicPageRoute: string[]) {
    return publicPageRoute.map((route) => portalRoutes[this.contentLang][route]);
  }

  // Képkezelő
  public getImages(query: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/media/images${query}`);
  }

  public getImageCrops(imageid: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/media/image-variants-of-image/${imageid}`);
  }

  public getOriginalImage(cropid: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/media/original-variant-of-image-variant/${cropid}`);
  }

  public uploadImage(formData: FormData) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/media/upload`, formData);
  }

  /*
  public getUploadImageUrl() {
    const url = this.reqService.resolveUrl(
      `api/${this.cmsLang}/${this.contentLang}/media/upload`
    );
    return url;
  }
*/
  public uploadFormImage(formData: FormData, context?: string, contextid?: string | number) {
    if (context && contextid) {
      return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/media/upload/${context}/${contextid}`, formData);
    } else {
      return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/media/upload`, formData);
    }
  }

  public uploadImageAsBase64(formData: FormData) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/media/upload-as-base64`, formData);
  }

  /*public cropImage(imageId: string, data: IImageCropInfo) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/media/crop/${imageId}`, data);
  }*/

  public deleteImage(imageid: string) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/media/delete/${imageid}`, {});
  }

  public getImageDataForm(imageid: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/media/image/${imageid}/data-form`);
  }

  public setImageDataForm(imageid: string, data) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/media/image/${imageid}/data-form`, data);
  }

  public uploadDocument(formData: FormData) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/document/file-upload`, formData);
  }

  /*
  public getUploadDocumentUrl() {
    const url = this.reqService.resolveUrl(
      `api/${this.cmsLang}/${this.contentLang}/document/file-upload`
    );
    return url;
  }
*/

  // MENU
  public getMenuList(groupKey?: string) {
    let options: IHttpOptions = {};
    if (groupKey) {
      options = {
        ...options,
        params: {
          only: groupKey,
        },
      };
    }
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/menu/tree`, options);
  }

  public getMenuItemFormInfo(id?: string) {
    if (id) {
      return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/menu/group/header/item/${id}`);
    } else {
      return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/menu/group/header/item`);
    }
  }

  public moveMenuItem(id: string, data: IMenuNodeMoveData) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/menu/item/${id}/move`, data);
  }

  public getMenuItemData(groupKey: string, id?: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/menu/group/${groupKey}/item${id ? `/${id}` : ''}`);
  }

  public saveMenuItemData(groupKey: string, data: any, id?: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/menu/group/${groupKey}/item${id ? `/${id}` : ''}`, data);
  }

  public createMenuItem(groupKey: string, data: any) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/menu/group/${groupKey}/item`, data);
  }

  public deleteMenuItem(id: string) {
    return this.reqService.delete(`api/${this.cmsLang}/${this.contentLang}/menu/item/${id}/delete`);
  }

  public getMenuSettings() {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/menu/settings`);
  }

  public updateMenuSettings(data: MNOSettings) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/menu/settings`, data);
  }

  // LAYOUT segéd hívások selectekhez
  // TODO: review
  public getColumns(searchTerm: string = null, limit: number = 10, options?: IHttpOptions): Observable<ApiResult<ColumnData[], ApiResponseMetaList>> {
    const params: Record<string, string> = {};

    if (options) {
      Object.assign(params, { ...options.params });
    }
    if (this.domainService.isCurrent('mandiner')) {
      params['include_hidden_filter'] = '1';
    }

    return this.getDataWithOptionalGlobalFilter<ApiResult<ColumnData[], ApiResponseMetaList>>(
      `api/${this.cmsLang}/${this.contentLang}/source/content-group/columns`,
      searchTerm,
      limit,
      params
    );
  }

  public getColumnsWithIcons(
    searchTerm: string = null,
    columnId?: string,
    limit = 10,
    options?: IHttpOptions
  ): Observable<ApiResult<ColumnWithIcon[], ApiResponseMetaList>> {
    const params: Record<string, string> = {};

    if (columnId) {
      params.column_filter = columnId;
    }

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.getDataWithOptionalGlobalFilter(`api/${this.cmsLang}/${this.contentLang}/source/content-group/columns-with-icon`, searchTerm, limit, params);
  }

  public getOnlineAndPrintColumns(searchTerm: string = null, limit: number = 200, advanced = false) {
    const url = `api/${this.cmsLang}/${this.contentLang}/source/content-group/columns`;
    const params: Record<string, string> = { rowCount_limit: limit.toString() };
    if (!advanced) {
      params.printOnly = '0';
    }
    if (this.domainService.isCurrent('mandiner')) {
      params['include_hidden_filter'] = '1';
    }
    if (searchTerm) {
      params.global_filter = searchTerm;
    }
    return this.reqService.get(url, { params });
  }

  public getCustomBuiltPages(searchTerm: string = null, limit: number = 10) {
    return this.getDataWithOptionalGlobalFilter(`api/${this.cmsLang}/${this.contentLang}/source/content-page/custom-built-pages`, searchTerm, limit);
  }

  public getTags(searchTerm: string = null, limit: number = 10, options?: IHttpOptions) {
    const params: Record<string, string> = {};

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.getDataWithOptionalGlobalFilter(`api/${this.cmsLang}/${this.contentLang}/source/content-group/tags`, searchTerm, limit, params);
  }

  getFoundationTags(searchTerm: string = null, limit: number = 9999): Observable<ApiResult<Tag[], ApiResponseMetaList>> {
    const params: Record<string, string> = {};

    if (searchTerm) {
      params.global_filter = searchTerm;
    }

    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/source/content-group/foundation-tags`, {
      params: {
        ...params,
        rowCount_limit: `${limit}`,
      },
    });
  }

  public getTypes(searchTerm: string = null, limit: number = 10, options?: IHttpOptions) {
    const params: Record<string, string> = {};

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.getDataWithOptionalGlobalFilter(`api/${this.cmsLang}/${this.contentLang}/source/program-types`, searchTerm, limit, params);
  }

  public getLocations(searchTerm: string = null, limit: number = 10, options?: IHttpOptions) {
    const params: Record<string, string> = {};

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.getDataWithOptionalGlobalFilter(`api/${this.cmsLang}/${this.contentLang}/source/program-locations`, searchTerm, limit, params);
  }

  public getQuizzes(searchTerm: string = null, limit: number = 10, options?: IHttpOptions) {
    const params: Record<string, string> = {};

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.getDataWithOptionalGlobalFilter(`api/${this.cmsLang}/${this.contentLang}/source/content-group/quizzes`, searchTerm, limit, params);
  }

  public getDossiersList(searchTerm?: string, params?: Record<string, string>) {
    return this.getDataWithOptionalGlobalFilter(`api/${this.cmsLang}/${this.contentLang}/source/content-group/dossiers`, searchTerm, 10, params);
  }

  public getRegions(searchTerm?: string, limit: number = 10, options?: IHttpOptions) {
    const params: Record<string, string> = {};

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.getDataWithOptionalGlobalFilter(`api/${this.cmsLang}/${this.contentLang}/source/content-group/regions`, searchTerm, limit, params);
  }

  public getSports(searchTerm?: string, limit: number = 10, options?: IHttpOptions) {
    const params: Record<string, string> = {};

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.getDataWithOptionalGlobalFilter(`api/${this.cmsLang}/${this.contentLang}/source/content-group/sports`, searchTerm, limit, params);
  }

  public getPrTags(searchTerm?: string, limit: number = 10, options?: IHttpOptions) {
    const params: Record<string, string> = {};

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.getDataWithOptionalGlobalFilter(`api/${this.cmsLang}/${this.contentLang}/source/content-group/pr-tags`, searchTerm, limit, params);
  }

  public getSponsors(searchTerm: string = null, limit: number = 10, options?: IHttpOptions) {
    const params: Record<string, string> = {};

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.getDataWithOptionalGlobalFilter(`api/${this.cmsLang}/${this.contentLang}/source/content-group/sponsorships`, searchTerm, limit, params);
  }

  public getArticleNetworkSlots(
    searchTerm: string = null,
    fromCache = false
  ): Observable<{
    data: ArticleNetworkSlot[];
  }> {
    if (fromCache && this.cache.articleNetworkSlots.length) {
      return of({ data: this.cache.articleNetworkSlots });
    }
    return this.getDataWithOptionalGlobalFilter<{ data: ArticleNetworkSlot[] }>(
      `api/${this.cmsLang}/${this.contentLang}/source/network-slot/article-network-slots`,
      searchTerm
    ).pipe(
      tap(({ data }) => {
        // searchTerm caching is not implemented so we should avoid storing such results
        if (!searchTerm) {
          this.cache.articleNetworkSlots = data;
        }
      })
    );
  }

  public getVotingNetworkSlots(searchTerm?: string) {
    return this.getDataWithOptionalGlobalFilter(`api/${this.cmsLang}/${this.contentLang}/source/network-slot/voting-network-slots`, searchTerm);
  }

  public getDossierNetworkSlots(searchTerm?: string, limit: number = 10, options?: IHttpOptions) {
    const params: Record<string, string> = {};

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.getDataWithOptionalGlobalFilter(`api/${this.cmsLang}/${this.contentLang}/source/network-slot/dossier-network-slots`, searchTerm, limit, params);
  }

  public getAuthors(searchTerm?: string) {
    return this.getDataWithOptionalGlobalFilter(`api/${this.cmsLang}/${this.contentLang}/source/user/authors`, searchTerm, 10);
  }

  public getAuthorsWithOptions(options?: IHttpParams) {
    const params: Record<string, string> = {};

    if (options) {
      Object.assign(params, { ...options });
    }

    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/source/user/authors`, { params });
  }

  public getContributors(searchTerm?: string, limit: number = 10) {
    return this.getDataWithOptionalGlobalFilter(`api/${this.cmsLang}/${this.contentLang}/source/user/authors-contributors`, searchTerm, limit);
  }

  public getContributorsList(searchTerm?: string, limit: number = 10) {
    return this.getDataWithOptionalGlobalFilter(`api/${this.cmsLang}/${this.contentLang}/source/contributors/form_list`, searchTerm, limit);
  }

  public getContributorsWithOptions(options?: IHttpParams) {
    const params: Record<string, string> = {};

    if (options) {
      Object.assign(params, { ...options });
    }

    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/source/user/authors-contributors`, { params });
  }

  public getExternalUsers(options?: IHttpOptions) {
    const params: Record<string, string> = {};

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/source/external-users`, { params: { ...params } });
  }

  public getPrograms(searchTerm: string, options?: IHttpOptions) {
    const params: Record<string, string> = {
      global_filter: searchTerm,
      is_active: '1',
    };

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/program/program-recommendations`, { params: { ...params } });
  }

  public getArticlesWithPublishDate(
    searchTerm: string,
    isOpinion?: boolean,
    columnId?: string,
    options?: IHttpOptions
  ): Observable<ApiResult<ContentSelectOptionWithPublishDate[], ApiResponseMetaList>> {
    let params = {
      withTimed: '1',
      global_filter: searchTerm,
      opinion_filter: isOpinion ? '1' : '0',
    };

    if (columnId) {
      Object.assign(params, { column_filter: columnId });
    }

    if (this.isMegyeiLap) {
      Object.assign(params, { notebook_filter: '0' });
    }

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/content-page/articles`, {
      params: {
        ...params,
      },
    });
  }

  public getArticles(
    searchTerm: string,
    isOpinion?: boolean,
    columnId?: string,
    options?: IHttpOptions,
    filterPodcast?: boolean,
    filterVideo?: boolean
  ): Observable<ApiResult<ContentSelectOption[], ApiResponseMetaList>> {
    let params = {
      withTimed: '1',
      global_filter: searchTerm,
      opinion_filter: isOpinion ? '1' : '0',
    };

    if (columnId) {
      Object.assign(params, { column_filter: columnId });
    }

    if (this.isMegyeiLap) {
      Object.assign(params, { notebook_filter: '0' });
    }

    if (options) {
      Object.assign(params, { ...options.params });
    }

    if (filterPodcast) {
      Object.assign(params, { podcast_filter: '1' });
    }

    if (filterVideo) {
      Object.assign(params, { video_filter: '1' });
    }

    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/content-page/articles`, {
      params: {
        ...params,
      },
    });
  }

  public exportArticles(searchTerm: string, params) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/export-and-download-articles`, {
      params: {
        ...(searchTerm ? { global_filter: searchTerm } : {}),
        ...params,
      },
      responseType: 'blob',
      observe: 'response',
    });
  }

  public exportGalleries(params) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/media/galleries-export`, {
      params: {
        ...params,
      },
      responseType: 'blob',
      observe: 'response',
    });
  }

  public getRecipes(
    searchTerm: string,
    isOpinion?: boolean,
    columnId?: string,
    options?: IHttpOptions
  ): Observable<ApiResult<ContentSelectOption[], ApiResponseMetaList>> {
    let params = {
      global_filter: searchTerm,
    };

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/recipes`, {
      params: {
        ...params,
      },
    });
  }

  public getArticlesWithoutSource(searchTerm: string, isOpinion?: boolean, columnId?: string, options?: IHttpOptions) {
    let params = {
      global_filter: searchTerm,
      opinion_filter: isOpinion ? '1' : '0',
    };

    if (columnId) {
      Object.assign(params, { columns_filter: columnId });
    }

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/articles`, {
      params: {
        ...params,
      },
    });
  }

  public getShortNews(searchTerm: string, options?: IHttpOptions) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/content-page/articles`, {
      params: {
        global_filter: searchTerm,
        shortNews_filter: '1',
        ...options?.params,
      },
    });
  }

  public getFastNews(searchTerm: string, options?: IHttpOptions) {
    const params: Record<string, string> = {
      global_filter: searchTerm,
      fastNews_filter: '1',
    };

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.reqService.get(
      `api/${this.cmsLang}/${this.contentLang}/source/content-page/articles`, // TODO: Check if this is correct
      {
        params: { ...params },
      }
    );
  }

  public getMinuteToMinutes(searchTerm: string, options?: IHttpOptions) {
    options ??= { params: {} };

    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/content-page/articles`, {
      params: {
        global_filter: searchTerm,
        isMinuteToMinute_filter: '1',
        ...options.params,
      },
    });
  }

  public getNotes(searchTerm: string, isNote?: boolean, columnId?: string, options?: IHttpOptions) {
    const params: Record<string, string> = {
      withTimed: '1',
      global_filter: searchTerm,
      notebook_filter: isNote ? '1' : '0',
    };

    if (options) {
      Object.assign(params, { ...options.params });
    }

    if (columnId) {
      Object.assign(params, { column_filter: columnId });
      return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/content-page/articles`, {
        params: { ...params },
      });
    } else {
      return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/content-page/articles`, {
        params: { ...params },
      });
    }
  }

  public getBrandArticles(searchTerm: string, brand: string, options?: IHttpOptions) {
    const params: Record<string, string> = {
      global_filter: searchTerm,
    };

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/content-page/branding-boxes/${brand}`, {
      params: { ...params },
    });
  }

  public getVisegradPostArticles(searchTerm: string, articleSource: string, options?: IHttpOptions) {
    const params: Record<string, string> = {
      global_filter: searchTerm,
      articleSource: articleSource,
    };

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/articles/`, { params: { ...params } });
  }

  public getArticlesByDossierManyTOMany(searchTerm: string, dossierId: string, options?: IHttpOptions) {
    const params: Record<string, string> = {
      withTimed: '1',
      global_filter: searchTerm,
      'dossiers_filter[]': dossierId,
    };

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/content-page/articles`, { params: { ...params } });
  }

  public getArticlesByDossier(searchTerm: string, dossierId: string, options?: IHttpOptions) {
    const params: Record<string, string> = {
      withTimed: '1',
      global_filter: searchTerm,
      dossier_filter: dossierId,
    };

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/content-page/articles`, { params: { ...params } });
  }

  public getVideos(searchTerm: string, columnId?: string, options?: IHttpOptions) {
    const params: Record<string, string> = {
      global_filter: searchTerm,
    };

    if (options) {
      Object.assign(params, { ...options.params });
    }

    if (columnId) {
      Object.assign(params, { column_filter: columnId });
      return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/media/videos`, { params: { ...params } });
    } else {
      return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/media/videos`, { params: { ...params } });
    }
  }

  public getGalleries(searchTerm: string, options?: IHttpOptions) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/media/galleries`, {
      params: { global_filter: searchTerm, ...options?.params },
    });
  }

  public getPodcasts(searchTerm: string, options?: IHttpOptions) {
    const params: Record<string, string> = {
      global_filter: searchTerm,
    };

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/content-group/podcasts`, { params: { ...params } });
  }

  public getPriorities(): Observable<ApiResult<Priority[], ApiResponseMetaList>> {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/content-page/priorities`);
  }

  public getBrandingBoxTypes() {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/content-page/branding-box/types`);
  }

  public getArticleSources() {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/content-group/article-sources`);
  }

  public getDossiers(searchTerm: string, options?: IHttpOptions) {
    const params: Record<string, string> = {
      global_filter: searchTerm,
    };

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/content-group/dossiers`, { params: { ...params } });
  }

  public getVotes(searchTerm: string, options?: IHttpOptions) {
    const params: Record<string, string> = {
      global_filter: searchTerm,
    };

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/voting/votings`, { params: { ...params } });
  }

  public getMultiVotes(searchTerm: string, options?: IHttpOptions) {
    const params: Record<string, string> = {
      global_filter: searchTerm,
    };

    if (options) {
      Object.assign(params, { ...options.params });
    }

    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/multi-vote/votes`, { params: { ...params } });
  }

  public getAdTypes() {
    return this.reqService.get(
      `api/${this.cmsLang}/${this.contentLang}/source/portal/commercials`
      // { params: { medium: medium } },
    );
  }

  public getArticleView(id: string): Observable<ApiResult<Article>> {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/article/${id}/view`);
  }

  public getArticleFromPublicApi(categorySlug: string, year: string, month: string, slug: string) {
    return this.reqService.get(`publicapi/${this.contentLang}/content-page/article/${categorySlug}/${year}/${month}/${slug}`);
  }

  public getQuizView(id: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-group/quiz/${id}`);
  }

  public getBrandArticleView(id: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/branding-box/${id}`).pipe(
      map(({ data }) => {
        const result: Record<string, any> = {};

        for (const { key, value } of data) {
          result[key] = value;
        }

        return result;
      })
    );
  }

  public getVideoView(id: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/media/video/${id}/view`);
  }

  public getGalleryView(id: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/media/gallery/${id}/view`);
  }

  public getDossierView(id: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-group/dossier/${id}/view`);
  }

  public getMultiVoteView(id: string): Observable<
    ApiResult<
      BackendMultiVoteData & {
        original?: BackendMultiVoteData;
      }
    >
  > {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/multi-vote/multi-vote/${id}/view`);
  }

  public getVoteView(id: string): Observable<ApiResult<BackendVoteData>> {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/voting/voting/${id}/view`);
  }

  public getPodcastView(id: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-group/podcast/${id}/view`);
  }

  // Profile
  public getProfile() {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/user/profile`);
  }

  public getCurrentExternalContributor() {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/user/get_current_external_contributor`);
  }

  public updateProfile(contentData: ContentData) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/user/profile`, contentData);
  }

  private getDataWithOptionalGlobalFilter<T = any>(url: string, searchTerm: string, limit = 200, extraParams: Record<string, string> = {}): Observable<T> {
    const params: Record<string, string> = {
      rowCount_limit: limit.toString(),
      ...extraParams,
    };

    if (searchTerm) {
      params.global_filter = searchTerm;
    }

    return this.reqService.get<T>(url, { params });
  }

  public getCommercialContentTypeSource() {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/portal/commercial/page_type`);
  }

  public getlanguageColumns() {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/source/content-group/language-columns`);
  }

  public getArticleFilterFormInfo(id?: string) {
    if (id) {
      return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/article/filter/${id}`);
    } else {
      return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/article/filter`);
    }
  }

  public createArticleFilter(data: any) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/content-page/article/filter`, data);
  }

  public getArticleFilterList() {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/article/filters`);
  }

  public deleteArticleFilter(id: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/content-page/article/filter/${id}/delete`, {});
  }

  public updateArticleFilter(id: string, data: any) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/content-page/article/filter/${id}`, data);
  }

  public getDefaultFilter() {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/content-page/article/filter/get-default`);
  }

  // Portál config oldal

  public getConfigList(itemType: string, options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/config/${itemType}`, options);
  }

  public getConfig(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/config/${itemType}/${id}`);
  }

  public getConfigFormInfo(itemType: string, id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/config/${itemType}/${id}`);
    } else {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/config/${itemType}`);
    }
  }

  public getConfigView(itemType: string, id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/config/${itemType}/${id}/view`);
  }

  public updateConfig(itemType: string, id: string, formData: any) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/config/${itemType}/${id}`, formData);
  }

  // Facebook Post (megyei + kpi)
  public getFacebookSites() {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/facebook-post/get-site-list-test`);
  }

  public sendFacebookPostRequest(data: any, isRecipe = false) {
    return this.reqService.post(`api/${this.cmsLang}/${this.contentLang}/facebook-post${isRecipe ? '-recipe' : ''}`, data);
  }

  public getKpiStatus(contentType: KpiContentType, id: string) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/central-publishing/status/${contentType}/${id}`);
  }

  public getTagsOnHeaderBarList(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/content-group/tag/on-header-bar/list`, options);
  }

  public addTagToHeaderBar(id: string) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/content-group/tag/on-header-bar/add?tagId=${id}`, {});
  }

  public removeTagToHeaderBar(id: string) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/content-group/tag/on-header-bar/remove?tagId=${id}`, {});
  }

  moveToheaderBarSlot(id: string, slot: number) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/content-group/tag/on-header-bar/sort?tagId=${id}&targetSort=${slot}`, {});
  }

  public createGame(itemType: string, formData: any) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/content-group/${itemType}/create`, formData);
  }

  mergeTags(formData: any) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/content-group/tags/merge`, formData);
  }

  public getCustomHeaderConfig() {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/menu/more-header-config`);
  }

  public createCustomHeaderConfig(data: CustomHeaderConfig) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/menu/more-header-config`, data);
  }

  public getBannedWords(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/comments/banned-words`, options);
  }

  public getBannedWord(id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/comments/banned-word/${id}`);
  }

  public createBannedWord(formData: any) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/comments/banned-word`, formData);
  }

  public updateBannedWord(id: string, formData: any) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/comments/banned-word/${id}`, formData);
  }

  public activateBannedWord(id: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/comments/banned-word/${id}/to-public`, {});
  }

  public inactivateBannedWord(id: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/comments/banned-word/${id}/to-not-public`, {});
  }

  public deleteBannedWord(id: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/comments/banned-word/${id}/delete`, {});
  }

  public restoreBannedWord(id: string) {
    return this.reqService.patch(`api/${this.cmsLang}/${this.contentLang}/comments/banned-word/${id}/restore`, {});
  }

  public getBannedWordForm() {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/comments/banned-word`);
  }

  public getReportedComments(options?: IHttpOptions) {
    const params: Record<string, string> = {};
    if (options) {
      Object.assign(params, { ...options.params });
    }
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/comments/report/list`, { params });
  }

  public allowReportedComment(id: string) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/comments/report/${id}/allow`, {});
  }

  public rejectReportedComment(id: string) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/comments/report/${id}/deny`, {});
  }

  public getAllComments(options?: IHttpOptions) {
    const params = Object.entries(options.params).reduce<Record<string, string>>((acc, [key, value]) => (value ? { ...acc, [key]: value } : acc), {});
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/comments/all-comments`, { params });
  }

  public commentToLeader(id: string) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/comments/comment/${id}/status-to-leader`, { id });
  }

  public commentToFollower(id: string) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/comments/comment/${id}/status-to-follower`, { id });
  }

  public showComment(id: string) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/comments/comment/${id}/status-to-show`, { id });
  }

  public hideComment(id: string) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/comments/comment/${id}/status-to-hide`, { id });
  }

  public deleteComment(id: string) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/comments/comment/${id}/status-to-delete`, { id });
  }

  public restoreComment(id: string) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/comments/comment/${id}/status-to-restore`, { id });
  }

  public approveComment(id: string) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/comments/reported-comment/${id}/allow`, { id });
  }

  public denyComment(id: string) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/comments/reported-comment/${id}/deny`, { id });
  }

  portalUser = {
    getAll: (options?: IHttpOptions) => {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/portal-users`, options);
    },
    getUserForm: (id?: string) => {
      if (id) {
        return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/portal-user/${id}/save`, {});
      } else {
        return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/portal-user/create`, {});
      }
    },
    updatePortalUserForm: (formInfo: any, id?: string) => {
      if (id) {
        return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/portal-user/${id}/save`, formInfo);
      } else {
        return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/portal-user/create`, formInfo);
      }
    },
    delete: (id: string) => {
      return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/portal-users/portal-user/${id}/delete`, {});
    },
    getSubscriptionFormInfo: (id?: string) => {
      if (id) {
        return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/portal-user/subscription/${id}/save`);
      } else {
        return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/portal-user/subscription/create`);
      }
    },
    updateSubscriptionForm: (formInfo: any, id?: string) => {
      if (id) {
        return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/portal-user/subscription/${id}/save`, formInfo);
      } else {
        return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/portal-user/subscription/create`, formInfo);
      }
    },
    mute: (id: string) => {
      return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/portal-users/portal-user/${id}/mute`, {});
    },
    unmute: (id: string) => {
      return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/portal-users/portal-user/${id}/unmute`, {});
    },
    source: (options?: IHttpOptions) => {
      const params = Object.entries(options.params).reduce<Record<string, string>>((acc, [key, value]) => (value ? { ...acc, [key]: value } : acc), {});
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/source/portal-users`, {
        ...options,
        params,
      });
    },
    badge: {
      getAll: (portalUserId: string, options?: IHttpOptions) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/portal-user/${portalUserId}/badges`;
        return this.reqService.get(url, options);
      },
      getFormInfoForCreate: (portalUserId: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/portal-user/${portalUserId}/badge`;
        return this.reqService.get(url);
      },
      create: (formInfo: ContentResponse, portalUserId?: string) => {
        const createUrl = portalUserId ? `/api/${this.cmsLang}/${this.contentLang}/portal-user/${portalUserId}/badge` : formInfo.meta.requestUrl;
        return this.reqService.post(createUrl, formInfo);
      },
      getFormInfoForUpdate: (badgeId: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/portal-user-badge/${badgeId}`;
        return this.reqService.get(url);
      },
      update: (badgeId: string, formData: any) => {
        return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/portal-user-badge/${badgeId}?destroy=1`, formData);
      },
      delete: (badgeId: string) => {
        return this.reqService.delete(`/api/${this.cmsLang}/${this.contentLang}/portal-user-badge/${badgeId}/delete`);
      },
    },
  };

  public validateRss(url: string): Observable<boolean> {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/check-external-rss-feed?url=${url}`).pipe(
      map((res: ApiResult<{ valid: boolean }>) => {
        return !!res.data.valid;
      })
    );
  }

  public fetchExternalRss(url: string): Observable<ApiResult<BackendExternalFeedData>> {
    return this.reqService.get(`/publicapi/${this.cmsLang}/external-rss-feed?url=${url}`);
  }

  public getGeneratedTags(data: TagGeneratorRequest) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/v1/content-group/dynamic-content-tagger`, data);
  }

  public searchGeneratedTags(searchTerm: string, id: string) {
    const params = {
      global_filter: searchTerm,
      id,
    };
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/v1/content-group/dynamic-content-tagger/search`, {
      params: {
        ...params,
      },
    });
  }

  public obtainTag(tagTitle: string) {
    const data = {
      data: {
        title: tagTitle,
      },
    };
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/content-group/obtain-tag`, data);
  }

  public getSubscriptionProduct(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/subscription/product/list`, options);
  }

  public createSubscriptionProduct(formInfo: ContentResponse, id?: string) {
    if (id) {
      return this.reqService.put(`/api/${this.cmsLang}/${this.contentLang}/subscription/product/${id}/save`, formInfo);
    }
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/subscription/product/create`, formInfo);
  }

  public getSubscriptionProductFormInfo(id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/subscription/product/${id}`);
    }
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/subscription/product/create`);
  }

  public deleteSubscriptionProduct(id: string) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/subscription/product/${id}/delete`, {});
  }

  public restoreSubscriptionProduct(id: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/subscription/product/${id}/restore`, {});
  }

  public getMarketingCampaign(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/subscription/campaign/list`, options);
  }

  public deleteMarketingCampaign(id: string) {
    return this.reqService.delete(`/api/${this.cmsLang}/${this.contentLang}/subscription/campaign/${id}/delete`);
  }

  public restoreMarketingCampaign(id: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/subscription/campaign/${id}/restore`, {});
  }

  public createMarketingCampaign(formData: ContentResponse, id?: string) {
    if (id) {
      return this.reqService.put(`/api/${this.cmsLang}/${this.contentLang}/subscription/campaign/${id}/update`, formData);
    }
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/subscription/campaign/create`, formData);
  }

  public getMarketingCampaignFormInfo(id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/subscription/campaign/${id}/details`, {});
    }
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/subscription/campaign/create`);
  }

  public activeMarketingCampaign(id: string) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/subscription/campaign/${id}/to-active`, {});
  }

  public inactiveMarketingCampaign(id: string) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/subscription/campaign/${id}/to-inactive`, {});
  }

  public getSubscriptionsList(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/subscription/list`, options);
  }

  public reInitTransaction(subscriptionId: string, options?: IHttpOptions): Observable<void> {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/subscription/${subscriptionId}/retry-init-transaction`, options);
  }

  public setTransactionStatusToRefund(subscriptionId: string): Observable<void> {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/subscription/${subscriptionId}/status/save`, {
      status: 'refund',
    });
  }

  public getSubscriptionsExport(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/subscription/export/list`, options);
  }

  public createSubscriptionsExport(date?: string[]) {
    let dateFilter = '';
    if (date?.length === 2) {
      dateFilter = `/${dateToFilterString(date[0])}/${dateToFilterString(date[1])}`;
    }
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/subscription/export/create${dateFilter}`, {});
  }

  public deleteSubscriptionsExport(id: string) {
    return this.reqService.put(`/api/${this.cmsLang}/${this.contentLang}/subscription/export/${id}/delete`, {});
  }

  public downloadSubscriptionsExport(id: string, options: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/subscription/export/${id}`, options);
  }

  public getCampingTypes(searchTerm: string = null) {
    const url = `/api/${this.cmsLang}/${this.contentLang}/source/campaign/type`;
    const params: Record<string, string> = {};
    if (searchTerm) {
      params.global_filter = searchTerm;
    }
    return this.reqService.get(url, { params });
  }

  public getTransactionStatuses(searchTerm: string = null) {
    const url = `/api/${this.cmsLang}/${this.contentLang}/source/content-group/subscription/transaction/status`;
    const params: Record<string, string> = {};
    if (searchTerm) {
      params.global_filter = searchTerm;
    }
    return this.reqService.get(url, { params });
  }

  getSubscriptionView(id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/subscription/${id}`);
  }

  public getExportAddressee(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/subscription/export/addressee/list`, options);
  }

  public getExportAddresseeFormInfo(id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/subscription/export/addressee/${id}`);
    }
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/subscription/export/addressee/create`);
  }

  public createExportAddressee(formData: ContentResponse, id?: string) {
    if (id) {
      return this.reqService.put(`/api/${this.cmsLang}/${this.contentLang}/subscription/export/addressee/${id}`, formData);
    }
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/subscription/export/addressee/create`, formData);
  }

  public activeExportAddressee(id: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/subscription/export/addressee/${id}/activate`, {});
  }

  public inactiveExportAddressee(id: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/subscription/export/addressee/${id}/inactivate `, {});
  }

  getSubscriptionEmailTemplateEventTypes(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/source/subscription/event-types`, options);
  }

  getSubscriptionEmailTemplates(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/subscription/email/list`, options);
  }

  createSubscriptionEmailTemplate(formInfo: ContentResponse, id?: string) {
    if (id) {
      return this.reqService.put(`/api/${this.cmsLang}/${this.contentLang}/subscription/email/${id}/save?destroy=1`, formInfo);
    }
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/subscription/email/create`, formInfo);
  }

  getSubscriptionEmailTemplateFormInfo(id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/subscription/email/${id}/details`);
    }
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/subscription/email/create`);
  }

  deleteSubscriptionEmailTemplate(id: string) {
    return this.reqService.delete(`/api/${this.cmsLang}/${this.contentLang}/subscription/email/${id}/delete`);
  }

  restoreSubscriptionEmailTemplate(id: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/subscription/email/${id}/restore`, {});
  }

  getPopupNotifierList(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/notifier/list`, options);
  }

  getPopupNotifierFormInfo(id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/notifier/${id}/edit`);
    }
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/notifier/create`);
  }

  createPopupNotifier(formInfo: ContentResponse, id?: string) {
    if (id) {
      return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/notifier/${id}/edit`, formInfo);
    }
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/notifier/create`, formInfo);
  }

  deletePopupNotifier(id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/notifier/${id}/delete`);
  }

  restorePopupNotifier(id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/notifier/${id}/restore`);
  }

  /* savePortalUserPassword() {

  } */

  getStations(options?: IHttpOptions) {
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/forecast/stations`, options);
  }

  getStationsFormInfo(id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/station/${id}/update`);
    }
    return this.reqService.get(`api/${this.cmsLang}/${this.contentLang}/forecast/station/create`);
  }

  createStation(formInfo: ContentResponse, id?: string) {
    if (id) {
      return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/forecast/station/${id}/update`, formInfo);
    }
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/forecast/station/create`, formInfo);
  }

  deleteStation(id: string) {
    return this.reqService.delete(`/api/${this.cmsLang}/${this.contentLang}/forecast/station/${id}/to-deleted`);
  }

  restoreStation(id: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/forecast/station/${id}/to-not-deleted`, {});
  }

  getAutoMails(options?: IHttpOptions): Observable<Automail[]> {
    return this.reqService.get<Automail[]>(`api/${this.cmsLang}/${this.contentLang}/forecast/custom-auto-mails`, options);
  }

  getAutomailFormInfo(id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/custom-auto-mail/${id}`);
    }
  }

  createAutomail(formInfo: ContentResponse, id?: string) {
    if (id) {
      return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/forecast/custom-auto-mail/${id}`, formInfo);
    }
  }

  getPartners(options?: IHttpOptions): Observable<Partner[]> {
    return this.reqService.get<Partner[]>(`api/${this.cmsLang}/${this.contentLang}/forecast/external/partners`, options);
  }

  getPartnerFormInfo(id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/external/partner/${id}/update`);
    }
    return this.reqService.get<Partner[]>(`api/${this.cmsLang}/${this.contentLang}/forecast/external/partner/create`);
  }

  createPartner(formInfo: ContentResponse, id?: string) {
    if (id) {
      return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/forecast/external/partner/${id}/update`, formInfo);
    }
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/forecast/external/partner/create`, formInfo);
  }

  deletePartner(id: string) {
    return this.reqService.delete(`/api/${this.cmsLang}/${this.contentLang}/forecast/external/partner/${id}/delete`);
  }

  restorePartner(id: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/forecast/external/partner/${id}/restore`, {});
  }

  getOrvosmetList(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/met/orvosmet/list`, options);
  }

  getOrvosmetFormInfo(id?: string) {
    if (id) {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/met/orvosmet/${id}/view`);
    }
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/met/orvosmet/create`);
  }

  createOrvosmet(formInfo: ContentResponse, id?: string) {
    if (id) {
      return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/forecast/met/orvosmet/${id}/update`, formInfo);
    }
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/forecast/met/orvosmet/create`, formInfo);
  }

  deleteOrvosmet(id: string) {
    return this.reqService.delete(`/api/${this.cmsLang}/${this.contentLang}/forecast/met/orvosmet/${id}/delete`);
  }

  getMapsList(options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/maps`, options);
  }

  getSnowdepthFormInfo() {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/snow-depth/form`);
  }

  updateSnowdepth(formInfo: ContentResponse) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/forecast/snow-depth/form`, formInfo);
  }

  getTemperatureMapForm() {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/map/temperature/edit_settings`);
  }

  updateTemperatureMap(formInfo: ContentResponse) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/forecast/map/temperature/edit_settings`, formInfo);
  }

  getWhiteEvents() {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/white/list`);
  }

  getWhiteEventsChance(id: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/white/period/${id}/form`);
  }

  updateWhiteEventsChance(id: string, formInfo) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/forecast/white/period/${id}/form`, formInfo);
  }

  setActivateMap(mapId: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/forecast/map/${mapId}/to-public`, null);
  }

  setDeactivateMap(mapId: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/forecast/map/${mapId}/to-not-public`, null);
  }

  public getTextForecast(date: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/prediction-text/date/${date}`);
  }

  public saveTextForecast(date: string, data: any, sendEmails = false) {
    return this.reqService.post(
      `/api/${this.cmsLang}/${this.contentLang}/forecast/prediction-text/date/${date}`,
      {
        data,
      },
      {
        params: {
          ...(sendEmails && { sendEmails: '1' }),
        },
      }
    );
  }

  public getTextForecastEmailRecipients() {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/prediction-text/emails?rowCount_limit=100`);
  }

  public createTextForecastEmailRecipient(email: string) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/forecast/prediction-text/add-email?rowCount_limit=100`, {
      data: { email },
    });
  }

  public deleteTextForecastEmailRecipient(id: string) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/forecast/prediction-text/remove-email?rowCount_limit=100`, {
      data: { id },
    });
  }

  public getHourlyForecastIcons() {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/prediction-hourly/icons`);
  }

  public getHourlyForecastPrediction(date: string, regionSlug: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/prediction-hourly/region/${regionSlug}/date/${date}`);
  }

  public saveHourlyForecastPrediction(date: string, regionSlug: string, data: any) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/forecast/prediction-hourly/region/${regionSlug}/date/${date}`, {
      data,
    });
  }

  public getDailyForecastPrediction(date: string, regionSlug: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/prediction-daily/region/${regionSlug}/date/${date}`);
  }

  public getDailyFrontalSystem(date: string) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/prediction-daily/date/${date}/frontal-system/load`);
  }

  public saveDailyFrontalSystem(date: string, frontalSystem: string) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/forecast/prediction-daily/date/${date}/frontal-system/save`, {
      data: {
        frontalSystem,
      },
    });
  }

  public saveDailyForecastPrediction(date: string, regionSlug: string, data: any) {
    return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/forecast/prediction-daily/region/${regionSlug}/date/${date}`, {
      data,
    });
  }

  public getDailyForecastRainUnits() {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/prediction-daily/rain-units`);
  }

  public getDailyForecastIcons() {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/prediction-daily/icons`);
  }

  public getForecastRegions() {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/area/regions`);
  }

  public getDetections(status: DetectionStatus, options?: IHttpOptions) {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/forecast/perception/list/status/${status}`, options);
  }

  public sendDetectionsAction(status: DetectionStatus, id: string) {
    return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/forecast/perception/${id}/to-${status}`, {});
  }

  facility = {
    getAll: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/facilities`;
      return this.reqService.get(url, options);
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/facility/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/facility`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/facility/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/facility/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/facility/${id}/restore`;
      return this.reqService.post(url, {});
    },
    activate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/facility/${id}/to-active`;
      return this.reqService.post(url, {});
    },
    inactivate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/facility/${id}/to-inactive`;
      return this.reqService.post(url, {});
    },
  };

  didYouKnow = {
    getAll: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/content-group/did-you-know/list`;
      return this.reqService.get(url, options);
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/content-group/did-you-know/${id || ''}`;
      return this.reqService.get(url);
    },
    getForm: () => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/content-group/did-you-know/create`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/content-group/did-you-know/create`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/content-group/did-you-know/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/content-group/did-you-know/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/content-group/did-you-know/${id}/restore`;
      return this.reqService.post(url, {});
    },
    activate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/content-group/did-you-know/${id}/to-active`;
      return this.reqService.post(url, {});
    },
    inactivate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/content-group/did-you-know/${id}/to-inactive`;
      return this.reqService.post(url, {});
    },
  };

  liveSport = {
    getAll: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/live-sports`;
      return this.reqService.get(url, options);
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/live-sport/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/live-sport`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/live-sport/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/live-sport/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/live-sport/${id}/restore`;
      return this.reqService.post(url, {});
    },
    activate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/live-sport/${id}/to-active`;
      return this.reqService.post(url, {});
    },
    inactivate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/live-sport/${id}/to-inactive`;
      return this.reqService.post(url, {});
    },
    getOneByScheduleId: (scheduleId: string, refreshHash?: string) => {
      if (refreshHash) {
        refreshHash = '?' + refreshHash;
      }
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${scheduleId}/data${refreshHash}`;
      return this.reqService.get(url);
    },
  };

  scheduleEvent = {
    getAll: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/schedule-events`;
      return this.reqService.get(url, options);
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/schedule-event/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/schedule-event`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/schedule-event/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/schedule-event/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/schedule-event/${id}/restore`;
      return this.reqService.post(url, {});
    },
    activate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/schedule-event/${id}/to-active`;
      return this.reqService.post(url, {});
    },
    inactivate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/schedule-event/${id}/to-inactive`;
      return this.reqService.post(url, {});
    },
  };

  playerPosition = {
    getAll: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/player-positions`;
      return this.reqService.get(url, options);
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/player-position/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/player-position`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/player-position/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/player-position/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/player-position/${id}/restore`;
      return this.reqService.post(url, {});
    },
    activate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/player-position/${id}/to-active`;
      return this.reqService.post(url, {});
    },
    inactivate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/player-position/${id}/to-inactive`;
      return this.reqService.post(url, {});
    },
  };

  staffPosition = {
    getAll: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/staff-positions`;
      return this.reqService.get(url, options);
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/staff-position/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/staff-position`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/staff-position/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/staff-position/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/staff-position/${id}/restore`;
      return this.reqService.post(url, {});
    },
    activate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/staff-position/${id}/to-active`;
      return this.reqService.post(url, {});
    },
    inactivate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/staff-position/${id}/to-inactive`;
      return this.reqService.post(url, {});
    },
  };

  staff = {
    getAll: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/staffs`;
      return this.reqService.get(url, options);
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/staff/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/staff`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/staff/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/staff/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/staff/${id}/restore`;
      return this.reqService.post(url, {});
    },
    activate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/staff/${id}/to-active`;
      return this.reqService.post(url, {});
    },
    inactivate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/staff/${id}/to-inactive`;
      return this.reqService.post(url, {});
    },
  };

  player = {
    getAll: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/players`;
      return this.reqService.get(url, options);
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/player/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/player`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/player/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/player/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/player/${id}/restore`;
      return this.reqService.post(url, {});
    },
    activate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/player/${id}/to-active`;
      return this.reqService.post(url, {});
    },
    inactivate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/player/${id}/to-inactive`;
      return this.reqService.post(url, {});
    },
    getSourceUrl: () => {
      return `/api/${this.cmsLang}/${this.contentLang}/source/sport/players`;
    },
  };

  team = {
    getAll: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/teams`;
      return this.reqService.get(url, options);
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/team/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/team`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/team/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/team/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/team/${id}/restore`;
      return this.reqService.post(url, {});
    },
    activate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/team/${id}/to-active`;
      return this.reqService.post(url, {});
    },
    inactivate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/team/${id}/to-inactive`;
      return this.reqService.post(url, {});
    },
    getSourceUrl: () => {
      return `/api/${this.cmsLang}/${this.contentLang}/source/sport/teams`;
    },
  };

  season = {
    getAll: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/seasons`;
      return this.reqService.get(url, options);
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/season/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/season`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/season/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/season/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/season/${id}/restore`;
      return this.reqService.post(url, {});
    },
    activate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/season/${id}/to-active`;
      return this.reqService.post(url, {});
    },
    inactivate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/season/${id}/to-inactive`;
      return this.reqService.post(url, {});
    },
  };

  tvStation = {
    getAll: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/tv-stations`;
      return this.reqService.get(url, options);
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/tv-station/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/tv-station`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/tv-station/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/tv-station/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/tv-station/${id}/restore`;
      return this.reqService.post(url, {});
    },
    activate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/tv-station/${id}/to-active`;
      return this.reqService.post(url, {});
    },
    inactivate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/tv-station/${id}/to-inactive`;
      return this.reqService.post(url, {});
    },
  };

  competition = {
    getSourceAll: (options?: IHttpOptions): Observable<ApiResult<Championship[]>> => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/source/sport/competitions`;
      return this.reqService.get(url, options);
    },
    getAll: (options?: IHttpOptions) => {
      const params: Record<string, string> = {};

      if (options) {
        Object.assign(params, { ...options.params });
      }
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/sport/competitions`, { params: { ...params } });
    },
    getAllSource: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/source/sport/competitions`;
      return this.reqService.get(url, options);
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/competition/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/competition`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/competition/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/competition/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/competition/${id}/restore`;
      return this.reqService.post(url, {});
    },
    activate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/competition/${id}/to-active`;
      return this.reqService.post(url, {});
    },
    inactivate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/competition/${id}/to-inactive`;
      return this.reqService.post(url, {});
    },
    getCompetitionTeamsApiData: (id: string) => {
      return {
        url: `/api/${this.cmsLang}/${this.contentLang}/sport/competition/${id}/teams`,
        method: 'get',
      };
    },
    getCompetitionTeams: (id: string) => {
      const apiData = this.competition.getCompetitionTeamsApiData(id);
      return this.reqService[apiData.method](apiData.url);
    },
    addTeamsApiData: (id: string) => {
      return {
        url: `/api/${this.cmsLang}/${this.contentLang}/sport/competition/${id}/bulk-add-teams`,
        method: 'post',
      } as { url: string; method: 'post' | 'patch' };
    },
    deleteTeamsApiData: (id: string) => {
      return {
        url: `/api/${this.cmsLang}/${this.contentLang}/sport/competition/${id}/bulk-remove-teams`,
        method: 'patch',
      } as { url: string; method: 'post' | 'patch' };
    },
  };

  competitionTeam = {
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/competition-team/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/competition-team`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/competition-team/${id}`;
      return this.reqService.patch(url, data);
    },
    getCompetitionTeamPLayersApiData: (id: string) => {
      return {
        url: `/api/${this.cmsLang}/${this.contentLang}/sport/competition-team/${id}/players`,
        method: 'get',
      };
    },
    getCompetitionTeamPlayersApiData: (id: string) => {
      return {
        url: `/api/${this.cmsLang}/${this.contentLang}/sport/competition-team/${id}/players`,
        method: 'get',
      };
    },
    addPlayersApiData: (id: string) => {
      return {
        url: `/api/${this.cmsLang}/${this.contentLang}/sport/competition-team/${id}/bulk-add-players`,
        method: 'post',
      } as { url: string; method: 'post' | 'patch' };
    },
    deletePlayersApiData: (id: string) => {
      return {
        url: `/api/${this.cmsLang}/${this.contentLang}/sport/competition-team/${id}/bulk-remove-players`,
        method: 'patch',
      } as { url: string; method: 'post' | 'patch' };
    },
  };

  competitionTeamPlayer = {
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/competition-team-player/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/competition-team-player`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/competition-team-player/${id}`;
      return this.reqService.patch(url, data);
    },
  };

  schedule = {
    getAll: (options?: IHttpOptions): Observable<ApiResult<ScheduleItem[], ApiResponseMetaList>> => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/schedules`;
      return this.reqService.get<ApiResult<ScheduleItem[], ApiResponseMetaList>>(url, options).pipe(
        map((response) => ({
          ...response,
          data: response.data.map((d) => ({
            ...d,
            visitors: parseInt(d.visitors as string),
            homeScore: parseFloat(d.homeScore as string),
            awayScore: parseFloat(d.awayScore as string),
            scheduleDate: backendDateToDate(d.scheduleDate as string),
            createdAt: backendDateToDate(d.createdAt as string),
            updatedAt: backendDateToDate(d.updatedAt as string),
          })),
        }))
      );
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/schedule`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${id}/restore`;
      return this.reqService.post(url, {});
    },
    activate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${id}/to-active`;
      return this.reqService.post(url, {});
    },
    inactivate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${id}/to-inactive`;
      return this.reqService.post(url, {});
    },

    players: {
      list: (id: string, team: 'home' | 'away') => {
        return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${id}/${team}/players`);
      },
      listUrl: (id: string, team: 'home' | 'away') => {
        return `/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${id}/${team}/players`;
      },
      bulkAdd: (id: string, team: 'home' | 'away', data: any) => {
        return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${id}/${team}/bulk-add-players`, data);
      },
      bulkAddUrl: (id: string, team: 'home' | 'away') => {
        return `/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${id}/${team}/bulk-add-players`;
      },
      bulkRemove: (id: string, team: 'home' | 'away', data: any) => {
        return this.reqService.delete(`/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${id}/${team}/bulk-remove-players`, data);
      },
      bulkRemoveUrl: (id: string, team: 'home' | 'away') => {
        return `/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${id}/${team}/bulk-remove-players`;
      },
      bulkAddApiData: (id: string, team: 'home' | 'away') => {
        return {
          url: `/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${id}/${team}/bulk-add-players`,
          method: 'post',
        } as { url: string; method: 'post' | 'patch' };
      },
      bulkRemoveApiData: (id: string, team: 'home' | 'away') => {
        return {
          url: `/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${id}/${team}/bulk-remove-players`,
          method: 'patch',
        } as { url: string; method: 'post' | 'patch' };
      },
      /**
       * Can only update lineup and player position!
       * @param id
       * @param team
       * @param data
       */
      update: (id: string, team: 'home' | 'away', data: any) => {
        return this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${id}/${team}/bulk-update-players`, data);
      },
    },
    events: {
      getAll: (scheduleId: string, options?: IHttpOptions) => {
        return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${scheduleId}/live-events`, options);
      },
      create: (scheduleId: string, data: any) => {
        return this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${scheduleId}/live-event`, data);
      },
      update: (scheduleId: string, liveEventId: string, data: any) => {
        return this.reqService.put(`/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${scheduleId}/live-event/${liveEventId}`, data);
      },
      delete: (scheduleId: string, eventId: string) => {
        return this.reqService.delete(`/api/${this.cmsLang}/${this.contentLang}/sport/schedule/${scheduleId}/live-event/${eventId}/delete`);
      },
    },
  };

  phase = {
    getAll: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/phases`;
      return this.reqService.get(url, options);
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/phase/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/phase`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/phase/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/phase/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/phase/${id}/restore`;
      return this.reqService.post(url, {});
    },
  };

  scheduleByRound = {
    getAll: (championship: string, round: string, options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/source/sport/schedule/${championship}/${round}`;
      return this.reqService.get(url, options);
    },
  };

  recipe = {
    getAll: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipes`;
      return this.reqService.get(url, options);
    },
    getAllByStatus: (options?: IHttpOptions, status: RecipeType = RecipeType.ACCEPTED) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipes/status/${status}`;
      return this.reqService.get(url, options);
    },
    getAllForRecipeCategory: (id: string, options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipes/recipe-category/${id}`;
      return this.reqService.get<ApiResult<any>>(url, options).pipe(
        map((result) => ({
          ...result,
          data: result.data.map((d) => ({
            ...d,
            thumbnail: {
              url: d.cover_image ?? d.secondary_cover_image,
            },
            guarantee: d.mmeWarranty === '1',
          })),
        }))
      );
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe/${id || ''}`;
      return this.reqService.get(url);
    },
    view: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/source/recipe/${id}`;
      return this.reqService.get<ApiResult<RecipeCardData>>(url).pipe(
        map((result) => ({
          ...result,
          data: {
            ...result.data,
            author: {
              ...result.data.publicAuthor,
              name: result.data.publicAuthor?.fullName,
              verified: result.data.publicAuthor?.isMaestroAuthor,
              avatarUrl: result.data.publicAuthor?.avatar?.thumbnailUrl,
            },
          },
        }))
      );
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe/${id}?destroy=1`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe/${id}/restore`;
      return this.reqService.post(url, {});
    },
    activate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe/${id}/to-active`;
      return this.reqService.patch(url, {});
    },
    inactivate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe/${id}/to-inactive`;
      return this.reqService.patch(url, {});
    },
    generatePreview: (id: string): Observable<ApiResult<PreviewData>> => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe/${id}/preview/generate`;
      return this.reqService.get(url);
    },
    accept: (id: string): Observable<ApiResult<any>> => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe/${id}/accept`;
      return this.reqService.patch(url, {});
    },
    coverImageApprove: (id: string): Observable<ApiResult<any>> => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe/${id}/coverimage-approve`;
      return this.reqService.patch(url, {});
    },
    coverImageReject: (id: string): Observable<ApiResult<any>> => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe/${id}/coverimage-reject`;
      return this.reqService.patch(url, {});
    },
    getCategoriesForRecipe: (id: string): Observable<ApiResult<any>> => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe-categories-by-recipe/${id}`;
      return this.reqService.get(url);
    },
    saveCategoriesForRecipe: (id: string, payload: string[]): Observable<any> => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe/${id}/categories`;
      return this.reqService.patch(url, { categories: payload });
    },
  };

  getRecipeCategoryAllSource(searchTerm: string = null, limit: number = 10, options?: IHttpOptions) {
    const params: Record<string, string> = {
      rowCount_limit: limit.toString(),
    };

    if (searchTerm) {
      params.global_filter = searchTerm;
    }

    if (options) {
      Object.assign(params, { ...options.params });
    }

    const url = `/api/${this.cmsLang}/${this.contentLang}/source/recipe-categories`;
    return this.reqService.get(url, { params });
  }

  recipeCategory = {
    getAll: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe-categories`;
      return this.reqService.get(url, options);
    },
    getHierarchicalList: (): Observable<ApiResult<any>> => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe-categories/hierarchical-list`;
      return this.reqService.get(url);
    },
    getCategoryTree: (categoryId: string): Observable<ApiResult<any>> => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe-category-tree-by-category/${categoryId}`;
      return this.reqService.get(url);
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe-category/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe-category`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe-category/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe-category/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe-category/${id}/restore`;
      return this.reqService.post(url, {});
    },
  };

  recipeIngredient = {
    getAll: (recipeId: string, options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe/${recipeId}/recipe-ingredients`;
      return this.reqService.get(url, options);
    },
    get: (id: string, idType: 'recipe-id' | 'recipe-ingredient-id') => {
      const url =
        idType === 'recipe-id'
          ? `/api/${this.cmsLang}/${this.contentLang}/recipe/${id}/recipe-ingredient`
          : `/api/${this.cmsLang}/${this.contentLang}/recipe-ingredient/${id}`;
      return this.reqService.get(url);
    },
    create: (recipeId: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe/${recipeId}/recipe-ingredient`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe-ingredient/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/recipe-ingredient/${id}/delete`;
      return this.reqService.delete(url);
    },
  };

  ingredient = {
    getAll: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/ingredients`;
      return this.reqService.get(url, options);
    },
    getAllSource: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/source/ingredients`;
      return this.reqService.get(url, options);
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/ingredient/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/ingredient?destroy=1`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/ingredient/${id}?destroy=1`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/ingredient/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/ingredient/${id}/restore`;
      return this.reqService.post(url, {});
    },
    generatePreview: (id: string): Observable<ApiResult<PreviewData>> => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/ingredient/${id}/preview/generate`;
      return this.reqService.get(url);
    },
  };

  bestPractice = {
    getAll: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/bestpractices`;
      return this.reqService.get(url, options);
    },
    getAllSource: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/source/bestpractices`;
      return this.reqService.get(url, options);
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/bestpractice/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/bestpractice`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/bestpractice/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/bestpractice/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/bestpractice/${id}/restore`;
      return this.reqService.post(url, {});
    },
    activate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/bestpractice/${id}/to-active`;
      return this.reqService.patch(url, {});
    },
    inactivate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/bestpractice/${id}/to-inactive`;
      return this.reqService.patch(url, {});
    },
  };

  /**
   * @deprecated maestro megszűnt létezni, mint önálló entitás. helyette public author van
   */
  maestro = {
    getAll: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/maestros`;
      return this.reqService.get(url, options);
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/maestro/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/maestro`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/maestro/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/maestro/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/maestro/${id}/restore`;
      return this.reqService.patch(url, {});
    },
    activate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/maestro/${id}/to-active`;
      return this.reqService.patch(url, {});
    },
    inactivate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/maestro/${id}/to-inactive`;
      return this.reqService.patch(url, {});
    },
  };

  allergen = {
    getAll: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/allergics`;
      return this.reqService.get(url, options);
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/allergic/${id || ''}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/allergic`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/allergic/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/allergic/${id}/delete`;
      return this.reqService.delete(url);
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/allergic/${id}/restore`;
      return this.reqService.patch(url, {});
    },
  };

  selection = {
    getAll: (options?: IHttpOptions) => {
      return this.getContentGroupList('selections', options);
    },
    get: (id?: string) => {
      return this.getContentGroupFormInfo('selection', id);
    },
    create: (data: any) => {
      return this.createContentGroup('selection', data);
    },
    update: (id: string, data: any) => {
      return this.updateContentGroup('selection', id, data);
    },
    delete: (id: string) => {
      return this.deleteContentGroup('selection', id, 'delete');
    },
    restore: (id: string) => {
      return this.restoreContentGroup('selection', id);
    },
    activate: (id: string) => {
      return this.activateContentGroup('selection', id, 'patch');
    },
    inactivate: (id: string) => {
      return this.inactivateContentGroup('selection', id, 'patch');
    },
    source: (options?: IHttpOptions) => {
      return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/source/content-group/selections`, options);
    },
    items: {
      getAll: (selectionId: string, options?: IHttpOptions) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/content-group/selection/${selectionId}/items`;
        return this.reqService.get(url, options);
      },
      get: (itemId: string) => {
        return this.getContentGroupFormInfo('selection-item', itemId);
      },
      create: (selectionId: string, data: any) => {
        return this.createContentGroup(`selection/${selectionId}/item`, data);
      },
      update: (itemId: string, data: any) => {
        return this.updateContentGroup('selection-item', itemId, data);
      },
      delete: (itemId: string) => {
        return this.deleteContentGroup('selection-item', itemId, 'delete');
      },
      multipleAddWithId: (selectionId: string, items: { id: string; contentType: 'article' | 'recipe' }[]) => {
        const payload = {
          data: items,
        };
        const url = `/api/${this.cmsLang}/${this.contentLang}/content-group/selection/${selectionId}/item-bulk-create`;
        return this.reqService.post(url, payload);
      },
      getFilters: () => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/content-group/selection-item/filters`;
        return this.reqService.get(url);
      },
      getFilterResult: (payload: { data: object }, options?: IHttpOptions) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/content-group/selection-item/search`;
        return this.reqService.post(url, payload, options);
      },
      multipleAddWithFilter: (selectionId: string, filters: { data: object }) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/content-group/selection/${selectionId}/item-filters-create`;
        return this.reqService.post(url, filters);
      },
    },
  };

  weeklyMenu = {
    getAll: (options?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/weekly-menus`;
      return this.reqService.get(url, options);
    },
    get: (id?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/weekly-menu/${id || 'create'}`;
      return this.reqService.get(url);
    },
    create: (data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/weekly-menu/create`;
      return this.reqService.post(url, data);
    },
    update: (id: string, data: any) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/weekly-menu/${id}`;
      return this.reqService.patch(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/weekly-menu/${id}/delete`;
      return this.reqService.delete(url);
    },
    activate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/weekly-menu/${id}/to-active`;
      return this.reqService.patch(url, {});
    },
    inactivate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/weekly-menu/${id}/to-inactive`;
      return this.reqService.patch(url, {});
    },
    generatePreview: (id: string): Observable<ApiResult<PreviewData>> => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/weekly-menu/${id}/preview/generate`;
      return this.reqService.get(url);
    },
    isRecipeInUse: (recipeId: string): Observable<any> => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/weekly-menu/recipe-in-use/${recipeId}`;
      return this.reqService.get(url);
    },
  };

  layoutLivePerformance = {
    getContentPublished: (layoutId: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/layout-editor/layout/${layoutId}/content_published`;
      return this.reqService.get(url);
    },

    getArticleViews: (articleIds: string[]) => {
      const queryParams = articleIds.map((id) => `ids[]=${id}`).join('&');
      const url = `/api/${this.cmsLang}/${this.contentLang}/statistics/google-real-time/articles-last-minutes-page-views?${queryParams}`;
      return this.reqService.get(url);
    },
  };

  removeArticleHomePageRecommendation(articleId: string) {
    const url = `/api/${this.cmsLang}/${this.contentLang}/content-page/article/${articleId}/remove-recommendation`;
    return this.reqService.post(url, {});
  }

  setArticleWaitListState(articleId: string, state: boolean) {
    const url = `/api/${this.cmsLang}/${this.contentLang}/content-page/article/${articleId}/waitinglisted?isWaitingListedToHomepage=${state}`;
    return this.reqService.post(url, {});
  }

  getGoogleRealtimeTableData(): Observable<ApiListResult<GoogleRealtimeRecord>> {
    // if(['dev', 'local'].includes(environment.type)) { // Dev and local does not have access to google realtime api
    //   return of(GOOGLE_REALTIME_RESPONSE_MOCK as any).pipe(delay(1000))
    // }

    return this.reqService.get<ApiListResult<GoogleRealtimeRecord>>('/api/hu/hu/statistics/google-real-time/top-page-views');
  }

  getEntities(
    params:
      | HttpParams
      | {
          [param: string]: string | string[];
        } = null
  ): Observable<ApiListResult<EntityListItem>> {
    const options: IHttpOptions = { params };
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/log/entities`, options);
  }

  getEntity(id: string): Observable<ApiResult<EntityDetail>> {
    return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/log/entity/${id}`);
  }

  layoutIframe = {
    get: <T = unknown>(url: string, params: IHttpOptions) => {
      return this.reqService.get<T>(`/api/${this.cmsLang}/${this.contentLang}/${url}`, params);
    },
    post: <T = unknown, P = unknown>(url: string, params?: IHttpOptions, data?: P) => {
      return this.reqService.post<T>(`/api/${this.cmsLang}/${this.contentLang}/${url}`, data, params);
    },
  };

  olympics = {
    getParticipants: (params: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/olympics/participants`;
      return this.reqService.get(url, params);
    },
    getParticipantsSource: (params: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/source/sport/olympics/participants-for-layout`;
      return this.reqService.get(url, params);
    },
    getParticipantsFormInfo: (participantId?: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/olympics/participant/${participantId}`;
      return this.reqService.get(url);
    },
    editParticipant: (participantId: string, data: ContentData) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/olympics/participant/${participantId}`;
      return this.reqService.patch(url, data);
    },
    activateParticipant: (participantId: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/olympics/${participantId}/to-active`;
      return this.reqService.post(url, {});
    },
    inactivateParticipant: (participantId: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/sport/olympics/${participantId}/to-inactive`;
      return this.reqService.post(url, {});
    },

    medals: {
      getMedals: (params?: IHttpOptions) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/sport/olympics/medal/list`;
        return this.reqService.get(url, params);
      },
      getMedalFormInfo: () => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/sport/olympics/medal/create`;
        return this.reqService.get(url);
      },
      getMedal: (medalId: string = '') => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/sport/olympics/medal/show/${medalId}`;
        return this.reqService.get(url);
      },
      editMedal: (medalId: string, data: ContentData) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/sport/olympics/medal/update/${medalId}`;
        return this.reqService.patch(url, data);
      },
      createMedal: (data: ContentData) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/sport/olympics/medal/create`;
        return this.reqService.post(url, data);
      },
    },
  };

  starDictionary = {
    occupations: {
      getOccupationSourceList: (options?: IHttpOptions) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/occupation/source-list`;
        return this.reqService.get(url, options);
      },
      getOccupationList: (options?: IHttpOptions) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/occupation/list`;
        return this.reqService.get(url, options);
      },
      getOccupation: (occupationId: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/occupation/${occupationId}/show`;
        return this.reqService.get(url);
      },
      getOccupationFormInfo: () => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/occupation/create`;
        return this.reqService.get(url);
      },
      getOccupationPlacesOfUseList: (occupationId) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/occupation/${occupationId}/places-of-use-list`;
        return this.reqService.get(url);
      },
      createOccupation: (formData: any) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/occupation/create`;
        return this.reqService.post(url, formData);
      },
      updateOccupation: (occupationId: string, formData: any) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/occupation/${occupationId}/update`;
        return this.reqService.patch(url, formData);
      },
      restoreOccupation: (occupationId: string) => {
        const url = `api/${this.cmsLang}/${this.contentLang}/star-dictionary/occupation/${occupationId}/restore`;
        return this.reqService.patch(url, {});
      },
      deleteOccupation: (occupationId: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/occupation/${occupationId}/delete`;
        return this.reqService.delete(url, {});
      },
    },
    birthPlaces: {
      getBirthplaceList: (options?: IHttpOptions) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/birthplace/list`;
        return this.reqService.get(url, options);
      },
      getBirthplace: (birthplaceId: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/birthplace/${birthplaceId}/show`;
        return this.reqService.get(url);
      },
      getBirthplaceFormInfo: () => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/birthplace/create`;
        return this.reqService.get(url);
      },
      getBirthplacesOfUseList: (birthplaceId) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/birthplace/${birthplaceId}/places-of-use-list`;
        return this.reqService.get(url);
      },
      createBirthplace: (formData: any) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/birthplace/create`;
        return this.reqService.post(url, formData);
      },
      updateBirthplace: (birthplaceId: string, formData: any) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/birthplace/${birthplaceId}/update`;
        return this.reqService.patch(url, formData);
      },
      restoreBirthplace: (birthplaceId: string) => {
        const url = `api/${this.cmsLang}/${this.contentLang}/star-dictionary/birthplace/${birthplaceId}/restore`;
        return this.reqService.patch(url, {});
      },
      deleteBirthplace: (birthplaceId: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/birthplace/${birthplaceId}/delete`;
        return this.reqService.delete(url, {});
      },
    },
    awards: {
      getAwardList: (options?: IHttpOptions) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/award/list`;
        return this.reqService.get(url, options);
      },
      getAward: (awardId: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/award/${awardId}/show`;
        return this.reqService.get(url);
      },
      getAwardFormInfo: () => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/award/create`;
        return this.reqService.get(url);
      },
      getAwardPlacesOfUseList: (awardId) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/award/${awardId}/places-of-use-list`;
        return this.reqService.get(url);
      },
      createAward: (formData: any) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/award/create`;
        return this.reqService.post(url, formData);
      },
      updateAward: (awardId: string, formData: any) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/award/${awardId}/update`;
        return this.reqService.patch(url, formData);
      },
      restoreAward: (awardId: string) => {
        const url = `api/${this.cmsLang}/${this.contentLang}/star-dictionary/award/${awardId}/restore`;
        return this.reqService.patch(url, {});
      },
      deleteAward: (awardId: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/award/${awardId}/delete`;
        return this.reqService.delete(url, {});
      },
    },
    stars: {
      getStarList: (options?: IHttpOptions) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/star/list`;
        return this.reqService.get(url, options);
      },
      getStar: (starId: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/star/${starId}/show`;
        return this.reqService.get(url);
      },
      getStarFormInfo: () => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/star/create`;
        return this.reqService.get(url);
      },
      createStar: (formData: any) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/star/create`;
        return this.reqService.post(url, formData);
      },
      updateStar: (starId: string, formData: any) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/star/${starId}/update`;
        return this.reqService.patch(url, formData);
      },
      restoreStar: (starId: string) => {
        const url = `api/${this.cmsLang}/${this.contentLang}/star-dictionary/star/${starId}/restore`;
        return this.reqService.patch(url, {});
      },
      activateStar: (starId: string) => {
        const url = `api/${this.cmsLang}/${this.contentLang}/star-dictionary/star/${starId}/to-active`;
        return this.reqService.patch(url, {});
      },
      inactivateStar: (starId: string) => {
        const url = `api/${this.cmsLang}/${this.contentLang}/star-dictionary/star/${starId}/to-inactive`;
        return this.reqService.patch(url, {});
      },
      deleteStar: (starId: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/star-dictionary/star/${starId}/delete`;
        return this.reqService.delete(url, {});
      },
    },
  };

  gastro = {
    experienceCategories: {
      getAllSource: (params?: IHttpOptions) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/source/gastro/experience-categories`;
        return this.reqService.get(url, params);
      },
      getAll: (params?: IHttpOptions) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience-categories`;
        return this.reqService.get(url, params);
      },
      get: (id: string = '') => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience-category/${id || ''}`;
        return this.reqService.get(url);
      },
      update: (id: string, data: ContentData) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience-category/${id}`;
        return this.reqService.patch(url, data);
      },
      create: (data: ContentData) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience-category`;
        return this.reqService.post(url, data);
      },
      delete: (id: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience-category/${id}/delete`;
        return this.reqService.delete(url, {});
      },
      restore: (id: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience-category/${id}/restore`;
        return this.reqService.patch(url, {});
      },
    },
    experience: {
      getAllSource: (params?: IHttpOptions) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/source/gastro/experience`;
        return this.reqService.get(url, params);
      },
      getAll: (params?: IHttpOptions) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience/list`;
        return this.reqService.get(url, params);
      },
      get: (id: string = '') => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience/${id ? id + '/show' : 'create'}`;
        return this.reqService.get(url);
      },
      update: (id: string, data: ContentData) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience/${id}/update`;
        return this.reqService.patch(url, data);
      },
      create: (data: ContentData) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience/create`;
        return this.reqService.post(url, data);
      },
      delete: (id: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience/${id}/delete`;
        return this.reqService.delete(url, {});
      },
      restore: (id: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience/${id}/restore`;
        return this.reqService.patch(url, {});
      },
      toActive: (id: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience/${id}/to-active`;
        return this.reqService.patch(url, {});
      },
      toInactive: (id: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience/${id}/to-inactive`;
        return this.reqService.patch(url, {});
      },
    },
    experienceOccasion: {
      getAllSource: (params?: IHttpOptions) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/source/gastro/experience/occasion`;
        return this.reqService.get(url, params);
      },
      getAll: (params?: IHttpOptions) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience/occasion/list`;
        return this.reqService.get(url, params);
      },
      get: (id: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience/occasion/${id}`;
        return this.reqService.get(url);
      },
      update: (id: string, data: ContentData, method: 'PATCH' | 'PUT') => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience/occasion/${id}`;
        return method === 'PATCH' ? this.reqService.patch(url, data) : this.reqService.put(url, data);
      },
      create: (data: ContentData, method: 'GET' | 'POST') => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience/occasion/create`;
        return method === 'GET' ? this.reqService.get(url) : this.reqService.post(url, data);
      },
      delete: (id: string, forceDelete = false) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience/occasion/${id}/delete${forceDelete ? '?force=1' : ''}`;
        return this.reqService.delete(url);
      },
      restore: (id: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience/occasion/${id}/restore`;
        return this.reqService.patch(url, {});
      },
      toActive: (id: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience/occasion/${id}/to-active`;
        return this.reqService.post(url, {});
      },
      toInactive: (id: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience/occasion/${id}/to-inactive`;
        return this.reqService.post(url, {});
      },
      export: (id: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/gastro/experience/occasion/${id}/export`;
        return this.reqService.get(url, { responseType: 'blob', observe: 'response' });
      },
    },
    purchases: {
      getAll: (params?: IHttpOptions) => {
        return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/orders/list`, params);
      },
      ticketResend: (purchaseId: string) => {
        return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/orders/${purchaseId}/notification/ticket-resend`);
      },
      ticketDownload: (purchaseId: string) => {
        return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/orders/${purchaseId}/download-ticket`, {
          responseType: 'blob',
          observe: 'response',
        });
      },
      orderStatusList: () => {
        return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/source/orders/status`);
      },
      regenerateInvoice: (purchaseId: string) => {
        return this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/orders/${purchaseId}/regenerate-invoice`);
      },
    },
  };

  topRankingGlossary = {
    wordOfGlossary: {
      getAll: (params?: IHttpOptions) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/top-ranking-glossary/word-of-glossary/list`;
        return this.reqService.get(url, params);
      },
      get: (id: string = '') => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/top-ranking-glossary/word-of-glossary/${id?.length ? `${id}/show` : ''}`;
        return this.reqService.get(url);
      },
      form: (id: string = '') => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/top-ranking-glossary/word-of-glossary/${id?.length ? `${id}/show` : 'create'}`;
        return this.reqService.get(url);
      },
      update: (id: string, data: ContentData) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/top-ranking-glossary/word-of-glossary/${id}/update`;
        return this.reqService.patch(url, data);
      },
      create: (data: ContentData) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/top-ranking-glossary/word-of-glossary/create`;
        return this.reqService.post(url, data);
      },
      delete: (id: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/top-ranking-glossary/word-of-glossary/${id}/delete`;
        return this.reqService.delete(url, {});
      },
      restore: (id: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/top-ranking-glossary/word-of-glossary/${id}/restore`;
        return this.reqService.patch(url, {});
      },
    },
  };

  seoMeta = {
    urls: {
      getAll: (params?: IHttpOptions) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/seo-meta-tool/seo-meta-tools`;
        return this.reqService.get(url, params);
      },
      get: (id: string = '') => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/seo-meta-tool/seo-meta-tool/${id || ''}`;
        return this.reqService.get(url);
      },
      update: (id: string, data: ContentData) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/seo-meta-tool/seo-meta-tool/${id}`;
        return this.reqService.patch(url, data);
      },
      create: (data: ContentData) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/seo-meta-tool/seo-meta-tool`;
        return this.reqService.post(url, data);
      },
      delete: (id: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/seo-meta-tool/seo-meta-tool/${id}/delete`;
        return this.reqService.delete(url, {});
      },
      restore: (id: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/seo-meta-tool/seo-meta-tool/${id}/restore`;
        return this.reqService.put(url, {});
      },
      toActive: (id: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/seo-meta-tool/seo-meta-tool/${id}/to-active`;
        return this.reqService.patch(url, {});
      },
      toInactive: (id: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/seo-meta-tool/seo-meta-tool/${id}/to-inactive`;
        return this.reqService.patch(url, {});
      },
    },
    robots: {
      getAll: (params?: IHttpOptions) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/seo-meta-tool/robots-contents`;
        return this.reqService.get(url, params);
      },
      get: (id: string = '') => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/seo-meta-tool/robots-content/${id || ''}`;
        return this.reqService.get(url);
      },
      update: (id: string, data: ContentData) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/seo-meta-tool/robots-content/${id}`;
        return this.reqService.patch(url, data);
      },
      create: (data: ContentData) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/seo-meta-tool/robots-content`;
        return this.reqService.post(url, data);
      },
      delete: (id: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/seo-meta-tool/robots-content/${id}/delete`;
        return this.reqService.delete(url, {});
      },
      restore: (id: string) => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/seo-meta-tool/robots-content/${id}/restore`;
        return this.reqService.put(url, {});
      },
    },
    category: {
      getSource: () => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/source/content-page/get-seo-research-context/category-selectors`;
        return this.reqService.get(url);
      },
    },
    researchSource: {
      getSource: () => {
        const url = `/api/${this.cmsLang}/${this.contentLang}/source/content-page/get-seo-research-context/research-sources`;
        return this.reqService.get(url);
      },
    },
  };

  readonly extraordinaryNotification = {
    list: (params: IHttpOptions = {}) => this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/extraordinary-notification/list`, params),
    getFormInfo: (id?: string) =>
      this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/extraordinary-notification/${id || 'create'}${id ? '/show' : ''}`),
    create: (data: ContentData) => this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/extraordinary-notification/create`, data),
    update: (id: string, data: ContentData) => this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/extraordinary-notification/${id}/update`, data),
    toActive: (id: string) => this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/extraordinary-notification/${id}/to-active`, {}),
    toInactive: (id: string) => this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/extraordinary-notification/${id}/to-inactive`, {}),
  };

  readonly promotion = {
    list: (params: IHttpOptions = {}) => this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/content-group/promotion/list`, params),
    getFormInfo: (id?: string) => this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/content-group/promotion/${id || 'create'}`),
    create: (data: ContentData) => this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/content-group/promotion/create`, data),
    update: (id: string, data: ContentData) => this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/content-group/promotion/${id}`, data),
    delete: (id: string) => this.reqService.delete(`/api/${this.cmsLang}/${this.contentLang}/content-group/promotion/${id}/delete`),
    restore: (id: string) => this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/content-group/promotion/${id}/restore`, {}),
    toActive: (id: string) => this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/content-group/promotion/${id}/to-active`, {}),
    toInactive: (id: string) => this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/content-group/promotion/${id}/to-inactive`, {}),
    getSource: () => this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/source/promotion/list`),
  };
  // Kalendárium (secret_days_calendar)
  secretDaysCalendar = {
    getAll: (params?: IHttpOptions) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/secret-days-calendar/list`;
      return this.reqService.get(url, params);
    },
    get: (id: string = '') => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/secret-days-calendar/${id ? id + '/show' : 'create'}`;
      return this.reqService.get(url);
    },
    update: (id: string, data: ContentData) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/secret-days-calendar/${id}/update`;
      return this.reqService.patch(url, data);
    },
    create: (data: ContentData, method: 'GET' | 'POST') => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/secret-days-calendar/create`;
      return method === 'GET' ? this.reqService.get(url) : this.reqService.post(url, data);
    },
    delete: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/secret-days-calendar/${id}/delete`;
      return this.reqService.delete(url, {});
    },
    restore: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/secret-days-calendar/${id}/restore`;
      return this.reqService.post(url, {});
    },
    activate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/secret-days-calendar/${id}/to-active`;
      return this.reqService.post(url, {});
    },
    inactivate: (id: string) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/secret-days-calendar/${id}/to-inactive`;
      return this.reqService.post(url, {});
    },
  };

  readonly quizCategories = {
    list: (params: IHttpOptions = {}) => this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/content-group/quiz-category/list`, params),
    getFormInfo: (id?: string) => this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/content-group/quiz-category/${id ? `${id}/show` : 'create'}`),
    create: (data: ContentData) => this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/content-group/quiz-category/create`, data),
    update: (id: string, data: ContentData) => this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/content-group/quiz-category/${id}/update`, data),
    toActive: (id: string) => this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/content-group/quiz-category/${id}/to-active`, {}),
    toInactive: (id: string) => this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/content-group/quiz-category/${id}/to-inactive`, {}),
    delete: (id: string) => this.reqService.delete(`/api/${this.cmsLang}/${this.contentLang}/content-group/quiz-category/${id}/delete`),
    restore: (id: string) => this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/content-group/quiz-category/${id}/restore`, {}),
  };
  readonly doctorAnswer = {
    list: (params: IHttpOptions = {}) => this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/doctor-answer/list`, params),
    getFormInfo: (id?: string) => this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/doctor-answer/${id ? `${id}/details` : 'create'}`),
    create: (data: ContentData) => this.reqService.post(`/api/${this.cmsLang}/${this.contentLang}/doctor-answer/create`, data),
    update: (id: string, data: ContentData) => this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/doctor-answer/${id}/update`, data),
    toActive: (id: string) => this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/doctor-answer/${id}/to-active`, {}),
    toInactive: (id: string) => this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/doctor-answer/${id}/to-inactive`, {}),
    delete: (id: string) => this.reqService.delete(`/api/${this.cmsLang}/${this.contentLang}/doctor-answer/${id}/delete`),
    restore: (id: string) => this.reqService.patch(`/api/${this.cmsLang}/${this.contentLang}/doctor-answer/${id}/restore`, {}),
  };
  //Kalendárium nap
  secretDaysCalendarDay = {
    get: (id: string = '') => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/secret-days-calendar-day/${id + '/show'}`;
      return this.reqService.get(url);
    },
    update: (id: string, data: ContentData) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/secret-days-calendar-day/${id}/update`;
      return this.reqService.patch(url, data);
    },
    /*
     * Method GET returns the form, method POST creates new day
     */
    create: (method: 'GET' | 'POST', data?: ContentData) => {
      const url = `/api/${this.cmsLang}/${this.contentLang}/secret-days-calendar-day/create`;
      return method === 'GET' ? this.reqService.get(url) : this.reqService.post(url, data);
    },
  };

  readonly notifications = {
    list: (params?: Record<string, string | string[]>) =>
      this.reqService.get<ApiListResult<UserNotification>>(`/api/${this.cmsLang}/${this.contentLang}/user-notifications`, { params }),
    actualDashboard: () => this.reqService.get<ApiListResult<UserNotification>>(`/api/${this.cmsLang}/${this.contentLang}/user-notifications/actual`),
    actualInstant: () => this.reqService.get<ApiListResult<UserNotification>>(`/api/${this.cmsLang}/${this.contentLang}/user-notifications/actual-instant`),
    formInfo: (id?: string) => this.reqService.get(`/api/${this.cmsLang}/${this.contentLang}/user-notification/${id ? id + '/edit' : 'create'}`),
    notificationSeen: (id: string, source: 'instant' | 'dashboard') =>
      this.reqService.post<void>(`/api/${this.cmsLang}/${this.contentLang}/user-notification/notice-seen`, {
        notice: id,
        source,
      }),
    create: (data: UserNotification) => this.reqService.post<void>(`/api/${this.cmsLang}/${this.contentLang}/user-notification/create`, data),
    update: (id: string, data: UserNotification) => this.reqService.patch<void>(`/api/${this.cmsLang}/${this.contentLang}/user-notification/${id}/edit`, data),
    activate: (id: string) => this.reqService.patch<void>(`/api/${this.cmsLang}/${this.contentLang}/user-notification/${id}/to-active`, {}),
    inactivate: (id: string) => this.reqService.patch<void>(`/api/${this.cmsLang}/${this.contentLang}/user-notification/${id}/to-inactive`, {}),
  };
}
