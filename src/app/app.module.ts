import { BrowserModule } from '@angular/platform-browser';
import { HTTP_INTERCEPTORS, provideHttpClient, withFetch, withInterceptorsFromDi, withJsonpSupport } from '@angular/common/http';
import { <PERSON>rror<PERSON><PERSON><PERSON>, NgModule } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { AppEnvironment } from '@trendency/kesma-core';
import { environment } from '../environments/environment';
import { AppComponent } from './app.component';
import { appRoutes } from './app.routing';
import { HttpInterceptorImp, HttpModule } from '@external/http';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { IconDefinition } from '@ant-design/icons-angular';
import { NzIconModule } from 'ng-zorro-antd/icon';
import {
  AimOutline,
  AlertOutline,
  AlignLeftOutline,
  AlignCenterOutline,
  AlignRightOutline,
  ApartmentOutline,
  AppstoreOutline,
  AreaChartOutline,
  ArrowDownOutline,
  ArrowLeftOutline,
  ArrowRightOutline,
  ArrowUpOutline,
  AuditOutline,
  BarChartOutline,
  BellOutline,
  BlockOutline,
  BugOutline,
  CalendarFill,
  CalendarOutline,
  CaretDownFill,
  CaretLeftOutline,
  CaretRightOutline,
  CaretUpFill,
  CarOutline,
  CheckCircleTwoTone,
  CloseCircleTwoTone,
  CloudOutline,
  CommentOutline,
  CompressOutline,
  ControlOutline,
  CopyOutline,
  DeleteFill,
  DeleteOutline,
  DeliveredProcedureOutline,
  DesktopOutline,
  DiffOutline,
  DollarOutline,
  DownloadOutline,
  DragOutline,
  EditFill,
  EditOutline,
  EnterOutline,
  EnvironmentOutline,
  ExclamationCircleOutline,
  ExclamationCircleTwoTone,
  EyeFill,
  EyeInvisibleOutline,
  EyeOutline,
  FieldTimeOutline,
  FileAddOutline,
  FileImageFill,
  FileImageOutline,
  FileSearchOutline,
  FileSyncOutline,
  FileTextOutline,
  FileTwoTone,
  FolderOutline,
  FullscreenOutline,
  GlobalOutline,
  GoogleOutline,
  GroupOutline,
  HeartOutline,
  HolderOutline,
  HomeOutline,
  ImportOutline,
  InboxOutline,
  InfoCircleFill,
  InfoCircleOutline,
  IssuesCloseOutline,
  LayoutOutline,
  LikeFill,
  LikeTwoTone,
  LinkOutline,
  LockOutline,
  MenuOutline,
  MinusCircleOutline,
  MinusOutline,
  MinusSquareFill,
  MobileOutline,
  NotificationOutline,
  OrderedListOutline,
  PartitionOutline,
  PictureOutline,
  PictureTwoTone,
  PlaySquareOutline,
  PlusOutline,
  ProfileOutline,
  PushpinFill,
  PushpinOutline,
  QuestionCircleTwoTone,
  ReadOutline,
  ReconciliationOutline,
  RedoOutline,
  ReloadOutline,
  SaveOutline,
  ScissorOutline,
  SearchOutline,
  SelectOutline,
  SendOutline,
  SettingFill,
  SettingOutline,
  ShoppingOutline,
  SnippetsOutline,
  StarOutline,
  SyncOutline,
  TagsOutline,
  TeamOutline,
  ThunderboltFill,
  TranslationOutline,
  UndoOutline,
  UnlockOutline,
  UnorderedListOutline,
  UploadOutline,
  UsergroupAddOutline,
  UserOutline,
  VerticalLeftOutline,
  VideoCameraAddOutline,
  WarningFill,
  WarningTwoTone,
  LoadingOutline,
} from '@ant-design/icons-angular/icons';
import { CoreModule } from '@core/core.module';
import { registerLocaleData } from '@angular/common';
import hu from '@angular/common/locales/hu';
import en from '@angular/common/locales/en';
import { LoadingService } from '@external/loading';
import { NZ_CONFIG, NzConfig } from 'ng-zorro-antd/core/config';
import { TestPagesModule } from './test-pages/test-pages.module';
import { MissingTranslationHandler, TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { createTranslateLoader, MissingTranslationHandlerImp, MissingTranslationService } from '@external/translations/translate-loaders-browser';
import * as Sentry from '@sentry/angular';
import { NZ_DATE_CONFIG } from 'ng-zorro-antd/i18n';
import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { register as registerSwiperElement } from 'swiper/element/bundle';
import { ExtendedTranslateService } from '@core/services/extended-translate.service';

registerLocaleData(hu);
registerLocaleData(en);

const icons: IconDefinition[] = [
  LoadingOutline,
  AlignLeftOutline,
  AlignCenterOutline,
  AlignRightOutline,
  UserOutline,
  CommentOutline,
  LockOutline,
  HomeOutline,
  HeartOutline,
  DownloadOutline,
  MenuOutline,
  FileTextOutline,
  GroupOutline,
  TranslationOutline,
  CarOutline,
  EnvironmentOutline,
  TagsOutline,
  FolderOutline,
  ProfileOutline,
  DeleteFill,
  EditOutline,
  DeleteOutline,
  RedoOutline,
  PlusOutline,
  PushpinFill,
  PushpinOutline,
  EditFill,
  EyeFill,
  CheckCircleTwoTone,
  CloseCircleTwoTone,
  CopyOutline,
  EyeOutline,
  ExclamationCircleTwoTone,
  AuditOutline,
  DeliveredProcedureOutline,
  SendOutline,
  LikeTwoTone,
  LikeFill,
  FileImageFill,
  UploadOutline,
  InboxOutline,
  PictureTwoTone,
  CompressOutline,
  FileTwoTone,
  CalendarOutline,
  InfoCircleOutline,
  HolderOutline,
  EyeInvisibleOutline,
  CaretRightOutline,
  FieldTimeOutline,
  PlaySquareOutline,
  FileAddOutline,
  VideoCameraAddOutline,
  SyncOutline,
  MinusSquareFill,
  CaretDownFill,
  CaretUpFill,
  SettingFill,
  WarningTwoTone,
  DragOutline,
  AlertOutline,
  NotificationOutline,
  SnippetsOutline,
  LayoutOutline,
  ThunderboltFill,
  CaretLeftOutline,
  ReloadOutline,
  AreaChartOutline,
  SearchOutline,
  DiffOutline,
  IssuesCloseOutline,
  MinusOutline,
  ControlOutline,
  DesktopOutline,
  ArrowUpOutline,
  ArrowDownOutline,
  ArrowLeftOutline,
  ArrowRightOutline,
  ReadOutline,
  CalendarFill,
  ExclamationCircleOutline,
  MinusCircleOutline,
  PictureOutline,
  OrderedListOutline,
  UnorderedListOutline,
  SaveOutline,
  AppstoreOutline,
  SettingOutline,
  BarChartOutline,
  TeamOutline,
  ShoppingOutline,
  ApartmentOutline,
  LinkOutline,
  InfoCircleFill,
  WarningFill,
  AppstoreOutline,
  SettingOutline,
  BarChartOutline,
  TeamOutline,
  ShoppingOutline,
  ApartmentOutline,
  LinkOutline,
  InfoCircleFill,
  WarningFill,
  PartitionOutline,
  UnlockOutline,
  BlockOutline,
  MobileOutline,
  DollarOutline,
  SelectOutline,
  GlobalOutline,
  BellOutline,
  CloudOutline,
  UsergroupAddOutline,
  QuestionCircleTwoTone,
  VerticalLeftOutline,
  AreaChartOutline,
  FullscreenOutline,
  FileImageOutline,
  ImportOutline,
  BugOutline,
  EnterOutline,
  FileSearchOutline,
  StarOutline,
  UndoOutline,
  AimOutline,
  ScissorOutline,
  FileSyncOutline,
  GoogleOutline,
  ReconciliationOutline,
];

registerSwiperElement();

const ngZorroConfig: NzConfig = {
  message: {
    nzMaxStack: 10,
  },
};

const SENTRY_PROVIDERS = [
  {
    provide: ErrorHandler,
    useValue: Sentry.createErrorHandler({
      showDialog: false,
    }),
  },
  {
    provide: Sentry.TraceService,
    deps: [Router],
    useValue: undefined,
  },
];

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    HttpModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: createTranslateLoader,
        deps: [],
      },
      missingTranslationHandler: {
        provide: MissingTranslationHandler,
        useClass: MissingTranslationHandlerImp,
        deps: [MissingTranslationService],
      },
      useDefaultLang: false,
    }),
    RouterModule.forRoot(appRoutes, {}),
    NzIconModule.forRoot(icons),
    CoreModule,
    TestPagesModule,
  ],
  providers: [
    provideHttpClient(withInterceptorsFromDi(), withJsonpSupport(), withFetch()),
    {
      provide: NZ_MODAL_DATA,
      useValue: {},
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpInterceptorImp,
      multi: true,
    },
    {
      provide: NZ_CONFIG,
      useValue: ngZorroConfig,
    },
    {
      provide: NZ_DATE_CONFIG,
      useValue: {
        firstDayOfWeek: 1, // week starts on Monday (Sunday is 0)
      },
    },
    {
      provide: AppEnvironment,
      useValue: environment,
    },
    { provide: TranslateService, useClass: ExtendedTranslateService },
    ...SENTRY_PROVIDERS,
  ],
  bootstrap: [AppComponent],
})
export class AppModule {
  constructor(
    private readonly loadingService: LoadingService,
    trace: Sentry.TraceService
  ) {
    this.loadingService.setNavigationProgressBar();
  }
}
